# Repository Guidelines

## Project Structure & Module Organization
- `src/`: application code (Vue 2.7 + Element‑UI).
  - `api/`: request clients and endpoints
  - `components/`: reusable UI (PascalCase `.vue`)
  - `views/`: route views (`views/<area>/<feature>/index.vue`)
  - `router/`, `store/`: routing and Vuex modules
  - `assets/`, `icons/`, `layout/`, `utils/`, `plugins/`, `composables/`
- `public/`: static assets served as‑is
- Config: `.env.*` (env settings), `vue.config.js` (Vue CLI), `unocss.config.js` (utility styles)

## Build, Test, and Development Commands
- `pnpm serve:local`: run dev server with local env (`.env.localhost`)
- `pnpm serve:staging`: run dev server with staging env
- `pnpm build:staging | build:production | build:production.xc`: create builds per environment
- `pnpm lint` / `pnpm lint:fix`: run ESLint (auto‑fix with `:fix`)
- `pnpm format`: Prettier format `src`
- `pnpm icon:build`: regenerate icon components from `src/icons`
- Release helpers: `pnpm publish:*` force‑sync branches; use only in CI/release flows

## Coding Style & Naming Conventions
- Indent: 2 spaces (`.editorconfig`), LF endings
- Prettier: no semicolons, single quotes, width 180 (`.prettierrc`)
- ESLint: Vue + Prettier integration; fix before commit
- Files: components PascalCase (`UserCard.vue`); utilities `camelCase.js`; views under feature folders with `index.vue`

## Testing Guidelines
- No unit test runner is configured in this template.
- Validate changes via local run + targeted component checks; add Jest + `@vue/test-utils` if introducing complex logic.
- Smoke test: ensure app builds for the target env and key routes render.

## Commit & Pull Request Guidelines
- Conventional Commits enforced by commitlint: e.g. `feat: add user filter`, `fix: correct pagination`.
- Hooks: pre‑commit runs lint‑staged (Prettier + ESLint); commit‑msg runs commitlint.
- PRs: include purpose, linked issue, screenshots/GIFs for UI, notes on env changes; ensure `pnpm lint` and a production build pass.

## Security & Configuration Tips
- Configure endpoints/keys via `.env.*` (avoid hard‑coding secrets). Typical pattern: `VUE_APP_*`.
- Sentry is integrated (`@sentry/vue`); gate DSN by environment if enabling.

## 沟通与语言（必读）
- 默认语言：仓库内的 Issue、PR、评审意见与文档沟通一律使用中文（简体）。
- 代码标识符：函数/变量名、API 名称保持英文；UI 文案与代码注释优先中文，必要时可附英文补充。
- 提交信息：遵循 Conventional Commits 英文类型前缀（如 `feat: ...`、`fix: ...`），正文可用中文。
- 回复要求：在本仓库的协作与讨论中，请始终以中文回复。
