const path = require('node:path')
const fs = require('fs-extra')

async function downloadIcons() {
  try {
    const iconSets = {
      // carbon: {
      //   icons: require('@iconify-json/carbon').icons,
      // },
      // ri: {
      //   icons: require('@iconify-json/ri').icons,
      //   customFilter: (item) => item.endsWith('fill'),
      // },
      mingcute: {
        icons: require('@iconify-json/mingcute').icons,
        customFilter: (item) => item.endsWith('fill'),
      },
    }

    for (const [collection, set] of Object.entries(iconSets)) {
      const outputDirectory = path.resolve(process.cwd(), `src/icons/`)

      if (fs.existsSync(outputDirectory) && !fs.lstatSync(outputDirectory).isDirectory()) {
        fs.unlinkSync(outputDirectory)
      }

      await fs.ensureDir(outputDirectory)

      const { icons, customFilter } = set
      const prefix = icons.prefix
      // let iconsList = Object.keys(icons.icons).map((item) => `i-${prefix}-${item}`)
      let iconsList = Object.keys(icons.icons).map((item) => `${prefix}:${item}`)

      if (customFilter) {
        iconsList = iconsList.filter((item) => customFilter(item))
      }

      await fs.writeJSON(path.resolve(outputDirectory, `${collection}.json`), iconsList)
    }
  } catch (error) {
    console.error(error)
  }
}

// eslint-disable-next-line unicorn/prefer-top-level-await
downloadIcons()
