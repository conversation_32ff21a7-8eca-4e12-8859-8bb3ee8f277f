/* eslint-disable unicorn/prefer-module */
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2023: true,
  },
  extends: ['eslint:recommended', 'plugin:vue/recommended', 'plugin:unicorn/recommended', 'plugin:prettier/recommended', './.eslintrc-auto-import.json'],
  parserOptions: {
    parser: '@babel/eslint-parser',
    ecmaVersion: 'latest',
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  rules: {
    'no-debugger': 'error',
    'no-console': 'off',
    'no-unused-vars': 'off',
    'vue/no-unused-vars': 'off',
    'unicorn/throw-new-error': 'off',

    'accessor-pairs': ['error', { enforceForClassMembers: true, setWithoutGet: true }],
    'array-callback-return': 'error',
    'block-scoped-var': 'error',
    'constructor-super': 'error',
    'default-case-last': 'error',
    'dot-notation': ['error', { allowKeywords: true }],
    eqeqeq: ['error', 'smart'],
    'new-cap': ['error', { capIsNew: false, newIsCap: true, properties: true }],
    'no-alert': 'error',
    'no-array-constructor': 'error',
    'no-async-promise-executor': 'error',
    'no-caller': 'error',
    'no-case-declarations': 'error',
    'no-class-assign': 'error',
    'no-compare-neg-zero': 'error',
    'no-cond-assign': ['error', 'always'],
    // 'no-console': ['error', { allow: ['warn', 'error'] }],
    'no-const-assign': 'error',
    'no-control-regex': 'error',
    // 'no-debugger': 'error',
    'no-delete-var': 'error',
    'no-dupe-args': 'error',
    'no-dupe-class-members': 'error',
    'no-dupe-keys': 'error',
    'no-duplicate-case': 'error',
    'no-empty': ['error', { allowEmptyCatch: true }],
    'no-empty-character-class': 'error',
    'no-empty-pattern': 'error',
    'no-eval': 'error',
    'no-ex-assign': 'error',
    'no-extend-native': 'error',
    'no-extra-bind': 'error',
    'no-extra-boolean-cast': 'error',
    'no-fallthrough': 'error',
    'no-func-assign': 'error',
    'no-global-assign': 'error',
    'no-implied-eval': 'error',
    'no-import-assign': 'error',
    'no-invalid-regexp': 'error',
    'no-irregular-whitespace': 'error',
    'no-iterator': 'error',
    'no-labels': ['error', { allowLoop: false, allowSwitch: false }],
    'no-lone-blocks': 'error',
    'no-loss-of-precision': 'error',
    'no-misleading-character-class': 'error',
    'no-multi-str': 'error',
    // 'no-new': 'error',
    'no-new-func': 'error',
    'no-new-symbol': 'error',
    'no-new-wrappers': 'error',
    'no-obj-calls': 'error',
    'no-octal': 'error',
    'no-proto': 'error',
    'no-prototype-builtins': 'error',
    'no-redeclare': ['error', { builtinGlobals: false }],
    'no-regex-spaces': 'error',
    'no-restricted-globals': ['error', { message: 'Use `globalThis` instead.', name: 'global' }, { message: 'Use `globalThis` instead.', name: 'self' }],
    'no-restricted-properties': [
      'error',
      { message: 'Use `Object.getPrototypeOf` or `Object.setPrototypeOf` instead.', property: '__proto__' },
      { message: 'Use `Object.defineProperty` instead.', property: '__defineGetter__' },
      { message: 'Use `Object.defineProperty` instead.', property: '__defineSetter__' },
      { message: 'Use `Object.getOwnPropertyDescriptor` instead.', property: '__lookupGetter__' },
      { message: 'Use `Object.getOwnPropertyDescriptor` instead.', property: '__lookupSetter__' },
    ],
    'no-restricted-syntax': ['error', 'DebuggerStatement', 'LabeledStatement', 'WithStatement', 'TSEnumDeclaration[const=true]', 'TSExportAssignment'],
    'no-self-assign': ['error', { props: true }],
    'no-self-compare': 'error',
    'no-sequences': 'error',
    'no-shadow-restricted-names': 'error',
    'no-sparse-arrays': 'error',
    'no-template-curly-in-string': 'error',
    'no-this-before-super': 'error',
    'no-throw-literal': 'error',
    'no-undef': 'error',
    'no-undef-init': 'error',
    'no-unexpected-multiline': 'error',
    'no-unmodified-loop-condition': 'error',
    'no-unneeded-ternary': ['error', { defaultAssignment: false }],
    'no-unreachable': 'error',
    'no-unreachable-loop': 'error',
    'no-unsafe-finally': 'error',
    'no-unsafe-negation': 'error',
    'no-unused-expressions': [
      'error',
      {
        allowShortCircuit: true,
        allowTaggedTemplates: true,
        allowTernary: true,
      },
    ],
    'no-use-before-define': ['error', { classes: false, functions: false, variables: true }],
    'no-useless-backreference': 'error',
    'no-useless-call': 'error',
    'no-useless-catch': 'error',
    'no-useless-computed-key': 'error',
    'no-useless-constructor': 'error',
    'no-useless-rename': 'error',
    'no-useless-return': 'error',
    'no-var': 'error',
    'no-with': 'error',
    'object-shorthand': [
      'error',
      'always',
      {
        avoidQuotes: true,
        ignoreConstructors: false,
      },
    ],
    'one-var': ['error', { initialized: 'never' }],
    'prefer-arrow-callback': [
      'error',
      {
        allowNamedFunctions: false,
        allowUnboundThis: true,
      },
    ],
    'prefer-const': [
      'error',
      {
        destructuring: 'all',
        ignoreReadBeforeAssign: true,
      },
    ],
    'prefer-exponentiation-operator': 'error',
    'prefer-promise-reject-errors': 'error',
    'prefer-regex-literals': ['error', { disallowRedundantWrapping: true }],
    'prefer-rest-params': 'error',
    'prefer-spread': 'error',
    'prefer-template': 'error',
    'sort-imports': [
      'error',
      {
        allowSeparatedGroups: false,
        ignoreCase: false,
        ignoreDeclarationSort: true,
        ignoreMemberSort: false,
        memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'],
      },
    ],

    'symbol-description': 'error',
    'unicode-bom': ['error', 'never'],
    // 'unused-imports/no-unused-imports': isInEditor ? 'off' : 'error',
    // 'unused-imports/no-unused-vars': ['error', { args: 'after-used', argsIgnorePattern: '^_', vars: 'all', varsIgnorePattern: '^_' }],
    'use-isnan': ['error', { enforceForIndexOf: true, enforceForSwitchCase: true }],
    'valid-typeof': ['error', { requireStringLiterals: true }],
    'vars-on-top': 'error',
    yoda: ['error', 'never'],

    'node/prefer-global/process': 'off',
    'vue/block-order': [
      'error',
      {
        order: ['template', 'script', 'style'],
      },
    ],

    'vue/component-name-in-template-casing': ['error', 'PascalCase', { registeredComponentsOnly: false, ignores: ['template'] }],
    'vue/component-options-name-casing': ['error', 'PascalCase'],
    'vue/component-tags-order': 'off', // this is deprecated
    'vue/custom-event-name-casing': ['error', 'kebab-case'],
    'vue/define-macros-order': [
      'error',
      {
        order: ['defineOptions', 'defineProps', 'defineEmits', 'defineSlots'],
      },
    ],
    'vue/dot-location': ['error', 'property'],
    'vue/dot-notation': ['error', { allowKeywords: true }],
    'vue/eqeqeq': ['error', 'smart'],
    'vue/html-quotes': ['error', 'double'],
    'vue/max-attributes-per-line': 'off',
    'vue/multi-word-component-names': 'off',
    'vue/no-dupe-keys': 'off',
    'vue/no-empty-pattern': 'error',
    'vue/no-irregular-whitespace': 'error',
    'vue/no-loss-of-precision': 'error',
    'vue/no-restricted-syntax': ['error', 'DebuggerStatement', 'LabeledStatement', 'WithStatement'],
    'vue/no-restricted-v-bind': ['error', '/^v-/'],
    'vue/no-setup-props-reactivity-loss': 'off',
    'vue/no-sparse-arrays': 'error',
    // 'vue/no-unused-refs': 'error',
    'vue/no-useless-v-bind': 'error',
    'vue/no-v-html': 'off',
    'vue/object-shorthand': [
      'error',
      'always',
      {
        avoidQuotes: true,
        ignoreConstructors: false,
      },
    ],
    'vue/prefer-separate-static-class': 'error',
    'vue/prefer-template': 'error',
    'vue/prop-name-casing': ['error', 'camelCase'],
    'vue/require-default-prop': 'off',
    'vue/require-prop-types': 'off',
    'vue/space-infix-ops': 'error',
    'vue/space-unary-ops': ['error', { nonwords: false, words: true }],
    'vue/array-bracket-spacing': ['error', 'never'],
    'vue/arrow-spacing': ['error', { after: true, before: true }],
    'vue/block-spacing': ['error', 'always'],
    'vue/block-tag-newline': [
      'error',
      {
        multiline: 'always',
        singleline: 'always',
      },
    ],
    'vue/brace-style': ['error', 'stroustrup', { allowSingleLine: true }],
    'vue/comma-dangle': ['error', 'always-multiline'],
    'vue/comma-spacing': ['error', { after: true, before: false }],
    'vue/comma-style': ['error', 'last'],
    'vue/html-comment-content-spacing': [
      'error',
      'always',
      {
        exceptions: ['-'],
      },
    ],
    'vue/key-spacing': ['error', { afterColon: true, beforeColon: false }],
    'vue/keyword-spacing': ['error', { after: true, before: true }],
    'vue/object-curly-newline': 'off',
    'vue/object-curly-spacing': ['error', 'always'],
    'vue/object-property-newline': ['error', { allowMultiplePropertiesPerLine: true }],
    'vue/operator-linebreak': ['error', 'before'],

    'vue/no-unused-components': 'error',
    'vue/padding-line-between-blocks': ['error', 'always'],
    // 'vue/quote-props': ['error', 'consistent-as-needed'],
    'vue/space-in-parens': ['error', 'never'],
    'vue/template-curly-spacing': 'error',
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/no-empty-component-block': 'error',
    'vue/static-class-names-order': 'error',
    'vue/no-static-inline-styles': ['error', { allowBinding: true }],
    'vue/attributes-order': [
      'error',
      {
        order: [
          'DEFINITION',
          'LIST_RENDERING',
          'CONDITIONALS',
          'RENDER_MODIFIERS',
          'GLOBAL',
          ['UNIQUE', 'SLOT'],
          'TWO_WAY_BINDING',
          'OTHER_DIRECTIVES',
          'OTHER_ATTR',
          'EVENTS',
          'CONTENT',
        ],
        alphabetical: true,
      },
    ],
    'unicorn/filename-case': ['error', { case: 'kebabCase', ignore: ['App.vue', '.js'] }],
  },
  overrides: [
    {
      files: ['src/components/**/*.vue', 'src/views/**/components/**/*.vue', 'src/layout/**/components/**/*.vue'],
      rules: {
        'unicorn/filename-case': [
          'error',
          {
            cases: {
              pascalCase: true,
            },
          },
        ],
      },
    },
  ],
}
