## 准备

### 配置 git

```bash
# 换行符为 LF
git config --global core.autocrlf input

# 默认分支为 main
git config --global init.defaultBranch main

# 自动关联远程分支
git config --global push.autoSetupRemote true
```

### 安装 pnpm

```bash
npm i -g pnpm
```

### 调整项目配置

使用此模板时，请尝试按照清单正确更新项目的信息

- [ ] 在 `.env` 中改变项目名称 `VITE_TITLE`
- [ ] 在 `.env` 中改变项目路径 `BASE_URL`
- [ ] 在 `.env` 中改变项目标志 `VITE_SLUG`
- [ ] 在 `.env` 中改变项目 API `VITE_BASE_API`
- [ ] 在 `.env` 中改变后端启动地址 `VITE_BASE_SERVER`
- [ ] 在 `.env.local` 中改变后端启动地址 `DEV_SERVER_PROXY_TARGET`
- [ ] 在 `.env.development` 中改变后端启动地址 `DEV_SERVER_PROXY_TARGET`
- [ ] 在 `src/assets/logo` 目录下改变 `logo.png`
- [ ] 在 `public` 目录下改变 `favicon.ico`

## 开发

```bash
# 安装项目依赖
pnpm i

# 启动服务（使用本地开启的 java 服务，需先启动后端项目）
pnpm run serve:local

# 启动服务（使用测试环境远程服务）
pnpm run serve:staging

# 启动服务（使用正式环境远程服务）
pnpm run serve:production
```

## 调试

源代码查看工具默认使用 `vscode`，如需使用其他 IDE，请在项目根路径下新建 `.env.local` 文件。
可选项：

```sh
touch .env.local
```

```perl
# 开启调试插件
CODE_INSPECTOR = true
# 指定 IDE 为 webstorm
CODE_EDITOR = webstorm
```

### 支持自动识别的 IDE

支持自动识别的 IDE 及对应的 IDE 编码名称如下表：

<table>
    <tr>
        <th>IDE</th>
        <th>IDE 编码名称</th>
    </tr>
    <tr>
        <td>Visual Studio Code (vscode)</td>
        <td>code</td>
    </tr>
    <tr>
        <td>Visual Studio Code - Insiders（vscode 内部版）</td>
        <td>code_insiders</td>
    </tr>
    <tr>
        <td>WebStorm</td>
        <td>webstorm</td>
    </tr>
    <tr>
        <td>Atom</td>
        <td>atom</td>
    </tr>
    <tr>
        <td>HBuilderX</td>
        <td>hbuilder</td>
    </tr>
    <tr>
        <td>PhpStorm</td>
        <td>phpstorm</td>
    </tr>
    <tr>
        <td>Pycharm</td>
        <td>pycharm</td>
    </tr>
    <tr>
        <td>IntelliJ IDEA</td>
        <td>idea</td>
    </tr>
</table>

## 发布

```bash
# 发布测试环境
pnpm run publish:staging

# 发布生产环境
pnpm run publish:production

# 同时发布测试环境和生产环境
pnpm run publish:all
```

## git commit message 规范 [@commitlint/config-conventional](https://github.com/conventional-changelog/commitlint/tree/master/%40commitlint/config-conventional)

提交 commit 的类型，包括以下几种

- build: 构建相关
- chore: 修改工具相关（包括但不限于文档、代码生成等）
- ci: 持续集成
- docs: 修改文档
- feat: 新功能
- fix: 修复问题
- perf: 提升性能
- refactor: 重构代码，理论上不影响现有功能
- revert: 回退代码
- style: 修改代码格式，不影响代码逻辑
- test: 增加修改测试用例
