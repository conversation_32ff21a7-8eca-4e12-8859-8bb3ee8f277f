{"name": "template-demo", "version": "3.8.7", "author": "gxxj", "packageManager": "pnpm@10.17.0", "scripts": {"serve:local": "vite --mode localhost", "serve:staging": "vite --mode development", "publish:staging": "git stash && git checkout main && git pull && git reset --hard origin/dev && git push -f && git checkout dev", "publish:production": "git stash && git checkout release && git pull && git reset --hard origin/dev && git push -f && git checkout dev", "publish:production.xc": "git stash && git checkout xc && git pull && git reset --hard origin/dev && git push -f && git checkout dev", "publish:all": "pnpm run publish:staging && pnpm run publish:production", "lint": "eslint src --ext .js,.vue", "lint:fix": "eslint src --ext .js,.vue --fix", "format": "prettier --write src", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "build:production.xc": "vite build --mode production.xc", "preview": "vite preview", "up": "npx taze minor -I", "postinstall": "npx simple-git-hooks", "upstream": "git remote add upstream git@***********:project-templates/ruoyi-template/ruoyi-ui-admin-v2.git", "icon:build": "node scripts/prepare.js && prettier --write src/icons", "git:remote": "chmod +x ./scripts/switch_git_remote.sh && ./scripts/switch_git_remote.sh"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "commit-msg": "pnpm commitlint --edit ${1}"}, "lint-staged": {"src/**/*.{js,vue,css}": ["prettier --write"], "src/**/*.{js,vue}": ["eslint --fix"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@sentry/vue": "^8.55.0", "@vueuse/core": "9.13.0", "@vueuse/integrations": "9.13.0", "axios": "^1.12.2", "bowser": "^2.12.1", "change-case": "^5.4.4", "core-js": "^3.45.1", "dayjs": "^1.11.18", "default-passive-events": "^4.0.0", "echarts": "^6.0.0", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "js-base64": "^3.7.8", "js-cookie": "^3.0.5", "jsencrypt": "3.3.2", "nprogress": "^0.2.0", "quill": "^2.0.3", "screenfull": "^6.0.2", "sortablejs": "^1.15.6", "v-click-outside": "^3.2.0", "vcrontab": "^0.3.5", "vue": "^2.7.16", "vue-clipboard2": "^0.3.3", "vue-cool-lightbox": "^2.7.5", "vue-cropper": "^0.6.5", "vue-echarts": "^7.0.3", "vue-meta": "^2.4.0", "vue-router": "^3.6.5", "vuex": "^3.6.2", "watermark-plus": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.28.4", "@babel/eslint-parser": "^7.28.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@iconify-json/ep": "^1.2.3", "@iconify-json/lucide": "^1.2.68", "@iconify-json/mingcute": "^1.2.5", "@iconify/vue2": "^2.1.0", "@unocss/reset": "^66.5.1", "@vitejs/plugin-legacy": "^7.2.1", "@vitejs/plugin-vue2": "^2.3.3", "@vitejs/plugin-vue2-jsx": "^1.1.1", "@vue/cli-plugin-babel": "~5.0.9", "code-inspector-plugin": "^1.2.10", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-unicorn": "^56.0.1", "eslint-plugin-vue": "^9.33.0", "lint-staged": "^16.2.0", "prettier": "^3.6.2", "sass": "^1.93.0", "sass-loader": "^16.0.5", "shiki": "^3.13.0", "simple-git-hooks": "^2.13.1", "unocss": "^66.5.1", "unplugin-auto-import": "^20.1.0", "unplugin-turbo-console": "^2.2.0", "unplugin-vue-components": "^29.1.0", "vite": "^7.1.7"}, "browserslist": {"production": ["defaults"], "development": ["last 2 version"]}}