{
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "cSpell.allowCompoundWords": true,
  "cSpell.language": "en,en-US",
  "cSpell.words": [
    "echarts",
    "gxxj",
    "highlightjs",
    "jsencrypt",
    "pnpm",
    "riophae",
    "ruoyi",
    "Umami",
    "unocss",
    "unplugin",
    "vitest",
    "vuedraggable",
    "vueuse",
    "vuex",
    "wfdd",
    "windicss"
  ],
  "editor.bracketPairColorization.enabled": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "never",
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnPaste": true,
  "editor.formatOnSave": true,
  "editor.glyphMargin": true,
  "editor.gotoLocation.multipleDeclarations": "goto",
  "editor.gotoLocation.multipleDefinitions": "goto",
  "editor.gotoLocation.multipleImplementations": "goto",
  "editor.gotoLocation.multipleReferences": "goto",
  "editor.gotoLocation.multipleTypeDefinitions": "goto",
  "editor.guides.bracketPairs": "active",
  "editor.inlineSuggest.enabled": true,
  "editor.rulers": [
    180
  ],
  "editor.tabCompletion": "on",
  "editor.tabSize": 2,
  "eslint.quiet": true,
  "files.autoGuessEncoding": true,
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "git.autoStash": true,
  "git.autofetch": "all",
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  "git.fetchOnPull": true,
  "git.ignoreLegacyWarning": true,
  "git.untrackedChanges": "separate",
  "javascript.suggest.autoImports": true,
  "javascript.updateImportsOnFileMove.enabled": "always",
  "npm.packageManager": "pnpm",
  "prettier.printWidth": 180,
  "prettier.semi": false,
  "prettier.singleQuote": true,
  // "prettier.trailingComma": "es5",
  "scm.diffDecorationsGutterWidth": 2,
  "search.exclude": {
    "**/.git": true,
    "**/.github": true,
    "**/.nuxt": true,
    "**/.output": true,
    "**/.pnpm": true,
    "**/.vscode": true,
    "**/.yarn": true,
    "**/bower_components": true,
    "**/dist/**": true,
    "**/logs": true,
    "**/node_modules": true,
    "**/out/**": true,
    "**/package-lock.json": true,
    "**/pnpm-lock.yaml": true,
    "**/tmp": true,
    "**/yarn.lock": true
  },
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "vetur.format.defaultFormatterOptions": {
    "prettier": {
      "printWidth": 180,
      "semi": false,
      "singleQuote": true,
      "trailingComma": "es5"
    }
  },
  "vetur.format.enable": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false,
  "vetur.validation.template": false,
  "vue.codeActions.enabled": false
}
