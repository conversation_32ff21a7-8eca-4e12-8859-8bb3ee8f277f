{
  // See https://go.microsoft.com/fwlink/?LinkId=827846 to learn about workspace recommendations.
  // Extension identifier format: ${publisher}.${name}. Example: vscode.csharp
  // List of extensions which should be recommended for users of this workspace.
  "recommendations": [
    "formulahendry.auto-close-tag",
    "formulahendry.auto-rename-tag",
    "editorconfig.editorconfig",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "vue.volar",
    "sdras.vue-vscode-snippets",
    "streetsidesoftware.code-spell-checker",
    "esbenp.prettier-vscode",
    "formulahendry.auto-close-tag",
    "formulahendry.auto-rename-tag",
    "capaj.vscode-standardjs-snippets",
    "visualstudioexptteam.vscodeintellicode",
    "usernamehw.errorlens",
    "eamodio.gitlens",
    "naumovs.color-highlight",
    "jock.svg",
    "atdow.vue-jumper",
    "gxl.git-graph-3"
  ],
  // List of extensions recommended by VS Code that should not be recommended for users of this workspace.
  "unwantedRecommendations": []
}
