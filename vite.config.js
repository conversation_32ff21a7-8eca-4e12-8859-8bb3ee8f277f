import path from 'node:path'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue2'
import vueJsx from '@vitejs/plugin-vue2-jsx'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Unocss from 'unocss/vite'
import { CodeInspectorPlugin } from 'code-inspector-plugin'

export default defineConfig(({ command, mode }) => {
  const environment = loadEnv(mode, process.cwd(), '')
  // console.log(process.env)
  return {
    base: environment.VITE_BASE_URL,

    build: {
      outDir: 'dist',
    },

    define: {
      'process.env': JSON.stringify({
        NODE_ENV: process.env.NODE_ENV || 'development',
        ...Object.fromEntries(Object.entries(environment).filter(([key]) => key.startsWith('VITE_'))),
      }),
    },

    resolve: {
      alias: [
        {
          find: /^~(.*)$/,
          replacement: '$1',
        },
        {
          find: /^@\//,
          // eslint-disable-next-line unicorn/prefer-module
          replacement: `${path.resolve(__dirname, 'src')}/`,
        },
        {
          find: 'path',
          replacement: 'path-browserify',
        },
        {
          find: 'vue',
          replacement: 'vue/dist/vue.esm.js',
        },
      ],
      // alias: {
      //   '~/': `${path.resolve(__dirname, 'src')}/`,
      //   '@/': `${path.resolve(__dirname, 'src')}/`,
      //   path: 'rollup-plugin-node-polyfills/polyfills/path',
      // },
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 忽略 element ui 内部警告
          silenceDeprecations: ['import', 'global-builtin', 'slash-div', 'function-units'],
          api: 'modern-compiler',
        },
      },
    },
    plugins: [
      vue(),
      vueJsx(),
      AutoImport({
        dts: true,
        dirs: ['src/composables', 'src/utils', 'src/api/**/*.js'],
        imports: ['@vueuse/core', 'vue', 'vue-router', 'vuex'],
        vueTemplate: true,
        eslintrc: {
          enabled: true,
        },
      }),
      Components({
        dts: true,
        dirs: ['src/components', 'src/views/**/components', 'src/layout/**/components'],
        extensions: ['vue'],
      }),
      Unocss(),
      ...(environment.VITE_CODE_DEBUG === 'true' ? [CodeInspectorPlugin({ bundler: 'vite', enforcePre: true })] : []),
    ],

    server: {
      open: false,
      host: '0.0.0.0',
      port: environment.VITE_SERVER_PORT,
      strictPort: false,
      https: false,
      proxy: {
        '/api': {
          target: environment.VITE_SERVER_PROXY_TARGET,
          changeOrigin: true,
        },
      },
    },

    esbuild: {
      pure: ['console.log', 'console.warn', 'console.info', 'console.table', 'console.dir'],
      drop: ['debugger'],
      legalComments: 'none',
    },
  }
})
