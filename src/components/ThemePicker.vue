<template>
  <ElColorPicker
    v-model="theme"
    class="theme-picker"
    popper-class="theme-picker-dropdown"
    :predefine="['#3b82f6', '#1890ff', '#304156', '#212121', '#11a983', '#13c2c2', '#6959CD', '#f5222d']"
  />
</template>

<script>
// eslint-disable-next-line unicorn/prefer-module
// const { version } = require('element-ui/package.json')
const ORIGINAL_THEME = '#3b82f6'
const version = '2.15.14'

export default {
  data() {
    return {
      chalk: '', // content of theme-chalk css
      theme: '',
    }
  },
  computed: {
    defaultTheme() {
      return this.$store.state.settings.theme
    },
  },
  watch: {
    defaultTheme: {
      handler(value, oldValue) {
        this.theme = value
      },
      immediate: true,
    },
    async theme(value) {
      await this.setTheme(value)
    },
  },
  created() {
    // if (this.defaultTheme !== ORIGINAL_THEME) this.setTheme(this.defaultTheme)
  },

  methods: {
    async setTheme(value) {
      const oldValue = this.chalk ? this.theme : ORIGINAL_THEME
      if (typeof value !== 'string') return
      const themeCluster = this.getThemeCluster(value.replace('#', ''))
      const originalCluster = this.getThemeCluster(oldValue.replace('#', ''))

      const getHandler = (variable, id) => {
        return () => {
          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))
          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)

          let styleTag = document.querySelector(`#${id}`)
          if (!styleTag) {
            styleTag = document.createElement('style')
            styleTag.setAttribute('id', id)
            document.head.append(styleTag)
          }
          styleTag.textContent = newStyle
        }
      }

      if (!this.chalk) {
        const url = `https://unpkg.com/element-ui@${version}/lib/theme-chalk/index.css`
        await this.getCSSString(url, 'chalk')
      }

      const chalkHandler = getHandler('chalk', 'chalk-style')

      chalkHandler()

      const styles = Array.prototype.slice.call(document.querySelectorAll('style')).filter((style) => {
        const text = style.textContent
        return new RegExp(oldValue, 'i').test(text) && !/Chalk Variables/.test(text)
      })
      for (const style of styles) {
        const { textContent } = style
        if (typeof textContent !== 'string') continue
        style.textContent = this.updateStyle(textContent, originalCluster, themeCluster)
      }

      this.$emit('change', value)
    },

    updateStyle(style, oldCluster, newCluster) {
      let newStyle = style
      for (const [index, color] of oldCluster.entries()) newStyle = newStyle.replaceAll(new RegExp(color, 'ig'), newCluster[index])

      return newStyle
    },

    getCSSString(url, variable) {
      return new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.addEventListener('readystatechange', () => {
          if (xhr.readyState === 4 && xhr.status === 200) {
            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')
            resolve()
          }
        })
        xhr.open('GET', url)
        xhr.send()
      })
    },

    getThemeCluster(theme) {
      // eslint-disable-next-line unicorn/consistent-function-scoping
      const tintColor = (color, tint) => {
        let red = Number.parseInt(color.slice(0, 2), 16)
        let green = Number.parseInt(color.slice(2, 4), 16)
        let blue = Number.parseInt(color.slice(4, 6), 16)

        if (tint === 0) {
          // when primary color is in its rgb space
          return [red, green, blue].join(',')
        } else {
          red += Math.round(tint * (255 - red))
          green += Math.round(tint * (255 - green))
          blue += Math.round(tint * (255 - blue))

          red = red.toString(16)
          green = green.toString(16)
          blue = blue.toString(16)

          return `#${red}${green}${blue}`
        }
      }

      // eslint-disable-next-line unicorn/consistent-function-scoping
      const shadeColor = (color, shade) => {
        let red = Number.parseInt(color.slice(0, 2), 16)
        let green = Number.parseInt(color.slice(2, 4), 16)
        let blue = Number.parseInt(color.slice(4, 6), 16)

        red = Math.round((1 - shade) * red)
        green = Math.round((1 - shade) * green)
        blue = Math.round((1 - shade) * blue)

        red = red.toString(16)
        green = green.toString(16)
        blue = blue.toString(16)

        return `#${red}${green}${blue}`
      }

      const clusters = [theme]
      for (let index = 0; index <= 9; index++) clusters.push(tintColor(theme, Number((index / 10).toFixed(2))))

      clusters.push(shadeColor(theme, 0.1))
      return clusters
    },
  },
}
</script>

<style lang="scss" scoped>
.theme-message,
.theme-picker-dropdown {
  z-index: 99999 !important;
}

.theme-picker .el-color-picker__trigger {
  height: 26px !important;
  width: 26px !important;
  padding: 2px;
}

.theme-picker-dropdown .el-color-dropdown__link-btn {
  display: none;
}
</style>
