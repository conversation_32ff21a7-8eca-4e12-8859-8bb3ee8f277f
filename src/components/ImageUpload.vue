<template>
  <div class="component-upload-image">
    <ElUpload
      ref="imageUpload"
      :action="uploadImgUrl"
      :before-upload="handleBeforeUpload"
      :class="{ hide: fileList.length >= limit }"
      :file-list="fileList"
      :headers="headers"
      :limit="limit"
      list-type="picture-card"
      multiple
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleDelete"
      :on-success="handleUploadSuccess"
      :show-file-list="true"
    >
      <i class="el-icon-plus"></i>
    </ElUpload>

    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b class="text-red-500">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b class="text-red-500">{{ fileType.join('/') }}</b>
      </template>
      的文件
    </div>

    <ElDialog append-to-body :close-on-click-modal="false" title="预览" :visible.sync="dialogVisible" width="800">
      <img class="block max-w-full mx-auto" :src="dialogImageUrl" />
    </ElDialog>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Object, Array],
      default: undefined,
    },
    // 图片数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制 (MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型，例如 ['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg'],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload: false,
      baseUrl: useEnvironment.VITE_BASE_API,
      uploadImgUrl: `${useEnvironment.VITE_BASE_API}/common/upload`, // 上传的图片服务器地址
      headers: {
        Authorization: `Bearer ${useMyCookies.get('token')}`,
      },
      fileList: [],
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    },
  },
  watch: {
    value: {
      handler(value) {
        if (value) {
          // 首先将值转为数组
          const list = Array.isArray(value) ? value : this.value.split(',')
          // 然后将数组转为对象数组
          this.fileList = list.map((item) => {
            if (typeof item === 'string') item = item.includes(this.baseUrl) ? { name: item, url: item } : { name: this.baseUrl + item, url: this.baseUrl + item }

            return item
          })
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 上传前 loading 加载
    handleBeforeUpload(file) {
      let isImg = false
      if (this.fileType.length > 0) {
        let fileExtension = ''
        if (file.name.includes('.')) fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)

        isImg = this.fileType.some((type) => {
          if (file.type.includes(type)) return true
          if (fileExtension && fileExtension.includes(type)) return true
          return false
        })
      } else {
        isImg = file.type.includes('image')
      }

      if (!isImg) {
        appModal.msgError(`文件格式不正确，请上传${this.fileType.join('/')}图片格式文件!`)
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          appModal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      appModal.loading('正在上传图片，请稍候...')
      this.number++
    },
    // 文件个数超出
    handleExceed() {
      appModal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传成功回调
    handleUploadSuccess(response, file) {
      this.uploadList.push({ name: response.fileName, url: response.fileName })
      this.uploadedSuccessfully()
    },
    // 删除图片
    handleDelete(file) {
      const findIndex = this.fileList.map((f) => f.name).indexOf(file.name)
      if (findIndex !== -1) {
        this.fileList.splice(findIndex, 1)
        this.$emit('input', this.listToString(this.fileList))
      }
    },
    // 上传失败
    handleUploadError() {
      appModal.msgError('上传图片失败，请重试')
      appModal.closeLoading()
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = [...this.fileList, ...this.uploadList]
        this.uploadList = []
        this.number = 0
        this.$emit('input', this.listToString(this.fileList))
        appModal.closeLoading()
      }
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 对象转成指定字符串分隔
    listToString(list, separator = ',') {
      let string_ = ''
      for (const index in list) if (list[index].url) string_ += list[index].url.replace(this.baseUrl, '') + separator

      return string_ === '' ? '' : string_.slice(0, Math.max(0, string_.length - 1))
    },
  },
}
</script>

<style lang="scss" scoped>
// .el-upload--picture-card 控制加号部分
.hide :deep(.el-upload--picture-card) {
  display: none;
}

// 去掉动画效果
:deep(.el-list-enter-active),
:deep(.el-list-leave-active) {
  transition: all 0s;
}

:deep(.el-list-enter),
:deep(.el-list-leave-active) {
  opacity: 0;
  transform: translateY(0);
}
</style>
