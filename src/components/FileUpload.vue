<template>
  <div class="upload-file">
    <ElUpload
      ref="fileUpload"
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      class="upload-file-uploader"
      :file-list="fileList"
      :headers="headers"
      :limit="limit"
      multiple
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
    >
      <!-- 上传按钮 -->
      <ElButton size="mini" type="primary">选取文件</ElButton>
      <!-- 上传提示 -->
      <template #tip>
        <div v-if="showTip" class="el-upload__tip">
          请上传
          <template v-if="fileSize">
            大小不超过 <b class="text-red-500">{{ fileSize }}MB</b>
          </template>
          <template v-if="fileType">
            格式为 <b class="text-red-500">{{ fileType.join('/') }}</b>
          </template>
          的文件
        </div>
      </template>
    </ElUpload>

    <!-- 文件列表 -->
    <TransitionGroup class="el-upload-list el-upload-list--text upload-file-list" name="el-fade-in-linear" tag="ul">
      <li v-for="(file, index) in fileList" :key="file.url" class="el-upload-list__item ele-upload-list__item-content">
        <ElLink :href="`${baseUrl}${file.url}`" target="_blank" :underline="false">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </ElLink>
        <div class="ele-upload-list__item-content-action">
          <ElLink type="danger" :underline="false" @click="handleDelete(index)">删除</ElLink>
        </div>
      </li>
    </TransitionGroup>
  </div>
</template>

<script>
export default {
  props: {
    // 值
    value: {
      type: [String, Object, Array],
      default: undefined,
    },
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制 (MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型，例如 ['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['doc', 'xls', 'ppt', 'txt', 'pdf'],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      baseUrl: useEnvironment.VITE_BASE_API,
      uploadFileUrl: `${useEnvironment.VITE_BASE_API}/common/upload`, // 上传文件服务器地址
      headers: {
        Authorization: `Bearer ${useMyCookies.get('token')}`,
      },
      fileList: [],
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    },
  },
  watch: {
    value: {
      handler(value) {
        if (value) {
          let temporary = 1
          // 首先将值转为数组
          const list = Array.isArray(value) ? value : this.value.split(',')
          // 然后将数组转为对象数组
          this.fileList = list.map((item) => {
            if (typeof item === 'string') item = { name: item, url: item }

            item.uid = item.uid || Date.now() + temporary++
            return item
          })
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        const fileName = file.name.split('.')
        const fileExtension = fileName.at(-1)
        const isTypeOk = this.fileType.includes(fileExtension)
        if (!isTypeOk) {
          appModal.msgError(`文件格式不正确，请上传${this.fileType.join('/')}格式文件!`)
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          appModal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      appModal.loading('正在上传文件，请稍候...')
      this.number++
      return true
    },
    // 文件个数超出
    handleExceed() {
      appModal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传失败
    handleUploadError(error) {
      appModal.msgError('上传文件失败，请重试')
      appModal.closeLoading()
    },
    // 上传成功回调
    handleUploadSuccess(response, file) {
      this.uploadList.push({ name: response.fileName, url: response.fileName })
      this.uploadedSuccessfully()
    },
    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1)
      this.$emit('input', this.listToString(this.fileList))
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = [...this.fileList, ...this.uploadList]
        this.uploadList = []
        this.number = 0
        this.$emit('input', this.listToString(this.fileList))
        appModal.closeLoading()
      }
    },
    // 获取文件名称
    getFileName(name) {
      return name.includes('/') ? name.slice(name.lastIndexOf('/') + 1) : ''
    },
    // 对象转成指定字符串分隔
    listToString(list, separator = ',') {
      let string_ = ''
      for (const index in list) string_ += list[index].url + separator

      return string_ === '' ? '' : string_.slice(0, Math.max(0, string_.length - 1))
    },
  },
}
</script>

<style lang="scss" scoped>
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list {
  :deep(.el-upload-list__item) {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
  }

  :deep(.ele-upload-list__item-content) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }

  :deep(.ele-upload-list__item-content-action .el-link) {
    margin-right: 10px;
  }
}
</style>
