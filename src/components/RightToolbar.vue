<template>
  <div class="float-right relative" :style="style">
    <ElRow>
      <ElTooltip v-if="search" class="item" :content="showSearch ? '隐藏搜索' : '显示搜索'" effect="dark" placement="top">
        <ElButton circle icon="el-icon-search" size="mini" @click="toggleSearch()" />
      </ElTooltip>
      <ElTooltip class="item" content="刷新" effect="dark" placement="top">
        <ElButton circle icon="el-icon-refresh" size="mini" @click="refresh()" />
      </ElTooltip>
      <ElTooltip v-if="columns" class="item" content="显隐列" effect="dark" placement="top">
        <ElButton v-if="showColumnsType === 'transfer'" circle icon="el-icon-menu" size="mini" @click="showColumn()" />
        <ElDropdown v-if="showColumnsType === 'checkbox'" class="pl-3" :hide-on-click="false" trigger="click">
          <ElButton circle icon="el-icon-menu" size="mini" />
          <template #dropdown>
            <ElDropdownMenu>
              <ElDropdownItem v-for="item in columns" :key="item.key">
                <ElCheckbox :checked="item.visible" :label="item.label" @change="checkboxChange($event, item.label)" />
              </ElDropdownItem>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </ElTooltip>
    </ElRow>
    <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open">
      <ElTransfer v-model="value" :data="columns" :titles="['显示', '隐藏']" @change="dataChange"></ElTransfer>
    </ElDialog>
  </div>
</template>

<script>
export default {
  props: {
    // 是否显示检索条件
    showSearch: {
      type: Boolean,
      default: true,
    },
    // 显隐列信息
    columns: {
      type: Array,
      default: () => [],
    },
    // 是否显示检索图标
    search: {
      type: Boolean,
      default: true,
    },
    // 显隐列类型（transfer 穿梭框、checkbox 复选框）
    showColumnsType: {
      type: String,
      default: 'checkbox',
    },
    // 右外边距
    gutter: {
      type: Number,
      default: 10,
    },
  },
  data() {
    return {
      // 显隐数据
      value: [],
      // 弹出层标题
      title: '显示/隐藏',
      // 是否显示弹出层
      open: false,
    }
  },
  computed: {
    style() {
      const returnValue = {}
      if (this.gutter) returnValue.marginRight = `${this.gutter / 2}px`

      return returnValue
    },
  },
  created() {
    if (this.showColumnsType === 'transfer') {
      // 显隐列初始默认隐藏列
      for (const item in this.columns) {
        if (this.columns[item].visible === false) {
          this.value.push(Number.parseInt(item))
        }
      }
    }
  },
  methods: {
    // 搜索
    toggleSearch() {
      this.$emit('update:showSearch', !this.showSearch)
    },
    // 刷新
    refresh() {
      this.$emit('query-table')
    },
    // 右侧列表元素变化
    dataChange(data) {
      for (const item in this.columns) {
        const key = this.columns[item].key
        // eslint-disable-next-line vue/no-mutating-props
        this.columns[item].visible = !data.includes(key)
      }
    },
    // 打开显隐列 dialog
    showColumn() {
      this.open = true
    },
    // 勾选
    checkboxChange(event, label) {
      this.columns.find((item) => item.label === label).visible = event
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-transfer__button) {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}
:deep(.el-transfer__button):first-child {
  margin-bottom: 10px;
}
</style>
