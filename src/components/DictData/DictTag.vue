<template>
  <div>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(item.value)">
        <span
          v-if="(item.raw.listClass === 'default' || item.raw.listClass === '') && (item.raw.cssClass === '' || item.raw.cssClass === null)"
          :key="item.value"
          :class="item.raw.cssClass"
          :index="index"
          >{{ `${item.label} ` }}</span
        >
        <ElTag v-else :key="index" :class="item.raw.cssClass" :disable-transitions="true" :index="index" :type="item.raw.listClass === 'primary' ? '' : item.raw.listClass">
          {{ `${item.label} ` }}
        </ElTag>
      </template>
    </template>
    <template v-if="unMatched && showValue">
      {{ handleArray(UnmatchedArray) }}
    </template>
  </div>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [Number, String, Array, Boolean],
      default: undefined,
    },
    // 当未找到匹配的数据时，显示 value
    showValue: {
      type: Boolean,
      default: true,
    },
    separator: {
      type: String,
      default: ',',
    },
  },
  data() {
    return {
      UnmatchedArray: [], // 记录未匹配的项
    }
  },
  computed: {
    values() {
      if (this.value === null || this.value === undefined || this.value === '') return []

      // 处理布尔值
      if (typeof this.value === 'boolean') {
        return [this.value ? '1' : '0']
      }

      // 处理数字
      if (typeof this.value === 'number') {
        return [String(this.value)]
      }

      return Array.isArray(this.value) ? this.value.map((item) => `${item}`) : String(this.value).split(this.separator)
    },
  },
  created() {
    // 组件创建时的逻辑
  },
  methods: {
    handleArray(array) {
      if (array.length === 0) return ''
      let result = ''
      for (const item of array) {
        result += `${item} `
      }
      return result.trim()
    },
    unMatched() {
      this.UnmatchedArray = []
      // 没有 value 不显示
      if (this.value === null || this.value === undefined || this.value === '' || this.options.length === 0) return false
      // 传入值为数组
      let unMatched = false // 添加一个标志来判断是否有未匹配项
      for (const item of this.values) {
        if (!this.options.some((v) => v.value === item)) {
          this.UnmatchedArray.push(item)
          unMatched = true // 如果有未匹配项，将标志设置为 true
        }
      }
      return unMatched // 返回标志的值
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-tag + .el-tag) {
  margin-left: 10px;
}
</style>
