import Vue from 'vue'
import store from '@/store'
import DataDict from '@/utils/dict'

function searchDictByKey(dict, key) {
  if (!key) return

  try {
    for (const element of dict) if (element.key === key) return element.value
  } catch {}
}

function install() {
  Vue.use(DataDict, {
    meta: {
      '*': {
        labelField: 'dictLabel',
        valueField: 'dictValue',
        request(dictMeta) {
          const storeDict = searchDictByKey(store.getters.dict, dictMeta.type)
          return storeDict
            ? new Promise((resolve) => {
                resolve(storeDict)
              })
            : new Promise((resolve, reject) => {
                useApiGetDictByType(dictMeta.type)
                  .then((response) => {
                    store.dispatch('dict/setDict', { key: dictMeta.type, value: response })
                    resolve(response)
                  })
                  .catch((error) => {
                    reject(error)
                  })
              })
        },
      },
    },
  })
}

export default {
  install,
}
