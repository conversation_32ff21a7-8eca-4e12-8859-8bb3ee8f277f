import Vue from 'vue'

// 字典数据组件
import DictData from '@/components/DictData'
DictData.install()

// 树形组件
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
Vue.component('Treeselect', Treeselect)

// iconify icon
import { Icon, addCollection } from '@iconify/vue2'
Vue.component('Icon', Icon)
// import mingcute from '@iconify-json/mingcute/icons.json'
// addCollection(mingcute)
