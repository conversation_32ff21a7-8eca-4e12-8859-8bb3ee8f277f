<template>
  <div ref="rightPanel" class="right-panel-container">
    <div class="right-panel-background" />
    <div class="right-panel">
      <div class="right-panel-items">
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    clickNotClose: {
      default: false,
      type: Boolean,
    },
  },
  computed: {
    show: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(value) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value,
        })
      },
    },
  },
  watch: {
    show(value) {
      if (value && !this.clickNotClose) this.addEventClick()
    },
  },
  mounted() {
    this.addEventClick()
  },
  beforeDestroy() {
    this.removeEventClick()
    const elx = this.$refs.rightPanel
    if (elx) {
      elx.remove()
    }
  },
  methods: {
    addEventClick() {
      // 避免重复绑定
      this.removeEventClick()
      globalThis.addEventListener('click', this.closeSidebar)
    },
    removeEventClick() {
      globalThis.removeEventListener('click', this.closeSidebar)
    },
    closeSidebar(event_) {
      const parent = event_.target.closest('.el-drawer__body')
      if (!parent) {
        this.show = false
        this.removeEventClick()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.right-panel-background {
  position: fixed;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.7, 0.3, 0.1, 1);
  background: rgba(0, 0, 0, 0.2);
  z-index: -1;
}

.right-panel {
  width: 100%;
  max-width: 260px;
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.05);
  transition: all 0.25s cubic-bezier(0.7, 0.3, 0.1, 1);
  transform: translate(100%);
  background: #fff;
  z-index: 40000;
}

.handle-button {
  width: 48px;
  height: 48px;
  position: absolute;
  left: -48px;
  text-align: center;
  font-size: 24px;
  border-radius: 6px 0 0 6px !important;
  z-index: 0;
  pointer-events: auto;
  cursor: pointer;
  color: #fff;
  line-height: 48px;
  i {
    font-size: 24px;
    line-height: 48px;
  }
}
</style>
