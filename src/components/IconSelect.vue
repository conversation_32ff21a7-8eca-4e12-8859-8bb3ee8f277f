<template>
  <div>
    <ElInput v-model="name" class="relative" clearable placeholder="请输入图标名称" @clear="filterIcons" @input="filterIcons">
      <template #suffix> <i class="el-icon-search el-input__icon" /></template>
    </ElInput>
    <div class="content-start flex flex-wrap h-75 overflow-y-scroll">
      <span v-for="(item, index) in visibleIcons" :key="index" class="cursor-pointer hover:text-blue-500 p-2" @click="selectedIcon(item)">
        <!-- <i :class="item" class="text-3xl"></i> -->
        <Icon class="text-3xl" :icon="item" />
      </span>
    </div>
    <div class="pt-4 px-10 text-center">
      <ElButton size="mini" @click="loadMore">加载更多 ({{ loadedCount }})</ElButton>
      <ElButton size="mini" @click="loadAll">加载全部 ({{ totalCount }})</ElButton>
    </div>
  </div>
</template>

<script>
import icons from '@/icons/mingcute'

const ITEMS_PER_LOAD = 200

export default {
  data() {
    return {
      totalCount: icons.length,
      iconList: icons,
      name: '',
      visibleIcons: [], // Displayed icons
      loadedCount: 0, // Number of icons loaded
    }
  },
  mounted() {
    this.loadMore()
  },
  methods: {
    filterIcons() {
      this.visibleIcons = this.iconList
      if (this.name) {
        this.visibleIcons = this.visibleIcons.filter((item) => item.includes(this.name))
      }
    },
    selectedIcon(name) {
      this.$emit('selected', name)
      document.body.click()
    },
    loadMore() {
      const end = this.loadedCount + ITEMS_PER_LOAD
      this.visibleIcons = this.iconList.slice(0, end)
      this.loadedCount = end
    },
    loadAll() {
      this.visibleIcons = this.iconList
    },
    reset() {
      this.name = ''
      this.visibleIcons = []
      this.loadedCount = 0
      this.loadMore()
    },
  },
}
</script>
