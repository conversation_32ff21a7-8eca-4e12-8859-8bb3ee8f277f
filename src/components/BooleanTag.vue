<template>
  <ElTag effect="plain" size="mini" :type="value ? 'success' : 'danger'">
    {{ label }}
  </ElTag>
</template>

<script>
export default {
  props: {
    value: {
      type: [Boolean, Number, String],
      required: true,
    },
    trueLabel: {
      type: String,
      default: '是',
    },
    falseLabel: {
      type: String,
      default: '否',
    },
    enumKey: {
      type: String,
      default: '',
    },
    options: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    label() {
      if (this.options && this.options.length > 0) {
        const option = this.options.find((opt) => opt.value === this.value || opt.value === String(this.value))
        return option ? option.label : this.value
      }

      if (this.enumKey) {
        return enumLabel(this.enumKey, this.value)
      }

      return this.value ? this.trueLabel : this.falseLabel
    },
  },
}
</script>
