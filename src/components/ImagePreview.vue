<template>
  <ElImage fit="cover" :preview-src-list="realSrcList" :src="`${realSrc}`" :style="`width:${realWidth};height:${realHeight};`">
    <template #error>
      <i class="el-icon-picture-outline"></i>
    </template>
  </ElImage>
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      default: '',
    },
    width: {
      type: [Number, String],
      default: '',
    },
    height: {
      type: [Number, String],
      default: '',
    },
  },
  computed: {
    realSrc() {
      if (!this.src) return

      const real_source = this.src.split(',')[0]
      if (isExternal(real_source)) return real_source

      return useEnvironment.VITE_BASE_API + real_source
    },
    realSrcList() {
      if (!this.src) return

      const real_source_list = this.src.split(',')
      const sourceList = []
      for (const item of real_source_list) {
        if (isExternal(item)) {
          sourceList.push(item)
          continue
        }
        sourceList.push(useEnvironment.VITE_BASE_API + item)
        continue
      }
      return sourceList
    },
    realWidth() {
      return typeof this.width === 'string' ? this.width : `${this.width}px`
    },
    realHeight() {
      return typeof this.height === 'string' ? this.height : `${this.height}px`
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-image) {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;
  :deep(.el-image__inner) {
    transition: all 0.3s;
    cursor: pointer;
    &:hover {
      transform: scale(1.2);
    }
  }
  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>
