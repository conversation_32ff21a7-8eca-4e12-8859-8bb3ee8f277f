<template>
  <div class="bg-white flex flex-col items-center justify-center mt-10 p-4">
    <div v-for="(account, index) in accounts" :key="index" class="py-1">
      <ElButton v-for="(item, i) in account.items" :key="i" plain size="mini" :type="account.type" @click="quicklyLogin(item)">{{ item.nickname }}</ElButton>
    </div>
  </div>
</template>

<script>
import accounts from '@/data/accounts'

export default {
  computed: {
    accounts() {
      return accounts
    },
  },
  methods: {
    async quicklyLogin(parameters) {
      await this.$store.dispatch('Login', { ...parameters, password: useCrypto.encrypt(parameters.password) })
      this.$router.push({ name: 'Enter' })
    },
  },
}
</script>
