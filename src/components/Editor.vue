<template>
  <div>
    <ElUpload
      v-if="type === 'url'"
      ref="upload"
      :action="uploadUrl"
      :before-upload="handleBeforeUpload"
      class="hidden"
      :headers="headers"
      name="file"
      :on-error="handleUploadError"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
    >
    </ElUpload>
    <div ref="editor" class="editor" :style="styles"></div>
  </div>
</template>

<script>
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

export default {
  props: {
    // 编辑器的内容
    value: {
      type: String,
      default: '',
    },
    // 高度
    height: {
      type: Number,
      default: undefined,
    },
    // 最小高度
    minHeight: {
      type: Number,
      default: undefined,
    },
    // 只读
    readOnly: {
      type: Boolean,
      default: false,
    },
    // 上传文件大小限制 (MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 类型（base64 格式、url 格式）
    type: {
      type: String,
      default: 'url',
    },
  },
  data() {
    return {
      uploadUrl: `${useEnvironment.VITE_BASE_API}/common/upload`, // 上传的图片服务器地址
      headers: {
        Authorization: `Bearer ${useMyCookies.get('token')}`,
      },
      quillInstance: undefined,
      currentValue: '',
      options: {
        theme: 'snow',
        bounds: document.body,
        debug: 'warn',
        modules: {
          // 工具栏配置
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
            ['blockquote', 'code-block'], // 引用  代码块
            [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
            [{ indent: '-1' }, { indent: '+1' }], // 缩进
            [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            [{ align: [] }], // 对齐方式
            ['clean'], // 清除文本格式
            ['link', 'image', 'video'], // 链接、图片、视频
          ],
        },
        placeholder: '请输入内容',
        readOnly: this.readOnly,
      },
    }
  },
  computed: {
    styles() {
      const style = {}
      if (this.minHeight) {
        style.minHeight = `${this.minHeight}px`
      }
      if (this.height) {
        style.height = `${this.height}px`
      }
      return style
    },
  },
  watch: {
    value: {
      handler(value) {
        if (value !== this.currentValue) {
          this.currentValue = value === null ? '' : value
          if (this.quillInstance) {
            this.setContent(this.currentValue)
          }
        }
      },
      immediate: true,
    },
    readOnly: {
      handler(newValue) {
        if (this.quillInstance) {
          this.quillInstance.disable(newValue)
        }
      },
    },
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.destroy()
  },
  methods: {
    init() {
      const editor = this.$refs.editor
      this.quillInstance = new Quill(editor, this.options)

      // 设置初始内容
      this.setContent(this.currentValue)

      // 如果设置了上传地址则自定义图片上传事件
      if (this.type === 'url') {
        this.setupImageUpload()
      }

      // 绑定事件
      this.bindEvents()
    },

    setContent(content) {
      if (!this.quillInstance) return

      // 使用 dangerouslyPasteHTML 替代 pasteHTML
      if (content) {
        this.quillInstance.root.innerHTML = content
      } else {
        this.quillInstance.setText('')
      }
    },

    setupImageUpload() {
      const toolbar = this.quillInstance.getModule('toolbar')
      toolbar.addHandler('image', (value) => {
        if (value) {
          this.$refs.upload.$children[0].$refs.input.click()
        } else {
          this.quillInstance.format('image', false)
        }
      })
    },

    bindEvents() {
      // 文本变化事件
      this.quillInstance.on('text-change', (delta, oldDelta, source) => {
        const html = this.quillInstance.root.innerHTML
        const text = this.quillInstance.getText()
        this.currentValue = html
        this.$emit('input', html)
        this.$emit('on-change', { html, text, quill: this.quillInstance })
        this.$emit('on-text-change', delta, oldDelta, source)
      })

      // 选择变化事件
      this.quillInstance.on('selection-change', (range, oldRange, source) => {
        this.$emit('on-selection-change', range, oldRange, source)
      })

      // 编辑器变化事件
      this.quillInstance.on('editor-change', (eventName, ...arguments_) => {
        this.$emit('on-editor-change', eventName, ...arguments_)
      })
    },

    destroy() {
      if (this.quillInstance) {
        // 移除所有事件监听器
        this.quillInstance.off('text-change')
        this.quillInstance.off('selection-change')
        this.quillInstance.off('editor-change')

        // 销毁实例
        this.quillInstance = undefined
      }
    },

    // 对外方法：获取内容
    getContent() {
      return this.quillInstance ? this.quillInstance.root.innerHTML : ''
    },

    // 对外方法：获取纯文本
    getText() {
      return this.quillInstance ? this.quillInstance.getText() : ''
    },

    // 对外方法：设置内容
    setContentExternally(content) {
      this.setContent(content)
    },

    // 对外方法：获取 Delta
    getContents() {
      return this.quillInstance ? this.quillInstance.getContents() : undefined
    },

    // 对外方法：启用/禁用编辑器
    setEnabled(enabled) {
      if (this.quillInstance) {
        this.quillInstance.enable(enabled)
      }
    },

    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml']
      const isJPG = type.includes(file.type)

      // 检验文件格式
      if (!isJPG) {
        this.$message.error('图片格式错误！支持的格式：JPEG、JPG、PNG、SVG')
        return false
      }

      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }

      return true
    },

    handleUploadSuccess(response, file) {
      if (!this.quillInstance) return

      // 获取光标所在位置
      const selection = this.quillInstance.getSelection()
      const length = selection ? selection.index : 0

      // 插入图片
      const imageUrl = useEnvironment.VITE_BASE_API + response.fileName
      this.quillInstance.insertEmbed(length, 'image', imageUrl)

      // 调整光标到最后
      this.quillInstance.setSelection(length + 1, Quill.sources.API)
    },

    handleUploadError() {
      this.$message.error('图片插入失败')
    },
  },
}
</script>

<style lang="scss" scoped>
.editor {
  min-height: 200px;
}

.hidden {
  display: none;
}
</style>
