<template>
  <div v-loading="loading" :style="`height:${height}`">
    <iframe class="h-full w-full" frameborder="no" scrolling="auto" :src="src" />
  </div>
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      height: `${document.documentElement.clientHeight - 94.5}px;`,
      loading: true,
      url: this.src,
    }
  },
  mounted() {
    setTimeout(() => {
      this.loading = false
    }, 100)
    this._onResize = () => {
      this.height = `${document.documentElement.clientHeight - 94.5}px;`
    }
    window.addEventListener('resize', this._onResize)
  },
  beforeDestroy() {
    if (this._onResize) {
      window.removeEventListener('resize', this._onResize)
      this._onResize = undefined
    }
  },
}
</script>
