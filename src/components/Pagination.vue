<template>
  <div class="bg-white px-4 py-2" :class="{ hidden }">
    <ElPagination
      :background="background"
      :current-page.sync="currentPage"
      :layout="layout"
      :page-size.sync="currentPageSize"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      v-bind="$attrs"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
export default {
  props: {
    total: {
      required: true,
      type: Number,
    },
    page: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 10,
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      },
    },
    // 移动端页码按钮的数量端默认值 5
    pagerCount: {
      type: Number,
      default: document.body.clientWidth < 992 ? 5 : 7,
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper',
    },
    background: {
      type: <PERSON>olean,
      default: true,
    },
    autoScroll: {
      type: Boolean,
      default: true,
    },
    hidden: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(value) {
        this.$emit('update:page', value)
      },
    },
    currentPageSize: {
      get() {
        return this.limit
      },
      set(value) {
        this.$emit('update:limit', value)
      },
    },
  },
  methods: {
    handleSizeChange(value) {
      if (this.currentPage * value > this.total) this.currentPage = 1

      this.$emit('pagination', { page: this.currentPage, size: value })
      if (this.autoScroll) scrollTo(0, 800)
    },
    handleCurrentChange(value) {
      this.$emit('pagination', { page: value, size: this.currentPageSize })
      if (this.autoScroll) scrollTo(0, 800)
    },
  },
}
</script>
