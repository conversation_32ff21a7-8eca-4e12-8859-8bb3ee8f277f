<template>
  <div class="w-full">
    <ElForm ref="form" :model="loginForm" :rules="loginRules">
      <ElFormItem prop="identifier">
        <ElInput v-model="loginForm.identifier" auto-complete="off" placeholder="手机号" prefix-icon="el-icon-mobile-phone" type="text" @keyup.enter.native="handleLogin" />
      </ElFormItem>
      <ElFormItem prop="credential">
        <div class="flex justify-between">
          <ElInput
            v-model="loginForm.credential"
            auto-complete="off"
            class="flex-1"
            placeholder="短信验证码"
            prefix-icon="el-icon-chat-dot-square"
            @keyup.enter.native="handleLogin"
          />
          <ElButton class="ml-2" :disabled="countdown > 0" :loading="smsLoading" @click="sendSmsCode">
            {{ countdown > 0 ? `${countdown}s 后重试` : '发送验证码' }}
          </ElButton>
        </div>
      </ElFormItem>

      <ElFormItem class="w-full">
        <ElButton class="w-full" :loading="loading" size="medium" type="primary" @click="handleLogin">
          <span>{{ loading ? '登 录 中...' : '登 录' }}</span>
        </ElButton>
      </ElFormItem>
    </ElForm>
  </div>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    defaultIdentifier: {
      type: String,
      default: '',
    },
    defaultCredential: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loginForm: {
        identifier: this.defaultIdentifier || (process.env.NODE_ENV === 'development' ? '13800138000' : ''),
        credential: this.defaultCredential || (process.env.NODE_ENV === 'development' ? '123456' : ''),
        uuid: '',
      },
      loginRules: {
        identifier: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        credential: [{ required: true, message: '请输入短信验证码', trigger: 'blur' }],
      },
      smsLoading: false,
      countdown: 0,
      timer: undefined,
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  mounted() {
    this.getCookie()
  },
  methods: {
    getCookie() {
      const identifier = useMyCookies.get('identifier') || this.loginForm.identifier
      const credential = useMyCookies.get('credential')

      this.loginForm.identifier = identifier || ''
      this.loginForm.credential = credential ? useCrypto.decrypt(credential) : this.loginForm.credential
    },
    handleLogin() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const loginData = {
            ...this.loginForm,
            authType: 'MOBILE',
          }
          this.$emit('login', loginData)
        }
      })
    },
    async sendSmsCode() {
      if (!this.loginForm.identifier) {
        this.$message.warning('请输入手机号')
        return
      }

      // 手机号格式验证
      if (!/^1[3-9]\d{9}$/.test(this.loginForm.identifier)) {
        this.$message.warning('请输入正确的手机号')
        return
      }

      this.smsLoading = true
      try {
        // 触发发送短信验证码事件
        this.$emit('send-sms', this.loginForm.identifier)
        // 开始倒计时
        this.startCountdown()
        this.$message.success('验证码已发送')
      } catch {
        this.$message.error('发送失败，请重试')
      } finally {
        this.smsLoading = false
      }
    },
    startCountdown() {
      this.countdown = 60
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
          this.timer = undefined
        }
      }, 1000)
    },
    // 设置表单数据
    setFormData(data) {
      Object.assign(this.loginForm, data)
    },
    // 清空表单
    resetForm() {
      this.$refs.form.resetFields()
    },
  },
}
</script>
