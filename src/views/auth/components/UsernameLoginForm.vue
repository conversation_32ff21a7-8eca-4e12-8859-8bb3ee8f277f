<template>
  <div class="w-full">
    <ElForm ref="form" :model="loginForm" :rules="loginRules">
      <ElFormItem prop="identifier">
        <ElInput v-model="loginForm.identifier" auto-complete="off" placeholder="账号" prefix-icon="el-icon-user" type="text" @keyup.enter.native="handleLogin" />
      </ElFormItem>
      <ElFormItem prop="credential">
        <ElInput v-model="loginForm.credential" auto-complete="off" placeholder="密码" prefix-icon="el-icon-lock" type="password" @keyup.enter.native="handleLogin" />
      </ElFormItem>
      <ElFormItem v-if="showCaptcha" prop="code">
        <div class="flex justify-between">
          <ElInput v-model="loginForm.code" auto-complete="off" class="flex-1" placeholder="验证码" prefix-icon="el-icon-chat-dot-square" @keyup.enter.native="handleLogin" />
          <div class="bg-center bg-cover cursor-pointer h-9 ml-2 w-24" :style="{ 'background-image': `url(${captchaImage})` }" @click="refreshCaptcha" />
        </div>
      </ElFormItem>
      <ElCheckbox v-model="loginForm.rememberMe" class="mb-4">记住密码</ElCheckbox>

      <ElFormItem class="w-full">
        <ElButton class="w-full" :loading="loading" size="medium" type="primary" @click="handleLogin">
          <span>{{ loading ? '登 录 中...' : '登 录' }}</span>
        </ElButton>
      </ElFormItem>
    </ElForm>
  </div>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    showCaptcha: {
      type: Boolean,
      default: true,
    },
    captchaImage: {
      type: String,
      default: '',
    },
    defaultIdentifier: {
      type: String,
      default: '',
    },
    defaultCredential: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loginForm: {
        identifier: this.defaultIdentifier || (process.env.NODE_ENV === 'development' ? 'admin' : ''),
        credential: this.defaultCredential || (process.env.NODE_ENV === 'development' ? 'v@8XBV#uZGn4qz3J' : ''),
        code: '',
        uuid: '',
        rememberMe: true,
      },
      loginRules: {
        identifier: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        credential: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
      },
    }
  },
  mounted() {
    this.getCookie()
  },
  methods: {
    getCookie() {
      const identifier = useMyCookies.get('identifier') || this.loginForm.identifier
      const credential = useMyCookies.get('credential')
      const rememberMe = useMyCookies.get('rememberMe') || this.loginForm.rememberMe

      this.loginForm.identifier = identifier || ''
      this.loginForm.credential = credential ? useCrypto.decrypt(credential) : this.loginForm.credential
      this.loginForm.rememberMe = rememberMe === 'true'
    },
    handleLogin() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const loginData = {
            ...this.loginForm,
            authType: 'USERNAME',
          }
          this.$emit('login', loginData)
        }
      })
    },
    refreshCaptcha() {
      this.$emit('refresh-captcha')
    },
    // 设置表单数据
    setFormData(data) {
      Object.assign(this.loginForm, data)
    },
    // 清空表单
    resetForm() {
      this.$refs.form.resetFields()
    },
  },
}
</script>
