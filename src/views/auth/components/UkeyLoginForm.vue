<template>
  <div class="w-full">
    <ElForm ref="form" :model="loginForm" :rules="loginRules">
      <ElFormItem prop="identifier">
        <ElInput v-model="loginForm.identifier" auto-complete="off" placeholder="账号" prefix-icon="el-icon-user" type="text" @keyup.enter.native="handleLogin" />
      </ElFormItem>
      <ElFormItem prop="credential">
        <ElInput v-model="loginForm.credential" auto-complete="off" placeholder="密码" prefix-icon="el-icon-lock" type="password" @keyup.enter.native="handleLogin" />
      </ElFormItem>

      <!-- UKey 签名按钮 -->
      <ElFormItem>
        <ElButton class="mb-4 w-full" :loading="signing" type="warning" @click="handleUkeySign">
          {{ signing ? '签名中...' : '对数据进行签名' }}
        </ElButton>
      </ElFormItem>

      <!-- UKey 状态显示 -->
      <ElFormItem v-if="ukeyInfo.signData">
        <div class="bg-green-50 border border-green-200 p-3 rounded">
          <p class="text-green-700 text-sm">✓ UKey 签名成功</p>
          <div v-if="ukeyInfo.UserName" class="mt-1 text-gray-600 text-sm">用户身份：{{ ukeyInfo.UserName }}</div>
        </div>
      </ElFormItem>

      <ElCheckbox v-model="loginForm.rememberMe" class="mb-4">记住密码</ElCheckbox>

      <ElFormItem class="w-full">
        <ElButton class="w-full" :loading="loading" size="medium" type="primary" @click="handleLogin">
          <span>{{ loading ? '登 录 中...' : '登 录' }}</span>
        </ElButton>
      </ElFormItem>
    </ElForm>
  </div>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    defaultIdentifier: {
      type: String,
      default: '',
    },
    defaultCredential: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loginForm: {
        identifier: this.defaultIdentifier || (process.env.NODE_ENV === 'development' ? 'admin' : ''),
        credential: this.defaultCredential || (process.env.NODE_ENV === 'development' ? 'v@8XBV#uZGn4qz3J' : ''),
        rememberMe: true,
      },
      loginRules: {
        identifier: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        credential: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      },
      signing: false,
      ukeyInfo: {
        IDInfo: '',
        KeyID: '',
        UserName: '',
        signData: '',
      },
      strDataInfo: Date.now().toString(),
    }
  },
  mounted() {
    this.getCookie()
    this.initUkeyMonitor()
  },
  beforeDestroy() {
    // 清理 UKey 监听与连接
    if (this._ukey && this._ukey.instance && this._ukey.handlers) {
      const { instance, handlers } = this._ukey
      const { open, message, close, error } = handlers
      try {
        if (open && instance.Socket_UK) instance.Socket_UK.removeEventListener('open', open)
        if (message && instance.Socket_UK) instance.Socket_UK.removeEventListener('message', message)
        if (close && instance.Socket_UK) instance.Socket_UK.removeEventListener('close', close)
        if (error && instance.Socket_UK) instance.Socket_UK.removeEventListener('error', error)
      } catch {}
      try {
        if (instance.Socket_UK) {
          instance.Socket_UK.close()
          instance.Socket_UK = undefined
        }
      } catch {}
      this._ukey = undefined
    }
  },
  methods: {
    getCookie() {
      const identifier = useMyCookies.get('identifier') || this.loginForm.identifier
      const credential = useMyCookies.get('credential')
      const rememberMe = useMyCookies.get('rememberMe') || this.loginForm.rememberMe

      this.loginForm.identifier = identifier || ''
      this.loginForm.credential = credential ? useCrypto.decrypt(credential) : this.loginForm.credential
      this.loginForm.rememberMe = rememberMe === 'true'
    },

    initUkeyMonitor() {
      console.log('初始化 UKey 监控')
      try {
        const s_pnp = new SoftKey6W()
        const onOpen = () => {
          console.log('UKey 服务连接成功')
        }
        const onMessage = (Message) => {
          console.log('UKey 事件：', Message)
          const PnpData = JSON.parse(Message.data)
          if (PnpData.type === 'PnpEvent') {
            if (PnpData.IsIn) {
              console.log(`UKEY 已被插入，路径：${PnpData.DevicePath}`)
              this.$message.success(`UKEY 已插入`)
            } else {
              console.log(`UKEY 已被拔出，路径：${PnpData.DevicePath}`)
              this.$message.warning(`UKEY 已拔出`)
              this.ukeyInfo = { IDInfo: '', KeyID: '', UserName: '', signData: '' }
            }
          }
        }
        const onClose = () => {
          console.log('UKey 服务连接关闭')
        }
        const onError = (error) => {
          console.log('UKey 服务连接错误', error)
          this.$message.error('UKey 服务连接失败，请检查服务程序是否安装')
        }

        s_pnp.Socket_UK.addEventListener('open', onOpen)
        s_pnp.Socket_UK.addEventListener('message', onMessage)
        s_pnp.Socket_UK.addEventListener('close', onClose)
        s_pnp.Socket_UK.addEventListener('error', onError)

        // 保存实例与监听器，便于销毁
        this._ukey = {
          instance: s_pnp,
          handlers: { open: onOpen, message: onMessage, close: onClose, error: onError },
        }
      } catch (error) {
        console.log('初始化 UKey 监控失败', error)
        this.$message.error(`初始化 UKey 监控失败：${error.message}`)
      }
    },

    async handleUkeySign() {
      console.log('开始 UKey 签名')
      this.signing = true
      try {
        await this.performUkeySign()
      } catch (error) {
        console.error('UKey 签名失败：', error)
        this.$message.error(`UKey 签名失败：${error.message}`)
      } finally {
        this.signing = false
      }
    },

    async performUkeySign() {
      let DevicePath
      const mSoftKey3A = new SoftKey6W()

      // 查找多把加密锁
      DevicePath = await mSoftKey3A.FindPort(1)
      console.log('查找多个加密锁结果：', DevicePath)

      if (!mSoftKey3A.GetLastError()) {
        console.log('发现多把加密锁')
        this.$message.error('系统上发现有 2 把及以上的加密锁，请只插入要进行的加密锁')
        return
      }

      // 查找单个加密锁
      DevicePath = await mSoftKey3A.FindPort(0)
      console.log('查找单个加密锁结果：', DevicePath)

      if (mSoftKey3A.GetLastError()) {
        console.log('未发现加密锁')
        this.$message.error('未发现加密锁，请插入加密锁')
        return
      }

      // 读取加密锁的芯片唯一 ID
      this.ukeyInfo.IDInfo = await mSoftKey3A.GetChipID(DevicePath)
      if (mSoftKey3A.GetLastError()) {
        // 某些旧型号可能没有芯片唯一 ID，使用加密狗 ID
        const id1 = await mSoftKey3A.GetID_1(DevicePath)
        const id2 = await mSoftKey3A.GetID_2(DevicePath)
        const idError = mSoftKey3A.GetLastError()
        if (idError) {
          console.log(`获取 ID 错误：${idError.message}`)
          this.$message.error(`获取 ID 错误：${idError.message}`)
          return
        }
        this.ukeyInfo.KeyID = toHex(id1) + toHex(id2)
        console.log('获取加密狗 ID 成功：', this.ukeyInfo.KeyID)
      } else {
        console.log('获取芯片 ID 成功：', this.ukeyInfo.IDInfo)
      }

      // 从锁中取出用户身份
      this.ukeyInfo.UserName = await mSoftKey3A.GetSm2UserName(DevicePath)
      const userError = mSoftKey3A.GetLastError()
      if (userError) {
        console.log(`返回用户身份时出现错误：${userError.message}`)
        this.$message.error(`返回用户身份时出现错误：${userError.message}`)
        return
      }
      console.log('获取用户身份成功：', this.ukeyInfo.UserName)

      // 使用默认 PIN 码进行数字签名
      const Pin = '123'
      this.ukeyInfo.signData = await mSoftKey3A.YtSign(this.strDataInfo, Pin, DevicePath)
      const signError = mSoftKey3A.GetLastError()
      if (signError) {
        console.log(`进行签名时出现错误：${signError.message}`)
        this.$message.error(`进行签名时出现错误：${signError.message}`)
        return
      }
      console.log('进行签名成功：', this.ukeyInfo.signData)
      this.$message.success('UKey 签名成功')
    },

    handleLogin() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 检查是否已完成 UKey 签名
          if (!this.ukeyInfo.signData) {
            this.$message.warning('请先完成 UKey 签名')
            return
          }

          const loginData = {
            ...this.loginForm,
            authType: 'UKEY',
            ukeyData: {
              IDInfo: this.ukeyInfo.IDInfo,
              KeyID: this.ukeyInfo.KeyID,
              UserName: this.ukeyInfo.UserName,
              signData: this.ukeyInfo.signData,
              strDataInfo: this.strDataInfo,
            },
          }

          this.$emit('login', loginData)
        }
      })
    },

    // 设置表单数据
    setFormData(data) {
      Object.assign(this.loginForm, data)
    },

    // 清空表单
    resetForm() {
      this.$refs.form?.resetFields()
      this.ukeyInfo = {
        IDInfo: '',
        KeyID: '',
        UserName: '',
        signData: '',
      }
      this.strDataInfo = Date.now().toString()
    },
  },
}
</script>
