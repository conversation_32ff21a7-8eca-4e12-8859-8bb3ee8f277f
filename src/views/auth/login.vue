<template>
  <div class="bg-cover flex h-screen items-center justify-center" :style="{ 'background-image': `url(${backgroundImage})` }">
    <div class="bg-white p-5 pb-1 rounded-lg w-100">
      <div class="mb-4 text-center">
        <div class="inline-flex items-center justify-center">
          <h2 class="font-bold text-2xl text-gray-800">{{ appTitle }}</h2>
        </div>
      </div>

      <!-- 登录方式切换 -->
      <ElTabs v-model="loginType" class="mb-4" @tab-click="handleTabClick">
        <ElTabPane label="用户名密码登录" name="USERNAME"></ElTabPane>
        <ElTabPane label="手机号登录" name="MOBILE"></ElTabPane>
        <ElTabPane label="UKey硬件登录" name="UKEY"></ElTabPane>
      </ElTabs>

      <!-- 用户名密码登录 -->
      <UsernameLoginForm
        v-if="loginType === 'USERNAME'"
        ref="usernameForm"
        :captcha-image="captchaImage"
        :loading="loading"
        :show-captcha="captchaEnabled"
        @login="handleLogin"
        @refresh-captcha="getCode"
      />

      <!-- 手机号登录 -->
      <MobileLoginForm v-else-if="loginType === 'MOBILE'" ref="mobileForm" :loading="loading" @login="handleLogin" @send-sms="sendSmsCode" />

      <!-- UKey 硬件登录 -->
      <UkeyLoginForm v-else-if="loginType === 'UKEY'" ref="ukeyForm" :loading="loading" @login="handleLogin" />
    </div>
    <div class="bottom-3 fixed mx-auto text-white text-xs tracking-wide">{{ appCopyright }}</div>
  </div>
</template>

<script>
import backgroundImage from '@/assets/images/login-background.jpg'
import UkeyLoginForm from './components/UkeyLoginForm.vue'

export default {
  components: {
    UkeyLoginForm,
  },
  data() {
    return {
      backgroundImage,
      captchaImage: '',
      loginType: 'USERNAME', // 默认用户名密码登录
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      registerEnabled: false,
      redirect: undefined,
    }
  },
  computed: {
    appTitle() {
      return useEnvironment.VITE_TITLE
    },
    appCopyright() {
      return useEnvironment.VITE_COPYRIGHT
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true,
    },
  },
  created() {
    this.getCode()
  },
  methods: {
    handleTabClick(tab) {
      this.loginType = tab.name
      // 切换 tab 时重置对应表单
      this.$nextTick(() => {
        switch (this.loginType) {
          case 'USERNAME': {
            this.$refs.usernameForm?.resetForm()

            break
          }
          case 'MOBILE': {
            this.$refs.mobileForm?.resetForm()

            break
          }
          case 'UKEY': {
            this.$refs.ukeyForm?.resetForm()

            break
          }
          // No default
        }
      })
    },
    getCode() {
      useApiGetCaptcha().then((response) => {
        this.captchaEnabled = response.enabled
        this.captchaImage = response?.image
        // 将验证码 key 传递给用户名登录组件
        this.$nextTick(() => {
          if (this.loginType === 'USERNAME') {
            this.$refs.usernameForm?.setFormData({ uuid: response?.key })
          }
        })
      })
    },
    async sendSmsCode(mobile) {
      this.loading = true
      try {
        // 获取短信验证码 UUID
        const response = await useApiGetSmsCaptcha(mobile)
        // 将 UUID 传递给当前活动的表单组件
        this.$nextTick(() => {
          if (this.loginType === 'MOBILE') {
            this.$refs.mobileForm?.setFormData({ uuid: response?.key })
          }
        })
      } catch {
        this.$message.error('发送失败，请重试')
      } finally {
        this.loading = false
      }
    },
    handleLogin(formData) {
      this.loading = true

      // 处理记住密码逻辑
      if (formData.authType === 'USERNAME' && formData.rememberMe) {
        useMyCookies.set('identifier', formData.identifier)
        useMyCookies.set('credential', useCrypto.encrypt(formData.credential))
        useMyCookies.set('rememberMe', formData.rememberMe)
      } else {
        useMyCookies.remove('identifier')
        useMyCookies.remove('credential')
        useMyCookies.remove('rememberMe')
      }

      this.$store
        .dispatch('Login', formData)
        .then(() => {
          this.redirect ? this.$router.push({ path: this.redirect }) : this.$router.push({ name: 'Index' })
        })
        .catch(() => {
          this.loading = false
          if (formData.authType === 'USERNAME' && this.captchaEnabled) {
            this.getCode()
          }
        })
    },
  },
}
</script>
