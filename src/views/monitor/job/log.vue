<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="baseQueryParams" size="small">
        <ElFormItem label="任务名称" prop="jobName">
          <ElInput v-model="baseQueryParams.jobName" class="w-60" clearable placeholder="请输入任务名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="任务组名" prop="jobGroup">
          <ElSelect v-model="baseQueryParams.jobGroup" class="w-60" clearable placeholder="请选择任务组名">
            <ElOption v-for="opt in enumOptions('JobGroupEnum')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="执行状态" prop="status">
          <ElSelect v-model="baseQueryParams.status" class="w-60" clearable placeholder="请选择执行状态">
            <ElOption v-for="opt in enumOptions('SuccessBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="执行时间">
          <ElDatePicker
            v-model="dateRange"
            class="w-60"
            :default-time="['00:00:00', '23:59:59']"
            end-placeholder="结束日期"
            range-separator="-"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></ElDatePicker>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:job:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:job:remove']" icon="el-icon-delete" plain size="mini" type="danger" @click="handleClean">清空</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:job:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton icon="el-icon-close" plain size="mini" type="warning" @click="handleClose">关闭</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>

      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="jobLogList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="日志ID" prop="jobLogId" width="80" />
          <ElTableColumn align="center" header-align="center" label="任务名称" prop="jobName" />
          <ElTableColumn align="center" header-align="center" label="任务组名" prop="jobGroup">
            <template #default="{ row }">
              <span>{{ enumLabel('JobGroupEnum', row.jobGroup) }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="调用目标字符串" prop="invokeTarget" />
          <ElTableColumn align="center" header-align="center" label="日志信息" prop="jobMessage" />
          <ElTableColumn align="center" header-align="center" label="执行状态" prop="status">
            <template #default="{ row }">
              <BooleanTag enum-key="SuccessBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="执行时间" prop="createTime" width="180">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['monitor:job:query']" icon="el-icon-view" size="mini" type="text" @click="handleView(row)">详细</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>

      <Pagination v-show="total > 0" slot="pagination" :limit.sync="baseQueryParams.size" :page.sync="baseQueryParams.page" :total="total" @pagination="getList" />
    </TreeTable>

    <!-- 调度日志详细 -->
    <ElDialog append-to-body :close-on-click-modal="false" title="调度日志详细" :visible.sync="open" width="700px">
      <ElForm ref="form" label-width="100px" :model="form" size="mini">
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="日志序号：">{{ form.jobLogId }}</ElFormItem>
            <ElFormItem label="任务名称：">{{ form.jobName }}</ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="任务分组：">{{ enumLabel('JobGroupEnum', form.jobGroup) }}</ElFormItem>
            <ElFormItem label="执行时间：">{{ form.createTime }}</ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="调用方法：">{{ form.invokeTarget }}</ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="日志信息：">{{ form.jobMessage }}</ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="执行状态：">
              <div>{{ form.status ? '成功' : '失败' }}</div>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="异常信息：">{{ form.exceptionInfo }}</ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
      <template #footer>
        <ElButton @click="open = false">关 闭</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调度日志表格数据
      jobLogList: [],
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      // （全局枚举）无需本地维护选项与格式化
      // 基础查询参数
      baseQueryParams: {
        page: 1,
        size: 10,
        jobName: undefined,
        jobGroup: undefined,
        status: true,
      },
    }
  },
  computed: {
    queryParams() {
      return {
        ...this.baseQueryParams,
        beginTime: this.dateRange?.[0],
        endTime: this.dateRange?.[1],
      }
    },
    jobId() {
      return this.$route.params?.jobId
    },
  },
  created() {
    if (this.jobId) {
      useApiGetJob(this.jobId).then((response) => {
        this.getList({
          ...this.baseQueryParams,
          jobName: response.jobName,
          jobGroup: response.jobGroup,
        })
      })
    } else {
      this.getList()
    }
  },
  methods: {
    // 无需本地 loadEnums，统一由 $enums 管理
    // 查询调度日志列表
    getList(parameters = this.queryParams) {
      this.loading = true
      useApiGetJobLogList(parameters).then((response) => {
        this.jobLogList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 返回按钮
    handleClose() {
      const object = { path: '/monitor/job' }
      appTab.closeOpenPage(object)
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList({
        ...this.baseQueryParams,
        page: 1,
      })
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.jobLogId)
      this.multiple = selection.length === 0
    },
    // 详细按钮操作
    handleView(row) {
      this.open = true
      this.form = row
    },
    // 删除按钮操作
    handleDelete(row) {
      const jobLogIds = this.ids
      appModal
        .confirm(`是否确认删除调度日志 ID 为"${jobLogIds}"的数据项？`)
        .then(() => {
          return useApiDelJobLog(jobLogIds)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 清空按钮操作
    handleClean() {
      appModal
        .confirm('是否确认清空所有调度日志数据项？')
        .then(() => {
          return useApiCleanJobLog()
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('清空成功')
        })
        .catch(() => {})
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'monitor/jobLog/export',
        data: this.queryParams,
        filename: '调度日志',
      })
    },
  },
}
</script>
