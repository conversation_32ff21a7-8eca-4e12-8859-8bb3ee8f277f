<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="queryParams" size="small">
        <ElFormItem label="任务名称" prop="jobName">
          <ElInput v-model="queryParams.jobName" clearable placeholder="请输入任务名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="任务组名" prop="jobGroup">
          <ElSelect v-model="queryParams.jobGroup" clearable placeholder="请选择任务组名">
            <ElOption v-for="opt in enumOptions('JobGroupEnum')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="任务状态" prop="status">
          <ElSelect v-model="queryParams.status" clearable placeholder="请选择任务状态">
            <ElOption v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:job:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:job:edit']" :disabled="single" icon="el-icon-edit" plain size="mini" type="success" @click="handleUpdate">修改</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:job:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:job:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:job:query']" icon="el-icon-s-operation" plain size="mini" type="info" @click="handleJobLog">日志</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>

      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="jobList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="任务ID" prop="jobId" width="100" />
          <ElTableColumn align="center" header-align="center" label="任务名称" prop="jobName" width="200" />
          <ElTableColumn align="center" header-align="center" label="任务组名" prop="jobGroup" width="100">
            <template #default="{ row }">
              <span>{{ enumLabel('JobGroupEnum', row.jobGroup) }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="left" header-align="center" label="调用目标字符串" prop="invokeTarget" />
          <ElTableColumn align="center" header-align="center" label="cron执行表达式" prop="cronExpression" width="200" />
          <ElTableColumn align="center" header-align="center" label="状态" width="80">
            <template #default="{ row }">
              <ElSwitch v-model="row.status" :active-value="true" :inactive-value="false" @change="handleStatusChange(row)"></ElSwitch>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['monitor:job:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
              <ElButton v-hasPermission="['monitor:job:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
              <ElDropdown v-hasPermission="['monitor:job:changeStatus', 'monitor:job:query']" size="mini" @command="(command) => handleCommand(command, row)">
                <ElButton icon="el-icon-d-arrow-right" size="mini" type="text">更多</ElButton>
                <template #dropdown>
                  <ElDropdownMenu>
                    <ElDropdownItem v-hasPermission="['monitor:job:changeStatus']" command="handleRun" icon="el-icon-caret-right">执行一次</ElDropdownItem>
                    <ElDropdownItem v-hasPermission="['monitor:job:query']" command="handleView" icon="el-icon-view">任务详细</ElDropdownItem>
                    <ElDropdownItem v-hasPermission="['monitor:job:query']" command="handleJobLog" icon="el-icon-s-operation">调度日志</ElDropdownItem>
                  </ElDropdownMenu>
                </template>
              </ElDropdown>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>

      <Pagination v-show="total > 0" slot="pagination" :limit.sync="queryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <div>
      <!-- 添加或修改定时任务对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="800px">
        <ElForm ref="form" label-width="120px" :model="form" :rules="rules">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="任务名称" prop="jobName">
                <ElInput v-model="form.jobName" placeholder="请输入任务名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="任务分组" prop="jobGroup">
                <ElSelect v-model="form.jobGroup" placeholder="请选择任务分组">
                  <ElOption v-for="opt in enumOptions('JobGroupEnum')" :key="opt.value" :label="opt.label" :value="opt.value" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="调用方法" prop="invokeTarget">
                <ElInput v-model="form.invokeTarget" placeholder="请输入调用目标字符串" />
                <InvokeMethodHelp />
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="cron 表达式" prop="cronExpression">
                <ElInput v-model="form.cronExpression" placeholder="请输入 cron 执行表达式">
                  <template #append>
                    <ElButton type="primary" @click="handleShowCron">
                      生成表达式
                      <i class="el-icon--right el-icon-time"></i>
                    </ElButton>
                  </template>
                </ElInput>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="状态">
                <ElRadioGroup v-model="form.status">
                  <ElRadio v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="是否并发" prop="concurrent">
                <ElRadioGroup v-model="form.concurrent">
                  <ElRadio v-for="opt in enumOptions('AllowDenyBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="执行策略" prop="misfirePolicy">
                <ElRadioGroup v-model="form.misfirePolicy" size="small">
                  <ElRadioButton v-for="opt in enumOptions('MisfirePolicyEnum')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadioButton>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </template>
      </ElDialog>

      <ElDialog append-to-body class="scrollbar" :close-on-click-modal="false" destroy-on-close title="Cron 表达式生成器" :visible.sync="openCron">
        <VCronTab :expression="expression" @fill="crontabFill" @hide="openCron = false"></VCronTab>
      </ElDialog>

      <!-- 任务日志详细 -->
      <ElDialog append-to-body :close-on-click-modal="false" title="任务详细" :visible.sync="openView" width="700px">
        <ElForm ref="form" label-width="120px" :model="form" size="mini">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="任务ID：">{{ form.jobId }}</ElFormItem>
              <ElFormItem label="任务名称：">{{ form.jobName }}</ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="任务分组：">{{ enumLabel('JobGroupEnum', form.jobGroup) }}</ElFormItem>
              <ElFormItem label="创建时间：">{{ form.createTime }}</ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="cron表达式：">{{ form.cronExpression }}</ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="下次执行时间：">{{ form.nextValidTime }}</ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="调用目标方法：">{{ form.invokeTarget }}</ElFormItem>
            </ElCol>
            <ElCol v-if="form.remark" :span="24">
              <ElFormItem label="备注说明：">
                <div class="bg-gray-50 border border-gray-200 p-3 rounded-md text-gray-700 text-sm whitespace-pre-line">{{ form.remark }}</div>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="任务状态：">
                <div>{{ form.status ? '启用' : '禁用' }}</div>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="是否并发：">
                <div>{{ form.concurrent ? '允许' : '禁止' }}</div>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="执行策略：">
                <div>{{ enumLabel('MisfirePolicyEnum', form.misfirePolicy) }}</div>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <template #footer>
          <ElButton @click="openView = false">关 闭</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
import vcrontab from 'vcrontab'

export default {
  components: { VCronTab: vcrontab },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 定时任务表格数据
      jobList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示详细弹出层
      openView: false,
      // 是否显示 Cron 表达式弹出层
      openCron: false,
      // 传入的表达式
      expression: '',
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        jobName: undefined,
        jobGroup: undefined,
        status: true,
      },
      // 表单参数
      form: {
        jobId: undefined,
        jobName: undefined,
        jobGroup: 'default',
        invokeTarget: undefined,
        cronExpression: undefined,
        misfirePolicy: '0',
        concurrent: false,
        status: true,
      },
      // 表单校验
      rules: {
        jobName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
        invokeTarget: [{ required: true, message: '调用目标字符串不能为空', trigger: 'blur' }],
        cronExpression: [{ required: true, message: 'cron 执行表达式不能为空', trigger: 'blur' }],
      },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询定时任务列表
    getList() {
      this.loading = true
      useApiGetJobList(this.queryParams).then((response) => {
        this.jobList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        jobId: undefined,
        jobName: undefined,
        jobGroup: 'default',
        invokeTarget: undefined,
        cronExpression: undefined,
        misfirePolicy: '0',
        concurrent: false,
        status: true,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.jobId)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'handleRun': {
          this.handleRun(row)
          break
        }
        case 'handleView': {
          this.handleView(row)
          break
        }
        case 'handleJobLog': {
          this.handleJobLog(row)
          break
        }
        default: {
          break
        }
      }
    },
    // 任务状态修改
    handleStatusChange(row) {
      const text = row.status ? '启用' : '禁用'
      appModal
        .confirm(`确认要"${text}""${row.jobName}"任务吗？`)
        .then(() => {
          return useApiPatchJob(row.jobId, { status: row.status })
        })
        .then(() => {
          appModal.msgSuccess(`${text}成功`)
        })
        .catch(() => {
          row.status = !row.status
        })
    },
    // 立即执行一次
    handleRun(row) {
      appModal
        .confirm(`确认要立即执行一次"${row.jobName}"任务吗？`)
        .then(() => {
          return useApiRunJob(row.jobId, row.jobGroup)
        })
        .then(() => {
          appModal.msgSuccess('执行成功')
        })
        .catch(() => {})
    },
    // 任务详细信息
    handleView(row) {
      useApiGetJob(row.jobId).then((response) => {
        this.form = { ...this.form, ...response }
        this.openView = true
      })
    },
    // cron 表达式按钮操作
    handleShowCron() {
      this.expression = this.form.cronExpression
      this.openCron = true
    },
    // 确定后回传值
    crontabFill(value) {
      this.form.cronExpression = value
    },
    // 任务日志列表查询
    handleJobLog(row) {
      const jobId = row.jobId || 0
      this.$router.push(`/monitor/job-log/index/${jobId}`)
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加任务'
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      const jobId = row.jobId || this.ids
      useApiGetJob(jobId).then((response) => {
        this.form = {
          jobId: response.jobId,
          jobName: response.jobName,
          jobGroup: response.jobGroup,
          invokeTarget: response.invokeTarget,
          cronExpression: response.cronExpression,
          misfirePolicy: response.misfirePolicy === 'IGNORE_MISFIRES' ? '1' : response.misfirePolicy,
          concurrent: response.concurrent,
          status: response.status,
        }
        this.open = true
        this.title = '修改任务'
      })
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 创建提交数据，排除 remark 字段
          const submitData = { ...this.form }
          delete submitData.remark

          if (this.form.jobId === undefined) {
            useApiAddJob(submitData).then((response) => {
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          } else {
            useApiUpdateJob(this.form.jobId, submitData).then((response) => {
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      const jobIds = row.jobId || this.ids
      appModal
        .confirm(`是否确认删除定时任务 ID 为"${jobIds}"的数据项？`)
        .then(() => {
          return useApiDelJob(jobIds)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'monitor/job/export',
        data: this.queryParams,
        filename: '定时任务',
      })
    },
  },
}
</script>
