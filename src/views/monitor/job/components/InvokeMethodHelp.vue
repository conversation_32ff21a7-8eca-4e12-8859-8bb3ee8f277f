<template>
  <div class="bg-blue-50 border border-blue-200 invoke-method-help mb-3 mt-2 p-4 rounded-lg">
    <div class="flex font-medium items-center mb-3 text-blue-800 text-sm">
      <i class="el-icon-info mr-2"></i>
      定时任务调用示例：
    </div>

    <div class="space-y-3">
      <div v-for="(category, index) in invokeCategories" :key="index" class="bg-white border border-gray-200 p-3 rounded-md">
        <div class="flex font-medium items-center mb-2 text-gray-700 text-sm">
          <i :class="[category.icon, category.iconColor]"></i>
          {{ category.title }}：
        </div>
        <div class="ml-6 space-y-1 text-gray-600 text-sm">
          <div v-for="(method, methodIndex) in category.methods" :key="methodIndex" class="flex items-center">
            <span :class="category.dotColor"></span>
            <code class="bg-gray-100 px-2 py-1 rounded text-xs">{{ method.code }}</code>
            <span class="ml-2 text-gray-500">- {{ method.description }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      invokeCategories: [
        {
          title: 'Bean 调用示例',
          icon: 'el-icon-s-tools mr-2',
          iconColor: 'text-blue-500',
          dotColor: 'bg-blue-400 h-2 mr-2 rounded-full w-2',
          methods: [
            {
              code: 'testTask.execute()',
              description: '执行简单测试任务',
            },
            {
              code: "testTask.executeWithParams('hello', 5)",
              description: '执行带参数的任务',
            },
            {
              code: 'testTask.generateDailyReport()',
              description: '生成日报',
            },
            {
              code: 'testTask.cleanupData()',
              description: '数据清理',
            },
            {
              code: 'testTask.sendEmailNotification()',
              description: '发送邮件通知',
            },
          ],
        },
        {
          title: 'Class 类调用示例',
          icon: 'el-icon-s-cooperation mr-2',
          iconColor: 'text-green-500',
          dotColor: 'bg-green-400 h-2 mr-2 rounded-full w-2',
          methods: [
            {
              code: 'com.xinjian.quartz.task.TestTask.execute()',
              description: '执行简单测试任务',
            },
            {
              code: "com.xinjian.quartz.task.TestTask.executeWithParams('hello', 5)",
              description: '执行带参数的任务',
            },
            {
              code: 'com.xinjian.quartz.task.TestTask.generateDailyReport()',
              description: '生成日报',
            },
            {
              code: 'com.xinjian.quartz.task.TestTask.cleanupData()',
              description: '数据清理',
            },
            {
              code: 'com.xinjian.quartz.task.TestTask.sendEmailNotification()',
              description: '发送邮件通知',
            },
          ],
        },
      ],
    }
  },
}
</script>
