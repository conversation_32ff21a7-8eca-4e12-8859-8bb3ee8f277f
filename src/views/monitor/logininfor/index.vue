<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="baseQueryParams" size="small">
        <ElFormItem label="登录地址" prop="ipaddr">
          <ElInput v-model="baseQueryParams.ipaddr" class="w-60" clearable placeholder="请输入登录地址" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="用户名称" prop="username">
          <ElInput v-model="baseQueryParams.username" class="w-60" clearable placeholder="请输入用户名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="baseQueryParams.status" class="w-60" clearable placeholder="登录状态">
            <ElOption v-for="opt in enumOptions('SuccessBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="登录时间">
          <ElDatePicker
            v-model="dateRange"
            class="w-60"
            :default-time="['00:00:00', '23:59:59']"
            end-placeholder="结束日期"
            range-separator="-"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></ElDatePicker>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:logininfor:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleBatchDelete"
            >删除</ElButton
          >
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:logininfor:remove']" icon="el-icon-delete" plain size="mini" type="danger" @click="handleClean">清空</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:logininfor:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
      </ElRow>

      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>
      <template #rightTable="{ height: tableHeight }">
        <ElTable
          ref="tables"
          v-loading="loading"
          :data="list"
          :default-sort="defaultSort"
          :max-height="tableHeight"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
        >
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="访问ID" prop="infoId" />
          <ElTableColumn align="center" header-align="center" label="用户ID" prop="userId" />
          <ElTableColumn align="center" header-align="center" label="用户名称" prop="username" :sort-orders="['descending', 'ascending']" sortable="custom" />
          <ElTableColumn align="center" header-align="center" label="登录地址" prop="ipaddr" width="130" />
          <ElTableColumn align="center" header-align="center" label="登录地点" prop="loginLocation" />
          <ElTableColumn align="center" header-align="center" label="浏览器" prop="browser" />
          <ElTableColumn align="center" header-align="center" label="操作系统" prop="os" />
          <ElTableColumn align="center" header-align="center" label="登录状态" prop="status">
            <template #default="{ row }">
              <BooleanTag enum-key="SuccessBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="操作信息" prop="msg" />
          <ElTableColumn align="center" header-align="center" label="登录日期" prop="loginTime" :sort-orders="['descending', 'ascending']" sortable="custom" width="180">
            <template #default="{ row }">
              <span>{{ row.loginTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="操作" width="150">
            <template #default="{ row }">
              <ElButton v-hasPermission="['monitor:logininfor:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
              <ElButton v-hasPermission="['monitor:logininfor:unlock']" icon="el-icon-unlock" size="mini" type="text" @click="handleUnlock(row)">解锁</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <Pagination v-show="total > 0" slot="pagination" :limit.sync="baseQueryParams.size" :page.sync="baseQueryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: { prop: 'loginTime', order: 'descending' },
      // 基础查询参数
      baseQueryParams: {
        page: 1,
        size: 10,
        ipaddr: undefined,
        username: undefined,
        status: true,
      },
    }
  },
  computed: {
    queryParams() {
      return {
        ...this.baseQueryParams,
        beginTime: this.dateRange?.[0],
        endTime: this.dateRange?.[1],
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询登录日志列表
    getList(parameters = this.queryParams) {
      this.loading = true
      useApiGetLoginLogList(parameters).then((response) => {
        this.list = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList({
        ...this.queryParams,
        page: 1,
      })
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.getList({
        ...this.queryParams,
        page: 1,
      })
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.infoId)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 排序触发事件
    handleSortChange(column, property, order) {
      this.getList({
        ...this.queryParams,
        orderByColumn: column.prop,
        isAsc: column.order,
      })
    },
    // 单个删除按钮操作
    handleDelete(row) {
      appModal
        .confirm(`是否确认删除访问 ID 为"${row.infoId}"的数据项？`)
        .then(() => {
          return useApiDelLoginLog(row.infoId)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 批量删除按钮操作
    handleBatchDelete() {
      if (this.ids.length === 0) {
        appModal.msgWarning('请至少选择一条数据')
        return
      }
      appModal
        .confirm(`是否确认删除选中的${this.ids.length}条数据项？`)
        .then(() => {
          return useApiBatchDelLoginLog(this.ids)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 清空按钮操作
    handleClean() {
      appModal
        .confirm('是否确认清空所有登录日志数据项？')
        .then(() => {
          return useApiCleanLoginLog()
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('清空成功')
        })
        .catch(() => {})
    },
    // 解锁按钮操作
    handleUnlock(row) {
      const username = row ? row.username : this.selectNames
      const userId = row ? row.userId : this.selectUserIds
      appModal
        .confirm(`是否确认解锁用户"${username}"数据项？`)
        .then(() => {
          return useApiUnlockLoginStatus(userId)
        })
        .then(() => {
          appModal.msgSuccess(`用户${username}解锁成功`)
        })
        .catch(() => {})
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'monitor/logininfor/export',
        data: this.queryParams,
        filename: '登录日志',
      })
    },
  },
}
</script>
