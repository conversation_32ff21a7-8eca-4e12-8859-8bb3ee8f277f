<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="baseQueryParams" size="small">
        <ElFormItem label="操作地址" prop="operIp">
          <ElInput v-model="baseQueryParams.operIp" class="w-60" clearable placeholder="请输入操作地址" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="系统模块" prop="title">
          <ElInput v-model="baseQueryParams.title" class="w-60" clearable placeholder="请输入系统模块" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="操作人员" prop="operName">
          <ElInput v-model="baseQueryParams.operName" class="w-60" clearable placeholder="请输入操作人员" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="类型" prop="businessType">
          <ElSelect v-model="baseQueryParams.businessType" class="w-60" clearable placeholder="操作类型">
            <ElOption v-for="opt in enumOptions('BusinessTypeEnum')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="baseQueryParams.status" class="w-60" clearable placeholder="操作状态">
            <ElOption v-for="opt in enumOptions('SuccessBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="操作时间">
          <ElDatePicker
            v-model="dateRange"
            class="w-60"
            :default-time="['00:00:00', '23:59:59']"
            end-placeholder="结束日期"
            range-separator="-"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></ElDatePicker>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:operlog:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:operlog:remove']" icon="el-icon-delete" plain size="mini" type="danger" @click="handleClean">清空</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['monitor:operlog:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>

      <template #rightTable="{ height: tableHeight }">
        <ElTable
          ref="tables"
          v-loading="loading"
          :data="list"
          :default-sort="defaultSort"
          :max-height="tableHeight"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
        >
          <ElTableColumn align="center" header-align="center" type="selection" width="50" />
          <ElTableColumn align="center" header-align="center" label="日志ID" prop="operId" />
          <ElTableColumn align="center" header-align="center" label="系统模块" prop="title" />
          <ElTableColumn align="center" header-align="center" label="操作类型" prop="businessType">
            <template #default="{ row }">
              <span>{{ enumLabel('BusinessTypeEnum', row.businessType) }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="操作人员" prop="operName" :sort-orders="['descending', 'ascending']" sortable="custom" width="110" />
          <ElTableColumn align="center" header-align="center" label="操作地址" prop="operIp" width="130" />
          <ElTableColumn align="center" header-align="center" label="操作地点" prop="operLocation" />
          <ElTableColumn align="center" header-align="center" label="操作状态" prop="status">
            <template #default="{ row }">
              <BooleanTag enum-key="SuccessBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="操作日期" prop="operTime" :sort-orders="['descending', 'ascending']" sortable="custom" width="160">
            <template #default="{ row }">
              <span>{{ row.operTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="消耗时间" prop="costTime" :sort-orders="['descending', 'ascending']" sortable="custom" width="110">
            <template #default="{ row }">
              <span>{{ row.costTime }}毫秒</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row, $index }">
              <ElButton v-hasPermission="['monitor:operlog:query']" icon="el-icon-view" size="mini" type="text" @click="handleView(row, index)">详细</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>

      <Pagination v-show="total > 0" slot="pagination" :limit.sync="queryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <div>
      <!-- 操作日志详细 -->
      <ElDialog append-to-body :close-on-click-modal="false" title="操作日志详细" :visible.sync="open" width="700px">
        <ElForm ref="form" label-width="100px" :model="form" size="mini">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="操作模块：">{{ form.title }} / {{ enumLabel('BusinessTypeEnum', form.businessType) }}</ElFormItem>
              <ElFormItem label="登录信息：">{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="请求地址：">{{ form.operUrl }}</ElFormItem>
              <ElFormItem label="请求方式：">{{ form.requestMethod }}</ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="操作方法：">{{ form.method }}</ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="请求参数：">{{ form.operParam }}</ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="返回参数：">{{ form.jsonResult }}</ElFormItem>
            </ElCol>
            <ElCol :span="6">
              <ElFormItem label="操作状态：">
                <div v-if="form.status === false">正常</div>
                <div v-else-if="form.status === true">失败</div>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="消耗时间：">{{ form.costTime }}毫秒</ElFormItem>
            </ElCol>
            <ElCol :span="10">
              <ElFormItem label="操作时间：">{{ form.operTime }}</ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem v-if="form.status === true" label="异常信息：">{{ form.errorMsg }}</ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <template #footer>
          <ElButton @click="open = false">关 闭</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: { prop: 'operTime', order: 'descending' },
      // 表单参数
      form: {},
      // 基础查询参数
      baseQueryParams: {
        page: 1,
        size: 10,
        operIp: undefined,
        title: undefined,
        operName: undefined,
        businessType: undefined,
        status: true,
      },
    }
  },
  computed: {
    queryParams() {
      return {
        ...this.baseQueryParams,
        beginTime: this.dateRange?.[0],
        endTime: this.dateRange?.[1],
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询登录日志
    getList(parameters = this.queryParams) {
      console.log('🚀 ‣ parameters:', parameters)
      this.loading = true
      useApiGetOperationLogList(parameters).then((response) => {
        this.list = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList({
        ...this.queryParams,
        page: 1,
      })
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.getList({
        ...this.queryParams,
        page: 1,
      })
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.operId)
      this.multiple = selection.length === 0
    },
    // 排序触发事件
    handleSortChange(column, property, order) {
      this.getList({
        ...this.queryParams,
        orderByColumn: column.prop,
        isAsc: column.order,
      })
    },
    // 详细按钮操作
    handleView(row) {
      this.open = true
      this.form = row
    },
    // 删除按钮操作
    handleDelete(row) {
      const operIds = row.operId || this.ids
      appModal
        .confirm(`是否确认删除日志 ID 为"${operIds}"的数据项？`)
        .then(() => {
          return useApiDelOperationLog(operIds)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 清空按钮操作
    handleClean() {
      appModal
        .confirm('是否确认清空所有操作日志数据项？')
        .then(() => {
          return useApiCleanOperationLog()
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('清空成功')
        })
        .catch(() => {})
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'monitor/operlog/export',
        data: this.queryParams,
        filename: '操作日志',
      })
    },
  },
}
</script>
