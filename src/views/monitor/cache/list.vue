<template>
  <div class="p-4">
    <ElRow class="mb-3" :gutter="10">
      <ElCol :span="8">
        <ElCard class="h-[calc(100vh-125px)]">
          <template #header>
            <span><i class="el-icon-collection"></i> 缓存列表</span>
            <ElButton class="float-right py-0.75" icon="el-icon-refresh-right" type="text" @click="refreshCacheNames()"></ElButton>
          </template>
          <ElTable v-loading="loading" class="w-full" :data="cacheNames" :height="tableHeight" highlight-current-row @row-click="getCacheKeys">
            <ElTableColumn align="center" header-align="center" label="序号" type="index" width="60"></ElTableColumn>

            <ElTableColumn align="left" :formatter="nameFormatter" header-align="center" label="缓存名称" prop="cacheName"></ElTableColumn>

            <ElTableColumn align="left" header-align="center" label="备注" prop="remark" />
            <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作" width="60">
              <template #default="{ row }">
                <ElButton icon="el-icon-delete" size="mini" type="text" @click="handleClearCacheName(row)"></ElButton>
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElCol>

      <ElCol :span="8">
        <ElCard class="h-[calc(100vh-125px)]">
          <template #header>
            <span><i class="el-icon-key"></i> 键名列表</span>
            <ElButton class="float-right py-0.75" icon="el-icon-refresh-right" type="text" @click="refreshCacheKeys()"></ElButton>
          </template>
          <ElTable v-loading="subLoading" class="w-full" :data="cacheKeys" :height="tableHeight" highlight-current-row @row-click="handleCacheValue">
            <ElTableColumn align="center" header-align="center" label="序号" type="index" width="60"></ElTableColumn>
            <ElTableColumn align="left" :formatter="keyFormatter" header-align="center" label="缓存键名"> </ElTableColumn>
            <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作" width="60">
              <template #default="{ row }">
                <ElButton icon="el-icon-delete" size="mini" type="text" @click="handleClearCacheKey(row)"></ElButton>
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElCol>

      <ElCol :span="8">
        <ElCard :bordered="false" class="h-[calc(100vh-125px)]">
          <template #header>
            <span><i class="el-icon-document"></i> 缓存内容</span>
            <ElButton class="float-right py-0.75" icon="el-icon-refresh-right" type="text" @click="handleClearCacheAll()">清理全部</ElButton>
          </template>
          <ElForm :model="cacheForm">
            <ElRow :gutter="32">
              <ElCol :offset="1" :span="22">
                <ElFormItem label="缓存名称:" prop="cacheName">
                  <ElInput v-model="cacheForm.cacheName" :read-only="true" />
                </ElFormItem>
              </ElCol>
              <ElCol :offset="1" :span="22">
                <ElFormItem label="缓存键名:" prop="cacheKey">
                  <ElInput v-model="cacheForm.cacheKey" :read-only="true" />
                </ElFormItem>
              </ElCol>
              <ElCol :offset="1" :span="22">
                <ElFormItem label="缓存内容:" prop="cacheValue">
                  <ElInput v-model="cacheForm.cacheValue" :read-only="true" :rows="8" type="textarea" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<script>
export default {
  data() {
    return {
      cacheNames: [],
      cacheKeys: [],
      cacheForm: {},
      loading: true,
      subLoading: false,
      nowCacheName: '',
      tableHeight: window.innerHeight - 200,
    }
  },
  created() {
    this.getCacheNames()
  },
  methods: {
    // 查询缓存名称列表
    getCacheNames() {
      this.loading = true
      useApiGetCacheNameList().then((response) => {
        this.cacheNames = response
        this.loading = false
      })
    },
    // 刷新缓存名称列表
    refreshCacheNames() {
      this.getCacheNames()
      appModal.msgSuccess('刷新缓存列表成功')
    },
    // 清理指定名称缓存
    handleClearCacheName(row) {
      useApiCleanCacheName(row.cacheName).then((response) => {
        appModal.msgSuccess(`清理缓存名称 [${row.cacheName}] 成功`)
        this.getCacheKeys()
      })
    },
    // 查询缓存键名列表
    getCacheKeys(row) {
      const cacheName = row === undefined ? this.nowCacheName : row.cacheName
      if (cacheName === '') {
        return
      }
      this.subLoading = true
      useApiGetCacheKeyList(cacheName).then((response) => {
        this.cacheKeys = response
        this.subLoading = false
        this.nowCacheName = cacheName
      })
    },
    // 刷新缓存键名列表
    refreshCacheKeys() {
      this.getCacheKeys()
      appModal.msgSuccess('刷新键名列表成功')
    },
    // 清理指定键名缓存
    handleClearCacheKey(cacheKey) {
      useApiCleanCacheKey(cacheKey).then((response) => {
        appModal.msgSuccess(`清理缓存键名 [${cacheKey}] 成功`)
        this.getCacheKeys()
      })
    },
    // 列表前缀去除
    nameFormatter(row) {
      return row.cacheName.replace(':', '')
    },
    // 键名前缀去除
    keyFormatter(cacheKey) {
      return cacheKey
    },
    // 查询缓存内容详细
    handleCacheValue(cacheKey) {
      useApiGetCacheValue(this.nowCacheName, cacheKey).then((response) => {
        this.cacheForm = response
      })
    },
    // 清理全部缓存
    handleClearCacheAll() {
      useApiCleanCacheAll().then((response) => {
        appModal.msgSuccess('清理全部缓存成功')
      })
    },
  },
}
</script>
