<template>
  <div class="h-full overflow-auto p-10px">
    <ElRow>
      <ElCol class="mb-10px px-15px" :span="24">
        <ElCard>
          <template #header>
            <span><i class="el-icon-monitor"></i> 基本信息</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" class="w-full">
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf"><div class="cell">Redis 版本</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.redis_version }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">运行模式</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.redis_mode === 'standalone' ? '单机' : '集群' }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">端口</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.tcp_port }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">客户端数</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.connected_clients }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf"><div class="cell">运行时间 (天)</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.uptime_in_days }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">使用内存</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.used_memory_human }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">使用 CPU</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ parseFloat(cacheInfo.used_cpu_user_children).toFixed(2) }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">内存配置</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.maxmemory_human }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf"><div class="cell">AOF 是否开启</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.aof_enabled === '0' ? '否' : '是' }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">RDB 是否成功</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.rdb_last_bgsave_status }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">Key 数量</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.dbSize }}</div>
                  </td>
                  <td class="el-table__cell is-leaf"><div class="cell">网络入口/出口</div></td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cacheInfo" class="cell">{{ cacheInfo.instantaneous_input_kbps }}kps/{{ cacheInfo.instantaneous_output_kbps }}kps</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </ElCard>
      </ElCol>

      <ElCol class="mb-10px px-15px" :span="12">
        <ElCard>
          <template #header>
            <span><i class="el-icon-pie-chart"></i> 命令统计</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <VChart ref="commandStatsChart" autoresize class="h-105" :option="commandStatsOption" theme="macarons"></VChart>
          </div>
        </ElCard>
      </ElCol>

      <ElCol class="mb-10px px-15px" :span="12">
        <ElCard>
          <template #header>
            <span><i class="el-icon-odometer"></i> 内存信息</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <VChart ref="usedMemoryChart" autoresize class="h-105" :option="usedMemoryOption" theme="macarons"></VChart>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<script>
export default {
  data() {
    return {
      cacheInfo: {},
      commandStatsOption: {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)',
        },
        series: [
          {
            name: '命令',
            type: 'pie',
            roseType: 'radius',
            radius: [15, 95],
            center: ['50%', '38%'],
            data: [],
            animationEasing: 'cubicInOut',
            animationDuration: 1000,
          },
        ],
      },
      usedMemoryOption: {
        tooltip: {
          // formatter: `{b} <br/>{a} : ${this.cacheInfo.used_memory_human}`,
        },
        series: [
          {
            name: '峰值',
            type: 'gauge',
            min: 0,
            max: 1000,
            detail: {
              // formatter: this.cacheInfo.used_memory_human,
            },
            data: [
              {
                // value: Number.parseFloat(this.cacheInfo.used_memory_human),
                name: '内存消耗',
              },
            ],
          },
        ],
      },
    }
  },
  created() {
    this.openLoading()
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      const response = await useApiGetCache()

      appModal.closeLoading()

      const commandStats = response.commandStats
      this.cacheInfo = { ...response.info, ...response.dbSize }

      await this.$nextTick()

      this.$refs.commandStatsChart.setOption({
        series: [{ data: commandStats }],
      })

      this.$refs.usedMemoryChart.setOption({
        tooltip: {
          formatter: `{b} <br/>{a} : ${this.cacheInfo.used_memory_human}`,
        },
        series: [
          {
            detail: {
              formatter: this.cacheInfo.used_memory_human,
            },
            data: [
              {
                value: Number.parseFloat(this.cacheInfo.used_memory_human),
                name: '内存消耗',
              },
            ],
          },
        ],
      })
    },
    // 打开加载层
    openLoading() {
      appModal.loading('正在加载缓存监控数据，请稍候！')
    },
  },
}
</script>
