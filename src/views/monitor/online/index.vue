<template>
  <div>
    <TreeTable>
      <ElForm ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="queryParams" size="small">
        <ElFormItem label="登录地址" prop="ipAddress">
          <ElInput v-model="queryParams.ipAddress" clearable placeholder="请输入登录地址" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="用户名称" prop="username">
          <ElInput v-model="queryParams.username" clearable placeholder="请输入用户名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" class="w-full" :data="list.slice((page - 1) * size, page * size)" :max-height="tableHeight">
          <ElTableColumn align="center" header-align="center" label="序号" type="index">
            <template #default="{ row, $index }">
              <span>{{ (page - 1) * size + $index + 1 }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="left" header-align="center" label="会话ID" prop="tokenId" />
          <ElTableColumn align="center" header-align="center" label="用户ID" prop="username" />
          <ElTableColumn align="center" header-align="center" label="用户昵称" prop="nickname" />
          <ElTableColumn align="center" header-align="center" label="主机" prop="ipAddress" />
          <ElTableColumn align="center" header-align="center" label="登录地点" prop="loginLocation" />
          <ElTableColumn align="center" header-align="center" label="浏览器" prop="browser" />
          <ElTableColumn align="center" header-align="center" label="操作系统" prop="os" />
          <ElTableColumn align="center" header-align="center" label="登录时间" prop="loginTime" width="180">
            <template #default="{ row }">
              <span>{{ row.loginTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['monitor:online:forceLogout']" icon="el-icon-delete" size="mini" type="text" @click="handleForceLogout(row)">强退</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>

      <Pagination v-show="total > 0" slot="pagination" :limit.sync="size" :page.sync="page" :total="total" />
    </TreeTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      page: 1,
      size: 10,
      // 查询参数
      queryParams: {
        ipAddress: undefined,
        username: undefined,
      },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询登录日志列表
    getList() {
      this.loading = true
      useApiGetOnlineUserList(this.queryParams).then((response) => {
        this.list = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.page = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 强退按钮操作
    handleForceLogout(row) {
      appModal
        .confirm(`是否确认强退名称为"${row.username}"的用户？`)
        .then(() => {
          return useApiForceLogout(row.tokenId)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('强退成功')
        })
        .catch(() => {})
    },
  },
}
</script>
