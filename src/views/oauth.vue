<template>
  <div class="oauth"></div>
</template>

<script>
export default {
  data() {
    return {
      id: this.$route.query.id,
      redirectUrl: this.$route.query.redirectUrl,
    }
  },
  created() {
    this.getCookie()
  },
  methods: {
    async getCookie() {
      if (!this.id) {
        this.$message.error('授权 ID 不能为空')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '授权跳转中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      try {
        useApiGetTokenById(this.id).then((response) => {
          console.log(response)
          this.$message.success('授权成功，正在跳转系统')
          this.$store.commit('SET_TOKEN', response.accessToken || response.token)
          setTimeout(() => {
            if (undefined !== this.redirectUrl && this.redirectUrl !== null && this.redirectUrl !== '') {
              this.$router.push({ path: this.redirectUrl }).catch(() => {})
            } else {
              this.$router.push({ path: '/index' }).catch(() => {})
            }
          }, 500)
        })
      } catch {
        this.$message.error('授权失败，请返回重新授权')
      } finally {
        loading.close()
      }
    },
  },
}
</script>
