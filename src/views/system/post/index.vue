<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="queryParams" size="small">
        <ElFormItem label="岗位编码" prop="postCode">
          <ElInput v-model="queryParams.postCode" clearable placeholder="请输入岗位编码" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="岗位名称" prop="postName">
          <ElInput v-model="queryParams.postName" clearable placeholder="请输入岗位名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="queryParams.status" clearable placeholder="岗位状态">
            <ElOption v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:post:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:post:edit']" :disabled="single" icon="el-icon-edit" plain size="mini" type="success" @click="handleUpdate">修改</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:post:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:post:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>
      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="postList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="岗位ID" prop="postId" width="100" />
          <ElTableColumn align="center" header-align="center" label="岗位编码" prop="postCode" />
          <ElTableColumn align="center" header-align="center" label="岗位名称" prop="postName" />
          <ElTableColumn align="center" header-align="center" label="岗位排序" prop="postSort" />
          <ElTableColumn align="center" header-align="center" label="状态" prop="status">
            <template #default="{ row }">
              <BooleanTag enum-key="EnabledBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="180">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['system:post:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
              <ElButton v-hasPermission="['system:post:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <Pagination v-show="total > 0" slot="pagination" :limit.sync="queryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <div>
      <!-- 添加或修改岗位对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px">
        <ElForm ref="form" label-width="80px" :model="form" :rules="rules">
          <ElFormItem label="岗位名称" prop="postName">
            <ElInput v-model="form.postName" placeholder="请输入岗位名称" />
          </ElFormItem>
          <ElFormItem label="岗位编码" prop="postCode">
            <ElInput v-model="form.postCode" placeholder="请输入编码名称" />
          </ElFormItem>
          <ElFormItem label="岗位顺序" prop="postSort">
            <ElInputNumber v-model="form.postSort" controls-position="right" :min="0" />
          </ElFormItem>
          <ElFormItem label="岗位状态" prop="status">
            <ElRadioGroup v-model="form.status">
              <ElRadio v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="form.remark" placeholder="请输入内容" type="textarea" />
          </ElFormItem>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        postCode: undefined,
        postName: undefined,
        status: true,
      },
      // 表单参数
      form: {
        ...this.form,
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: true,
        remark: undefined,
      },
      // 表单校验
      rules: {
        postName: [{ required: true, message: '岗位名称不能为空', trigger: 'blur' }],
        postCode: [{ required: true, message: '岗位编码不能为空', trigger: 'blur' }],
        postSort: [{ required: true, message: '岗位顺序不能为空', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  created() {
    this.getList()
  },
  methods: {
    // 查询岗位列表
    getList() {
      this.loading = true
      useApiGetPostList(this.queryParams).then((response) => {
        this.postList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: true,
        remark: undefined,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加岗位'
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      const postId = row.postId || this.ids
      useApiGetPost(postId).then((response) => {
        this.form = { ...this.form, ...response }
        this.open = true
        this.title = '修改岗位'
      })
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const id = this.form.postId
          if (id) {
            // 更新
            useApiUpdatePost(id, this.form).then((response) => {
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            // 新增
            useApiAddPost(this.form).then((response) => {
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      const postIds = row.postId || this.ids
      appModal
        .confirm(`是否确认删除岗位 ID 为"${postIds}"的数据项？`)
        .then(() => {
          return useApiDelPost(postIds)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'system/posts/export',
        data: this.queryParams,
        filename: '岗位列表',
      })
    },
  },
}
</script>
