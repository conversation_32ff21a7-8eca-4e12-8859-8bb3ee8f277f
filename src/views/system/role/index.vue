<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" :model="baseQueryParams" size="small">
        <ElFormItem label="角色标识" prop="roleKey">
          <ElInput v-model="baseQueryParams.roleKey" class="w-60" clearable placeholder="请输入角色标识" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="角色名称" prop="roleName">
          <ElInput v-model="baseQueryParams.roleName" class="w-60" clearable placeholder="请输入角色名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="启用状态" prop="status">
          <ElSelect v-model="baseQueryParams.status" class="w-60" clearable placeholder="角色启用状态">
            <ElOption v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="创建时间">
          <ElDatePicker
            v-model="dateRange"
            class="w-60"
            :default-time="['00:00:00', '23:59:59']"
            end-placeholder="结束日期"
            range-separator="-"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></ElDatePicker>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:role:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:role:edit']" :disabled="single" icon="el-icon-edit" plain size="mini" type="success" @click="handleUpdate">修改</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:role:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:role:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>
      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="roleList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="角色ID" prop="roleId" width="60" />
          <ElTableColumn align="center" header-align="center" label="角色标识" prop="roleKey" width="150" />
          <ElTableColumn align="center" header-align="center" label="角色名称" prop="roleName" width="150" />
          <!-- <ElTableColumn align="center" header-align="center" label="显示顺序" prop="roleSort" width="100" /> -->
          <ElTableColumn align="center" header-align="center" label="用户数量" prop="userCount" width="80" />
          <ElTableColumn align="center" header-align="center" label="数据权限" width="170">
            <template #default="{ row }">
              <span>{{ getDataScopeLabel(row.dataScope) }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="启用状态" width="100">
            <template #default="{ row }">
              <ElSwitch v-model="row.status" :active-value="true" :inactive-value="false" @change="handleStatusChange(row)"></ElSwitch>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="180">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <template v-if="row.roleId !== 1">
                <ElButton v-hasPermission="['system:role:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
                <ElButton v-hasPermission="['system:role:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
                <ElButton v-hasPermission="['system:role:edit']" icon="el-icon-circle-check" size="mini" type="text" @click="handleDataScope(row)">数据权限</ElButton>
                <ElButton v-hasPermission="['system:role:edit']" icon="el-icon-user" size="mini" type="text" @click="handleAuthUser(row)">分配用户</ElButton>
              </template>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <Pagination v-show="total > 0" slot="pagination" :limit.sync="baseQueryParams.size" :page.sync="baseQueryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <div>
      <!-- 添加或修改角色配置对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px">
        <ElForm ref="form" label-width="100px" :model="form" :rules="rules">
          <ElFormItem label="角色名称" prop="roleName">
            <ElInput v-model="form.roleName" placeholder="请输入角色名称" />
          </ElFormItem>
          <ElFormItem prop="roleKey">
            <template #label>
              <ElTooltip content="控制器中定义的角色标识，如：@PreAuthorize(`@ss.hasRole('ADMIN')`)" placement="top">
                <i class="el-icon-question"></i>
              </ElTooltip>
              角色标识
            </template>
            <ElInput v-model="form.roleKey" placeholder="请输入角色标识" />
          </ElFormItem>
          <ElFormItem label="角色顺序" prop="roleSort">
            <ElInputNumber v-model="form.roleSort" controls-position="right" :min="0" />
          </ElFormItem>
          <ElFormItem label="启用状态">
            <ElRadioGroup v-model="form.status">
              <ElRadio v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="菜单权限">
            <ElCheckbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</ElCheckbox>
            <ElCheckbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</ElCheckbox>
            <ElCheckbox v-model="form.menuCheckStrictly" @change="handleCheckedTreeConnect($event, 'menu')">父子联动</ElCheckbox>
            <ElTree
              ref="menu"
              :check-strictly="!form.menuCheckStrictly"
              class="tree-border"
              :data="menuOptions"
              empty-text="加载中，请稍候"
              node-key="id"
              :props="defaultProps"
              show-checkbox
            ></ElTree>
          </ElFormItem>
          <ElFormItem label="备注">
            <ElInput v-model="form.remark" placeholder="请输入内容" type="textarea"></ElInput>
          </ElFormItem>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </template>
      </ElDialog>

      <!-- 分配角色数据权限对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="openDataScope" width="500px">
        <ElForm label-width="80px" :model="form">
          <ElFormItem label="角色名称">
            <ElInput v-model="form.roleName" :disabled="true" />
          </ElFormItem>
          <ElFormItem label="角色标识">
            <ElInput v-model="form.roleKey" :disabled="true" />
          </ElFormItem>
          <ElFormItem label="权限范围">
            <ElSelect v-model="form.dataScope" @change="dataScopeSelectChange">
              <ElOption v-for="item in dataScopeOptions" :key="item.value" :label="item.label" :value="item.value"></ElOption>
            </ElSelect>
          </ElFormItem>
          <ElFormItem v-show="form.dataScope === 2" label="数据权限">
            <ElCheckbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</ElCheckbox>
            <ElCheckbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</ElCheckbox>
            <ElCheckbox v-model="form.deptCheckStrictly" @change="handleCheckedTreeConnect($event, 'dept')">父子联动</ElCheckbox>
            <ElTree
              ref="dept"
              :check-strictly="!form.deptCheckStrictly"
              class="tree-border"
              :data="deptOptions"
              default-expand-all
              empty-text="加载中，请稍候"
              node-key="id"
              :props="defaultProps"
              show-checkbox
            ></ElTree>
          </ElFormItem>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitDataScope">确 定</ElButton>
          <ElButton @click="cancelDataScope">取 消</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: '1',
          label: '全部数据权限',
        },
        {
          value: '2',
          label: '自定数据权限',
        },
        {
          value: '3',
          label: '本部门数据权限',
        },
        {
          value: '4',
          label: '本部门及以下数据权限',
        },
        {
          value: '5',
          label: '仅本人数据权限',
        },
      ],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 基础查询参数
      baseQueryParams: {
        page: 1,
        size: 10,
        roleName: undefined,
        roleKey: undefined,
        status: true,
      },
      // 表单参数
      form: {
        ...this.form,
        roleId: undefined,
        roleName: undefined,
        roleKey: undefined,
        roleSort: 0,
        status: true,
        menuIds: [],
        deptIds: [],
        menuCheckStrictly: true,
        deptCheckStrictly: true,
        remark: undefined,
      },
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 表单校验
      rules: {
        roleName: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
        roleKey: [{ required: true, message: '角色标识不能为空', trigger: 'blur' }],
        roleSort: [{ required: true, message: '角色顺序不能为空', trigger: 'blur' }],
      },
    }
  },
  computed: {
    queryParams() {
      return {
        ...this.baseQueryParams,
        beginTime: this.dateRange?.[0],
        endTime: this.dateRange?.[1],
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询角色列表
    async getList(parameters = this.queryParams) {
      try {
        this.loading = true
        const response = await useApiGetRoleList(parameters)

        // 直接使用接口返回的 userCount，无需额外计算
        this.roleList = response.records // 使用后端返回的数据，包含 userCount
        this.total = response.total
      } catch (error) {
        console.error('Error fetching role list:', error)
      } finally {
        this.loading = false
      }
    },
    // 获取数据权限标签
    getDataScopeLabel(dataScope) {
      const option = this.dataScopeOptions.find((item) => item.value === dataScope)
      return option ? option.label : '未知'
    },
    // 数组排序
    sortByKey(array, key) {
      return array.sort((a, b) => {
        const A = a[key]
        const B = b[key]

        // 使用 localeCompare 进行字符串比较，确保正确排序
        return A.localeCompare(B)
      })
    },
    // 查询菜单树结构
    getMenuTreeselect() {
      useApiGetMenuTreeselect().then((response) => {
        this.menuOptions = response
      })
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      const checkedKeys = this.$refs.menu.getCheckedKeys()
      // 半选中的菜单节点
      const halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys()
      checkedKeys.unshift(...halfCheckedKeys)
      return checkedKeys
    },
    // 所有部门节点数据
    getDeptAllCheckedKeys() {
      // 目前被选中的部门节点
      const checkedKeys = this.$refs.dept.getCheckedKeys()
      // 半选中的部门节点
      const halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys()
      checkedKeys.unshift(...halfCheckedKeys)
      return checkedKeys
    },
    // 根据角色 ID 查询菜单树结构
    async getRoleMenuTreeselect(roleId) {
      const response = await useApiGetMenuTreeselectByRole(roleId)
      this.menuOptions = response.menus
      return response
    },
    // 根据角色 ID 查询部门树结构
    async getDeptTree(roleId) {
      const response = await useApiGetRoleDeptTree(roleId)
      this.deptOptions = response.depts
      return response
    },
    // 角色启用状态修改
    handleStatusChange(row) {
      const text = row.status ? '启用' : '停用'
      appModal
        .confirm(`确认要"${text}""${row.roleName}"角色吗？`)
        .then(() => {
          return useApiUpdateRole(row.roleId, { ...this.form, status: row.status })
        })
        .then(() => {
          appModal.msgSuccess(`${text}成功`)
        })
        .catch(() => {
          row.status = !row.status
        })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false
      this.reset()
    },
    // 表单重置
    reset() {
      if (this.$refs.menu !== undefined) {
        this.$refs.menu.setCheckedKeys([])
      }
      this.menuExpand = false
      this.menuNodeAll = false
      this.deptExpand = true
      this.deptNodeAll = false
      this.form = {
        roleId: undefined,
        roleName: undefined,
        roleKey: undefined,
        roleSort: 0,
        status: true,
        menuIds: [],
        deptIds: [],
        menuCheckStrictly: true,
        deptCheckStrictly: true,
        remark: undefined,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList({
        ...this.queryParams,
        page: 1,
      })
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.roleId)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'handleDataScope': {
          this.handleDataScope(row)
          break
        }
        case 'handleAuthUser': {
          this.handleAuthUser(row)
          break
        }
        default: {
          break
        }
      }
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type === 'menu') {
        const treeList = this.menuOptions
        for (const element of treeList) {
          this.$refs.menu.store.nodesMap[element.id].expanded = value
        }
      } else if (type === 'dept') {
        const treeList = this.deptOptions
        for (const element of treeList) {
          this.$refs.dept.store.nodesMap[element.id].expanded = value
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type === 'menu') {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : [])
      } else if (type === 'dept') {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : [])
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type === 'menu') {
        this.form.menuCheckStrictly = !!value
      } else if (type === 'dept') {
        this.form.deptCheckStrictly = !!value
      }
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      this.getMenuTreeselect()
      this.open = true
      this.title = '添加角色'
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      const roleId = row.roleId || this.ids
      const roleMenu = this.getRoleMenuTreeselect(roleId)
      useApiGetRole(roleId).then((response) => {
        this.form = { ...this.form, ...response }
        this.open = true
        this.$nextTick(() => {
          roleMenu.then((response) => {
            const checkedKeys = response.checkedKeys
            for (const v of checkedKeys) {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false)
              })
            }
          })
        })
        this.title = '修改角色'
      })
    },
    // 选择角色权限范围触发
    dataScopeSelectChange(value) {
      if (value !== '2') {
        this.$refs.dept.setCheckedKeys([])
      }
    },
    // 分配数据权限操作
    handleDataScope(row) {
      this.reset()
      const deptTreeSelect = this.getDeptTree(row.roleId)
      useApiGetRole(row.roleId).then((response) => {
        this.form = { ...this.form, ...response }
        this.openDataScope = true
        this.$nextTick(() => {
          deptTreeSelect.then((response) => {
            this.$refs.dept.setCheckedKeys(response.checkedKeys)
          })
        })
        this.title = '分配数据权限'
      })
    },
    // 分配用户操作
    handleAuthUser(row) {
      const { roleName, roleId } = row
      this.$router.push({
        path: `/system/role-auth/user/${roleId}`,
        query: {
          tagName: roleName,
        },
      })
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.roleKey = this.form.roleKey.trim().toUpperCase() // 大写
          if (this.form.roleId === undefined) {
            this.form.menuIds = this.getMenuAllCheckedKeys()
            useApiAddRole(this.form).then(() => {
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          } else {
            this.form.menuIds = this.getMenuAllCheckedKeys()
            useApiUpdateRole(this.form.roleId, this.form).then(() => {
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 提交按钮（数据权限）
    submitDataScope() {
      if (this.form.roleId !== undefined) {
        this.form.deptIds = this.getDeptAllCheckedKeys()
        useApiUpdateRole(this.form.roleId, this.form).then(() => {
          appModal.msgSuccess('修改成功')
          this.openDataScope = false
          this.getList()
        })
      }
    },
    // 删除按钮操作
    handleDelete(row) {
      const roleIds = row.roleId || this.ids
      appModal
        .confirm(`是否确认删除角色 ID 为"${roleIds}"的数据项？`)
        .then(() => {
          // 判断是单条删除还是批量删除
          return Array.isArray(roleIds) ? useApiDelRoles(roleIds) : useApiDelRole(roleIds)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'system/roles/export',
        data: this.queryParams,
        filename: '角色列表',
      })
    },
  },
}
</script>
