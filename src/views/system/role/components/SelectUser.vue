<template>
  <!-- 授权用户 -->
  <ElDialog append-to-body title="选择用户" top="5vh" :visible.sync="visible" width="800px">
    <ElForm ref="queryForm" :inline="true" :model="queryParams" size="small">
      <ElFormItem label="用户名称" prop="username">
        <ElInput v-model="queryParams.username" clearable placeholder="请输入用户名称" @keyup.enter="handleQuery" />
      </ElFormItem>
      <ElFormItem label="手机号码" prop="mobile">
        <ElInput v-model="queryParams.mobile" clearable placeholder="请输入手机号码" @keyup.enter="handleQuery" />
      </ElFormItem>
      <ElFormItem>
        <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
        <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <ElRow>
      <ElTable ref="table" :data="userList" height="260px" @row-click="clickRow" @selection-change="handleSelectionChange">
        <ElTableColumn align="center" header-align="center" type="selection" width="55"></ElTableColumn>
        <ElTableColumn align="center" header-align="center" label="用户名称" prop="username" :show-overflow-tooltip="true" />
        <ElTableColumn align="center" header-align="center" label="用户昵称" prop="nickname" :show-overflow-tooltip="true" />
        <ElTableColumn align="center" header-align="center" label="邮箱" prop="email" :show-overflow-tooltip="true" />
        <ElTableColumn align="center" header-align="center" label="手机" prop="mobile" :show-overflow-tooltip="true" />
        <ElTableColumn align="center" header-align="center" label="状态" prop="status">
          <template slot-scope="scope">
            <BooleanTag enum-key="EnabledBoolean" :value="scope.row.status" />
          </template>
        </ElTableColumn>
        <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </ElTableColumn>
      </ElTable>
      <Pagination v-show="total > 0" :limit.sync="queryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </ElRow>
    <div slot="footer" class="dialog-footer">
      <ElButton type="primary" @click="handleSelectUser">确 定</ElButton>
      <ElButton @click="visible = false">取 消</ElButton>
    </div>
  </ElDialog>
</template>

<script>
export default {
  props: {
    // 角色 ID
    roleId: {
      type: [Number, String],
    },
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      userIds: [],
      // 总条数
      total: 0,
      // 未授权用户数据
      userList: [],
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        roleId: undefined,
        username: undefined,
        mobile: undefined,
      },
    }
  },
  computed: {},
  methods: {
    // 显示弹框
    show() {
      this.queryParams.roleId = this.roleId
      this.getList()
      this.visible = true
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userIds = selection.map((item) => item.userId)
    },
    // 查询表数据
    getList() {
      const { roleId, ...queryParameters } = this.queryParams
      useApiGetRoleUserCandidates(roleId, queryParameters).then((response) => {
        this.userList = response.records
        this.total = response.total
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 选择授权用户操作
    handleSelectUser() {
      const roleId = this.queryParams.roleId
      const userIds = this.userIds
      if (userIds.length === 0) {
        appModal.msgError('请选择要分配的用户')
        return
      }
      useApiAddRoleUsers(roleId, userIds).then((response) => {
        appModal.msgSuccess('操作成功')
        this.visible = false
        this.$emit('ok')
      })
    },
  },
}
</script>
