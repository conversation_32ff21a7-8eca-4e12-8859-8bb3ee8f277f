<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" :model="queryParams" size="small">
        <ElFormItem label="用户名称" prop="username">
          <ElInput v-model="queryParams.username" class="w-60" clearable placeholder="请输入用户名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="手机号码" prop="mobile">
          <ElInput v-model="queryParams.mobile" class="w-60" clearable placeholder="请输入手机号码" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <ElRow slot="actionLeftBtn" class="mb-2" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:role:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="openSelectUser">添加用户</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:role:remove']" :disabled="multiple" icon="el-icon-circle-close" plain size="mini" type="danger" @click="cancelAuthUserAll"
            >批量取消授权</ElButton
          >
        </ElCol>
        <ElCol :span="1.5">
          <ElButton icon="el-icon-close" plain size="mini" type="warning" @click="handleClose">关闭</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>

      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="userList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="用户名称" prop="username" />
          <ElTableColumn align="center" header-align="center" label="用户昵称" prop="nickname" />
          <ElTableColumn align="center" header-align="center" label="邮箱" prop="email" />
          <ElTableColumn align="center" header-align="center" label="手机" prop="mobile" />
          <ElTableColumn align="center" header-align="center" label="状态" prop="status">
            <template #default="{ row }">
              <BooleanTag enum-key="EnabledBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="180">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['system:role:remove']" icon="el-icon-circle-close" size="mini" type="text" @click="cancelAuthUser(row)">取消授权</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>

      <Pagination v-show="total > 0" slot="pagination" :limit.sync="queryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <SelectUser ref="select" :role-id="queryParams.roleId" @ok="handleQuery" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中用户组
      userIds: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 查询参数
      queryParams: {
        page: 1,
        size: 15,
        roleId: undefined,
        username: undefined,
        mobile: undefined,
      },
    }
  },
  computed: {
    roleId() {
      return this.$route.params?.roleId
    },
  },
  created() {
    if (this.roleId) {
      this.queryParams.roleId = this.roleId
      this.getList()
    }
  },
  methods: {
    // 查询授权用户列表
    getList() {
      this.loading = true
      const { roleId, ...queryParameters } = this.queryParams
      useApiGetRoleUsers(roleId, queryParameters).then((response) => {
        this.userList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 返回按钮
    handleClose() {
      const object = { path: '/system/role' }
      appTab.closeOpenPage(object)
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userIds = selection.map((item) => item.userId)
      this.multiple = selection.length === 0
    },
    // 打开授权用户表弹窗
    openSelectUser() {
      this.$refs.select.show()
    },
    // 取消授权按钮操作
    cancelAuthUser(row) {
      const roleId = this.queryParams.roleId
      appModal
        .confirm(`确认要取消该用户"${row.username}"角色吗？`)
        .then(() => {
          return useApiRemoveRoleUsers(roleId, [row.userId])
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('取消授权成功')
        })
        .catch(() => {})
    },
    // 批量取消授权按钮操作
    cancelAuthUserAll(row) {
      const roleId = this.queryParams.roleId
      const userIds = this.userIds
      appModal
        .confirm('是否取消选中用户授权数据项？')
        .then(() => {
          return useApiRemoveRoleUsers(roleId, userIds)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('取消授权成功')
        })
        .catch(() => {})
    },
  },
}
</script>
