<template>
  <ElForm ref="form" label-width="80px" :model="user" :rules="rules">
    <ElFormItem label="旧密码" prop="oldPassword">
      <ElInput v-model="user.oldPassword" placeholder="请输入旧密码" show-password type="password" />
    </ElFormItem>
    <ElFormItem label="新密码" prop="newPassword">
      <ElInput v-model="user.newPassword" placeholder="请输入新密码" show-password type="password" />
    </ElFormItem>
    <ElFormItem label="确认密码" prop="confirmPassword">
      <ElInput v-model="user.confirmPassword" placeholder="请确认新密码" show-password type="password" />
    </ElFormItem>
    <ElFormItem>
      <ElButton size="mini" type="primary" @click="submit">保存</ElButton>
      <ElButton size="mini" type="danger" @click="close">关闭</ElButton>
    </ElFormItem>
  </ElForm>
</template>

<script>
export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword === value) {
        callback()
      } else {
        callback(new Error('两次输入的密码不一致'))
      }
    }
    return {
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined,
      },
      // 表单校验
      rules: {
        oldPassword: [{ required: true, message: '旧密码不能为空', trigger: 'blur' }],
        newPassword: [
          { required: true, message: '新密码不能为空', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
          { pattern: /^[^"'<>\\|]+$/, message: String.raw`不能包含非法字符：< > " ' \ |`, trigger: 'blur' },
        ],
        confirmPassword: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          { required: true, validator: equalToPassword, trigger: 'blur' },
        ],
      },
    }
  },

  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          useApiUpdateUserPassword(this.user.oldPassword, this.user.newPassword).then((response) => {
            appModal.msgSuccess('修改成功')
          })
        }
      })
    },
    close() {
      appTab.closePage()
    },
  },
}
</script>
