<template>
  <div>
    <div class="user-info-head" @click="editCropper()"><img class="h-120px rounded-full w-120px" :src="options.img" title="点击上传头像" /></div>
    <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="800px" @close="closeDialog" @opened="modalOpened">
      <ElRow>
        <ElCol :md="12" :style="{ height: '350px' }" :xs="24">
          <VueCropper
            v-if="visible"
            ref="cropper"
            :auto-crop="options.autoCrop"
            :auto-crop-height="options.autoCropHeight"
            :auto-crop-width="options.autoCropWidth"
            :fixed-box="options.fixedBox"
            :img="options.img"
            :info="true"
            :output-type="options.outputType"
            @realTime="realTime"
          />
        </ElCol>
        <ElCol :md="12" :style="{ height: '350px' }" :xs="24">
          <div class="-translate-x-1/2 -translate-y-1/2 absolute h-200px left-1/2 overflow-hidden rounded-full shadow-[0_0_4px_#ccc] top-1/2 transform w-200px">
            <img :src="previews.url" :style="previews.img" />
          </div>
        </ElCol>
      </ElRow>
      <br />
      <ElRow>
        <ElCol :lg="2" :sm="3" :xs="3">
          <ElUpload action="#" :before-upload="beforeUpload" :http-request="requestUpload" :show-file-list="false">
            <ElButton size="small">
              选择
              <i class="el-icon--right el-icon-upload"></i>
            </ElButton>
          </ElUpload>
        </ElCol>
        <ElCol :lg="{ span: 1, offset: 2 }" :sm="2" :xs="2">
          <ElButton icon="el-icon-plus" size="small" @click="changeScale(1)"></ElButton>
        </ElCol>
        <ElCol :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <ElButton icon="el-icon-minus" size="small" @click="changeScale(-1)"></ElButton>
        </ElCol>
        <ElCol :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <ElButton icon="el-icon-refresh-left" size="small" @click="rotateLeft()"></ElButton>
        </ElCol>
        <ElCol :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <ElButton icon="el-icon-refresh-right" size="small" @click="rotateRight()"></ElButton>
        </ElCol>
        <ElCol :lg="{ span: 2, offset: 6 }" :sm="2" :xs="2">
          <ElButton size="small" type="primary" @click="uploadImg()">提 交</ElButton>
        </ElCol>
      </ElRow>
    </ElDialog>
  </div>
</template>

<script>
import store from '@/store'
import { VueCropper } from 'vue-cropper'

export default {
  components: { VueCropper },

  props: {
    user: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 是否显示弹出层
      open: false,
      // 是否显示 cropper
      visible: false,
      // 弹出层标题
      title: '修改头像',
      options: {
        img: this.user.avatar, // 裁剪图片的地址
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 200, // 默认生成截图框宽度
        autoCropHeight: 200, // 默认生成截图框高度
        fixedBox: true, // 固定截图框大小 不允许改变
        outputType: 'png', // 默认生成截图为 PNG 格式
        filename: 'avatar', // 文件名称
      },
      previews: {},
      resizeHandler: undefined,
    }
  },
  computed: {
    // ...mapGetters(['user']),
  },
  beforeDestroy() {
    if (this.resizeHandler) window.removeEventListener('resize', this.resizeHandler)
  },
  methods: {
    // 编辑头像
    editCropper() {
      this.open = true
    },
    // 打开弹出层结束时的回调
    modalOpened() {
      this.visible = true
      if (!this.resizeHandler) this.resizeHandler = this.refresh
      this.refresh()
      window.addEventListener('resize', this.resizeHandler)
    },
    // 刷新组件
    refresh() {
      this.$refs.cropper.refresh()
    },
    // 覆盖默认的上传行为
    requestUpload() {},
    // 向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft()
    },
    // 向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight()
    },
    // 图片缩放
    changeScale(number_ = 1) {
      this.$refs.cropper.changeScale(number_)
    },
    // 上传预处理
    beforeUpload(file) {
      if (file.type.includes('image/')) {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.addEventListener('load', () => {
          this.options.img = reader.result
          this.options.filename = file.name
        })
      } else {
        appModal.msgError('文件格式错误，请上传图片类型，如：JPG，PNG 后缀的文件。')
      }
    },
    // 上传图片
    uploadImg() {
      this.$refs.cropper.getCropBlob((data) => {
        const formData = new FormData()
        formData.append('avatarfile', data, this.options.filename)
        useApiUploadAvatar(formData).then((response) => {
          this.open = false
          this.options.img = useEnvironment.VITE_BASE_API + response.imgUrl
          store.commit('SET_USER', { ...this.user, avatar: this.options.img })
          appModal.msgSuccess('修改成功')
          this.visible = false
        })
      })
    },
    // 实时预览
    realTime(data) {
      this.previews = data
    },
    // 关闭窗口
    closeDialog() {
      this.options.img = store.getters.avatar
      this.visible = false
      window.removeEventListener('resize', this.resizeHandler)
    },
  },
}
</script>

<style lang="scss" scoped>
.user-info-head {
  position: relative;
  display: inline-block;
  height: 120px;
  margin-bottom: 20px;
}

.user-info-head:hover:after {
  content: '+';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #eee;
  background: rgba(0, 0, 0, 0.5);
  font-size: 24px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  line-height: 110px;
  border-radius: 50%;
}
</style>
