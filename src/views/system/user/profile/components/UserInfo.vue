<template>
  <ElForm ref="form" label-width="80px" :model="form" :rules="rules">
    <ElFormItem label="用户昵称" prop="nickname">
      <ElInput v-model="form.nickname" maxlength="30" />
    </ElFormItem>
    <ElFormItem label="手机号码" prop="mobile">
      <ElInput v-model="form.mobile" maxlength="11" />
    </ElFormItem>
    <ElFormItem label="邮箱" prop="email">
      <ElInput v-model="form.email" maxlength="50" />
    </ElFormItem>
    <ElFormItem label="性别">
      <ElRadioGroup v-model="form.gender">
        <ElRadio v-for="opt in enumOptions('GenderEnum')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
      </ElRadioGroup>
    </ElFormItem>
    <ElFormItem>
      <ElButton size="mini" type="primary" @click="submit">保存</ElButton>
      <ElButton size="mini" type="danger" @click="close">关闭</ElButton>
    </ElFormItem>
  </ElForm>
</template>

<script>
export default {
  props: {
    user: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      form: {
        nickname: '',
        mobile: '',
        email: '',
        gender: '',
      },
      // 表单校验
      rules: {
        nickname: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
        email: [
          { required: true, message: '邮箱地址不能为空', trigger: 'blur' },
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change'],
          },
        ],
        // mobile: [
        //   { required: true, message: '手机号码不能为空', trigger: 'blur' },
        //   {
        //     pattern: /^1[3-9]\d{9}$/,
        //     message: '请输入正确的手机号码',
        //     trigger: 'blur',
        //   },
        // ],
      },
    }
  },
  watch: {
    user: {
      handler(user) {
        if (user) {
          this.form = { ...user }
        }
      },
      immediate: true,
    },
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          useApiUpdateUserProfile(this.form).then((response) => {
            appModal.msgSuccess('修改成功')
            // this.user.mobile = this.form.mobile
            // this.user.email = this.form.email
            Object.assign(this.user, response)
          })
        }
      })
    },
    close() {
      appTab.closePage()
    },
  },
}
</script>
