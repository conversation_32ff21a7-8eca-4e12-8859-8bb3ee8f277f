<template>
  <div class="p-4">
    <ElRow :gutter="20">
      <ElCol :span="8" :xs="24">
        <ElCard class="box-card">
          <template #header>
            <span>个人信息</span>
          </template>
          <div>
            <div class="mb-4 text-center">
              <UserAvatar :user="user" />
              <div class="mt-3">
                <div class="font-medium text-lg">{{ user.nickname || user.username }}</div>
                <div class="mt-1 text-gray-500 text-sm">{{ user.username }}</div>
              </div>
            </div>
            <ul class="space-y-1 text-sm">
              <li class="border-b flex items-center justify-between py-3">
                <span class="text-gray-600"><i class="el-icon-user mr-2"></i>用户账号</span>
                <span class="font-medium">{{ user.username }}</span>
              </li>
              <li class="border-b flex items-center justify-between py-3">
                <span class="text-gray-600"><i class="el-icon-mobile-phone mr-2"></i>手机号码</span>
                <span class="font-medium">{{ user.mobile }}</span>
              </li>
              <li class="border-b flex items-center justify-between py-3">
                <span class="text-gray-600"><i class="el-icon-message mr-2"></i>用户邮箱</span>
                <span class="font-medium">{{ user.email }}</span>
              </li>
              <li class="border-b flex items-center justify-between py-3">
                <span class="text-gray-600"><i class="el-icon-office-building mr-2"></i>所属部门</span>
                <span v-if="user.dept" class="font-medium">{{ user.dept.deptName }}</span>
                <span v-else class="text-gray-400">暂无</span>
              </li>
              <li class="border-b flex items-center justify-between py-3">
                <span class="text-gray-600"><i class="el-icon-coordinate mr-2"></i>所属角色</span>
                <div class="flex flex-wrap gap-1 justify-end max-w-32">
                  <ElTag v-for="role in user.roles" :key="role.roleId" class="mb-1" size="small" type="primary">
                    {{ role.roleName }}
                  </ElTag>
                  <span v-if="!user.roles || user.roles.length === 0" class="text-gray-400">暂无</span>
                </div>
              </li>
              <li v-if="user.posts && user.posts.length > 0" class="border-b flex items-center justify-between py-3">
                <span class="text-gray-600"><i class="el-icon-suitcase mr-2"></i>所属岗位</span>
                <div class="flex flex-wrap gap-1 justify-end max-w-32">
                  <ElTag v-for="post in user.posts" :key="post.postId" class="mb-1" size="small" type="success">
                    {{ post.postName }}
                  </ElTag>
                </div>
              </li>
              <li class="flex items-center justify-between py-3">
                <span class="text-gray-600"><i class="el-icon-date mr-2"></i>创建日期</span>
                <span class="font-medium">{{ user.createTime }}</span>
              </li>
            </ul>
          </div>
        </ElCard>
      </ElCol>
      <ElCol :span="16" :xs="24">
        <ElCard>
          <template #header>
            <span>基本资料</span>
          </template>
          <ElTabs v-model="activeTab">
            <ElTabPane label="基本资料" name="userinfo">
              <UserInfo :user="user" />
            </ElTabPane>
            <ElTabPane label="修改密码" name="resetPassword">
              <ResetPassword />
            </ElTabPane>
          </ElTabs>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<script>
export default {
  data() {
    return {
      user: {},
      activeTab: 'userinfo',
    }
  },
  created() {
    this.getUser()
  },
  methods: {
    getUser() {
      useApiGetUserProfile().then((response) => {
        this.user = response
      })
    },
  },
}
</script>
