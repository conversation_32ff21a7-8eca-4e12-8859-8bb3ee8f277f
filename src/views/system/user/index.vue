<template>
  <div>
    <TreeTable>
      <!-- 部门数据 -->
      <div slot="treeSearch" class="head-container">
        <ElInput v-model="deptName" clearable placeholder="请输入部门名称" prefix-icon="el-icon-search" size="small" />
      </div>
      <div slot="tree" class="head-container">
        <ElTree
          ref="tree"
          :data="deptTree"
          default-expand-all
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          highlight-current
          node-key="id"
          :props="defaultProps"
          @node-click="handleNodeClick"
        />
      </div>
      <!-- 用户数据 -->
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="baseQueryParams" size="small">
        <ElFormItem label="用户名称" prop="username">
          <ElInput v-model="baseQueryParams.username" class="w-60" clearable placeholder="请输入用户名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="手机号码" prop="mobile">
          <ElInput v-model="baseQueryParams.mobile" class="w-60" clearable placeholder="请输入手机号码" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="baseQueryParams.status" class="w-60" clearable placeholder="用户状态">
            <ElOption v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="创建时间">
          <ElDatePicker
            v-model="dateRange"
            class="w-60"
            :default-time="['00:00:00', '23:59:59']"
            end-placeholder="结束日期"
            range-separator="-"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></ElDatePicker>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:user:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:user:edit']" :disabled="single" icon="el-icon-edit" plain size="mini" type="success" @click="handleUpdate">修改</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:user:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:user:import']" icon="el-icon-upload2" plain size="mini" type="info" @click="handleImport">导入</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:user:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :columns="columns" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>

      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="userList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="50" />
          <ElTableColumn v-if="columns[0].visible" key="userId" align="center" header-align="center" label="用户ID" prop="userId" />
          <ElTableColumn v-if="columns[1].visible" key="username" align="center" header-align="center" label="用户名称" prop="username" />
          <ElTableColumn v-if="columns[2].visible" key="nickname" align="center" header-align="center" label="用户昵称" prop="nickname" />
          <ElTableColumn v-if="columns[3].visible" key="deptName" align="center" header-align="center" label="部门" prop="dept.deptName" />
          <ElTableColumn v-if="columns[4].visible" key="mobile" align="center" header-align="center" label="手机号码" prop="mobile" width="120" />
          <ElTableColumn v-if="columns[5].visible" key="status" align="center" header-align="center" label="状态">
            <template #default="{ row }">
              <ElSwitch v-model="row.status" :active-value="true" :inactive-value="false" @change="handleStatusChange(row)"></ElSwitch>
            </template>
          </ElTableColumn>
          <ElTableColumn v-if="columns[6].visible" align="center" header-align="center" label="创建时间" prop="createTime" width="160">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作" width="160">
            <template #default="{ row }">
              <template v-if="row.userId !== 1">
                <ElButton v-hasPermission="['system:user:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
                <ElButton v-hasPermission="['system:user:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
                <ElDropdown v-hasPermission="['system:user:resetPwd', 'system:user:edit']" size="mini" @command="(command) => handleCommand(command, row)">
                  <ElButton icon="el-icon-d-arrow-right" size="mini" type="text">更多</ElButton>
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem v-hasPermission="['system:user:resetPwd']" command="handleResetPwd" icon="el-icon-key"> 重置密码</ElDropdownItem>
                      <ElDropdownItem v-hasPermission="['system:user:edit']" command="handleAuthRole" icon="el-icon-circle-check">分配角色</ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </template>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <Pagination v-show="total > 0" slot="pagination" :limit.sync="baseQueryParams.size" :page.sync="baseQueryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <div>
      <!-- 添加或修改用户配置对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="600px">
        <ElForm ref="form" label-width="80px" :model="form" :rules="rules">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="用户昵称" prop="nickname">
                <ElInput v-model="form.nickname" maxlength="30" placeholder="请输入用户昵称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="归属部门" prop="deptId">
                <Treeselect v-model="form.deptId" :options="deptTree" placeholder="请选择归属部门" :show-count="true" />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="手机号码" prop="mobile">
                <ElInput v-model="form.mobile" maxlength="11" placeholder="请输入手机号码" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="邮箱" prop="email">
                <ElInput v-model="form.email" maxlength="50" placeholder="请输入邮箱" />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem v-if="form.userId === undefined" label="用户名称" prop="username">
                <ElInput v-model="form.username" maxlength="30" placeholder="请输入用户名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem v-if="form.userId === undefined" label="用户密码" prop="password">
                <ElInput v-model="form.password" maxlength="20" placeholder="请输入用户密码" show-password type="password" />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="用户性别">
                <ElSelect v-model="form.gender" placeholder="请选择性别">
                  <ElOption v-for="opt in enumOptions('GenderEnum')" :key="opt.value" :label="opt.label" :value="opt.value"></ElOption>
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="状态">
                <ElRadioGroup v-model="form.status">
                  <ElRadio v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="岗位">
                <ElSelect v-model="form.postIds" multiple placeholder="请选择岗位">
                  <ElOption v-for="item in postOptions || []" :key="item.value" :disabled="item.disabled" :label="item.label" :value="item.value"></ElOption>
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="角色">
                <ElSelect v-model="form.roleIds" multiple placeholder="请选择角色">
                  <ElOption v-for="item in roleOptions || []" :key="item.value" :disabled="item.disabled" :label="item.label" :value="item.value"></ElOption>
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="24">
              <ElFormItem label="备注">
                <ElInput v-model="form.remark" placeholder="请输入内容" type="textarea"></ElInput>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </template>
      </ElDialog>

      <!-- 用户导入对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="upload.title" :visible.sync="upload.open" width="400px">
        <ElUpload
          ref="upload"
          accept=".xlsx, .xls"
          :action="`${upload.url}?updateSupport=${upload.updateSupport}`"
          :auto-upload="false"
          :disabled="upload.isUploading"
          drag
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip text-center"><ElCheckbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据</div>
            <span>仅允许导入 xls、xlsx 格式文件。</span>
            <ElLink class="align-baseline text-sm" type="primary" :underline="false" @click="importTemplate">下载模板</ElLink>
          </template>
        </ElUpload>
        <template #footer>
          <ElButton type="primary" @click="submitFileForm">确 定</ElButton>
          <ElButton @click="upload.open = false">取 消</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: undefined,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptTree: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: '963852741',
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {
        userId: undefined,
        deptId: undefined,
        username: undefined,
        nickname: undefined,
        password: undefined,
        mobile: undefined,
        email: undefined,
        gender: undefined,
        status: true,
        remark: undefined,
        postIds: [],
        roleIds: [],
      },
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: `Bearer ${useMyCookies.get('token')}` },
        // 上传的地址
        url: `${useEnvironment.VITE_BASE_API}/system/user/importData`,
      },
      // 基础查询参数
      baseQueryParams: {
        page: 1,
        size: 10,
        username: undefined,
        mobile: undefined,
        status: true,
        deptId: undefined,
      },
      // 列信息
      columns: [
        { key: 0, label: `用户 ID`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true },
      ],
      // 表单校验
      rules: {
        username: [
          { required: true, message: '用户名称不能为空', trigger: 'blur' },
          { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' },
        ],
        nickname: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
        password: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
          { pattern: /^[^"'<>\\|]+$/, message: String.raw`不能包含非法字符：< > " ' \ |`, trigger: 'blur' },
        ],
        email: [
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change'],
          },
        ],
        // mobile: [
        //   {
        //     pattern: /^1[3-9|]\d{9}$/,
        //     message: '请输入正确的手机号码',
        //     trigger: 'blur',
        //   },
        // ],
      },
    }
  },
  computed: {
    queryParams() {
      return {
        ...this.baseQueryParams,
        beginTime: this.dateRange?.[0],
        endTime: this.dateRange?.[1],
      }
    },
  },
  watch: {
    // 根据名称筛选部门树
    deptName(value) {
      this.$refs.tree.filter((element) => value(element))
    },
  },
  created() {
    this.getList()
    this.getDeptTree()
    useApiGetConfigKey('initPassword').then((response) => {
      if (!response) this.initPassword = response
    })
  },
  methods: {
    // 查询用户列表
    getList(parameters = this.queryParams) {
      this.loading = true
      useApiGetUserList(parameters).then((response) => {
        this.userList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 查询部门下拉树结构
    getDeptTree() {
      useApiGetDeptTreeSelect().then((response) => {
        this.deptTree = response
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.includes(value)
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.getList({
        ...this.queryParams,
        deptId: data.id,
        page: 1,
      })
    },
    // 用户状态修改
    handleStatusChange(row) {
      const text = row.status ? '启用' : '停用'
      appModal
        .confirm(`确认要"${text}""${row.username}"用户吗？`)
        .then(() => {
          return useApiUpdateUserStatus(row.userId, row.status)
        })
        .then(() => {
          appModal.msgSuccess(`${text}成功`)
        })
        .catch(() => {
          row.status = !row.status
        })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        username: undefined,
        nickname: undefined,
        password: undefined,
        mobile: undefined,
        email: undefined,
        gender: '0',
        status: true,
        remark: undefined,
        postIds: [],
        roleIds: [],
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList({
        ...this.queryParams,
        page: 1,
      })
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.$refs.tree.setCurrentKey()
      this.getList({
        ...this.queryParams,
        deptId: undefined,
        page: 1,
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.userId)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'handleResetPassword': {
          this.handleResetPassword(row)
          break
        }
        case 'handleAuthRole': {
          this.handleAuthRole(row)
          break
        }
        default: {
          break
        }
      }
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      Promise.all([useApiGetPostOptions(), useApiGetRoleOptions()]).then(([posts, roles]) => {
        this.postOptions = posts
        this.roleOptions = roles
        this.open = true
        this.title = '添加用户'
        this.form.password = this.initPassword
      })
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      const userId = row.userId || this.ids
      Promise.all([useApiGetUser(userId), useApiGetPostOptions(), useApiGetRoleOptions()]).then(([response, posts, roles]) => {
        this.form = { ...response }
        this.postOptions = posts
        this.roleOptions = roles
        this.$set(this.form, 'postIds', response.postIds)
        this.$set(this.form, 'roleIds', response.roleIds)
        this.open = true
        this.title = '修改用户'
        this.form.password = ''
      })
    },
    // 重置密码按钮操作
    handleResetPassword(row) {
      this.$prompt(`请输入"${row.username}"的新密码`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: '用户密码长度必须介于 5 和 20 之间',
      })
        .then(({ value }) => {
          useApiResetUserPassword(row.userId, value).then((response) => {
            appModal.msgSuccess(`修改成功，新密码是：${value}`)
          })
        })
        .catch(() => {})
    },
    // 分配角色操作
    handleAuthRole(row) {
      const userId = row.userId
      this.$router.push(`/system/user-auth/role/${userId}`)
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.userId === undefined) {
            useApiAddUser(this.form).then((response) => {
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          } else {
            useApiUpdateUser(this.form.userId, this.form).then((response) => {
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      const userIds = row.userId || this.ids
      appModal
        .confirm(`是否确认删除用户 ID 为"${userIds}"的数据项？`)
        .then(() => {
          return useApiDelUser(userIds)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'system/user/export',
        data: this.queryParams,
        filename: '用户列表',
      })
    },
    // 导入按钮操作
    handleImport() {
      this.upload.title = '用户导入'
      this.upload.open = true
    },
    // 下载模板操作
    importTemplate() {
      useDownloader.xlsx({
        url: 'system/user/importTemplate',
        data: {},
        filename: '用户模板',
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(`<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>${'操作成功'}</div>`, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
  },
}
</script>
