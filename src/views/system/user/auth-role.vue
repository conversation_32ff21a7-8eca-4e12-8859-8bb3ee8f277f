<template>
  <div>
    <TreeTable>
      <!-- 基本信息作为顶部“搜索区域”展示（统一到各页布局） -->
      <div slot="tableSearch">
        <h4 class="border-b border-gray-300 mb-6.25 mt-2 mx-2.5 pb-1.25 text-base" :style="{ color: $store.state.settings.theme }">基本信息</h4>
        <ElForm ref="form" label-width="80px" :model="form">
          <ElRow>
            <ElCol :offset="2" :span="8">
              <ElFormItem label="用户昵称" prop="nickname">
                <ElInput v-model="form.nickname" disabled />
              </ElFormItem>
            </ElCol>
            <ElCol :offset="2" :span="8">
              <ElFormItem label="登录账号" prop="username">
                <ElInput v-model="form.username" disabled />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
      </div>

      <!-- 操作按钮区域 -->
      <ElRow slot="actionLeftBtn" class="mb-2" :gutter="10">
        <ElCol :span="1.5">
          <ElButton icon="el-icon-plus" plain size="mini" type="primary" @click="openSelectUser">添加用户</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton :disabled="roleIds.length === 0" icon="el-icon-circle-close" plain size="mini" type="danger" @click="cancelAuthUserAll">批量取消授权</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton icon="el-icon-check" size="mini" type="primary" @click="submitForm">提交</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton icon="el-icon-close" size="mini" type="warning" @click="close">返回</ElButton>
        </ElCol>
      </ElRow>

      <template #rightTable="{ height: tableHeight }">
        <ElTable
          ref="table"
          v-loading="loading"
          :data="roles.slice((page - 1) * size, page * size)"
          :default-sort="{ prop: 'roleId' }"
          :max-height="tableHeight"
          :row-key="getRowKey"
          @row-click="clickRow"
          @selection-change="handleSelectionChange"
        >
          <ElTableColumn align="center" header-align="center" label="序号" type="index">
            <template #default="{ row, $index }">
              <span>{{ (page - 1) * size + $index + 1 }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn :reserve-selection="true" type="selection" width="55"></ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="角色ID" prop="roleId" />
          <ElTableColumn align="center" header-align="center" label="角色名称" prop="roleName" />
          <ElTableColumn align="center" header-align="center" label="权限字符" prop="roleKey" />
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="180">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton icon="el-icon-circle-close" size="mini" type="text" @click="cancelAuthUser(row)">取消授权</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>

      <Pagination v-show="total > 0" slot="pagination" :limit.sync="size" :page.sync="page" :total="total" />
    </TreeTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 分页信息
      total: 0,
      page: 1,
      size: 15,
      // 选中角色 ID
      roleIds: [],
      // 角色信息
      roles: [],
      // 用户信息
      form: {},
    }
  },
  computed: {
    userId() {
      return this.$route.params?.userId
    },
  },
  created() {
    if (this.userId) {
      this.loading = true
      useApiGetAuthRole(this.userId).then((response) => {
        this.form = response
        this.roles = response.roles
        this.total = this.roles.length
        this.$nextTick(() => {
          for (const row of this.roles) {
            if (row.flag) {
              this.$refs.table.toggleRowSelection(row)
            }
          }
        })
        this.loading = false
      })
    }
  },
  methods: {
    // 单击选中行数据
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.roleIds = selection.map((item) => item.roleId)
    },
    // 保存选中的数据 ID
    getRowKey(row) {
      return row.roleId
    },
    // 提交按钮
    submitForm() {
      const userId = this.form.userId
      const roleIds = this.roleIds.join(',')
      useApiUpdateAuthRole({ userId, roleIds }).then((response) => {
        appModal.msgSuccess('授权成功')
        this.close()
      })
    },
    // 关闭按钮
    close() {
      const object = { path: '/system/user' }
      appTab.closeOpenPage(object)
    },
  },
}
</script>
