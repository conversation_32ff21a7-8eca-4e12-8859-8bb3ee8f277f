<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" :model="queryParams" size="small">
        <ElFormItem label="部门名称" prop="deptName">
          <ElInput v-model="queryParams.deptName" clearable placeholder="请输入部门名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="queryParams.status" clearable placeholder="部门状态">
            <ElOption v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dept:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton icon="el-icon-sort" plain size="mini" type="info" @click="toggleExpandAll">展开/折叠</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>
      <template #rightTable="{ height: tableHeight }">
        <ElTable
          v-if="refreshTable"
          v-loading="loading"
          :data="deptList"
          :default-expand-all="isExpandAll"
          :max-height="tableHeight"
          row-key="deptId"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <ElTableColumn align="left" header-align="center" label="部门名称" min-width="260" prop="deptName"></ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="排序" prop="orderNum" width="60"></ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="状态" prop="status" width="100">
            <template #default="{ row }">
              <BooleanTag enum-key="EnabledBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="170">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作" width="180">
            <template #default="{ row }">
              <ElButton v-hasPermission="['system:dept:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
              <ElButton v-hasPermission="['system:dept:add']" icon="el-icon-plus" size="mini" type="text" @click="handleAdd(row)">新增</ElButton>
              <ElButton v-if="row.parentId !== 0" v-hasPermission="['system:dept:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
    </TreeTable>
    <div>
      <!-- 添加或修改部门对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="600px">
        <ElForm ref="form" label-width="80px" :model="form" :rules="rules">
          <ElRow>
            <ElCol v-if="form.parentId !== 0" :span="24">
              <ElFormItem label="上级部门" prop="parentId">
                <Treeselect v-model="form.parentId" :normalizer="normalizer" :options="deptOptions" placeholder="选择上级部门" />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="部门名称" prop="deptName">
                <ElInput v-model="form.deptName" placeholder="请输入部门名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="显示排序" prop="orderNum">
                <ElInputNumber v-model="form.orderNum" controls-position="right" :min="0" />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="负责人" prop="leader">
                <ElInput v-model="form.leader" maxlength="20" placeholder="请输入负责人" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="联系电话" prop="mobile">
                <ElInput v-model="form.mobile" maxlength="11" placeholder="请输入联系电话" />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="邮箱" prop="email">
                <ElInput v-model="form.email" maxlength="50" placeholder="请输入邮箱" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="部门状态">
                <ElRadioGroup v-model="form.status">
                  <ElRadio v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      // 部门树选项
      deptOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        deptName: undefined,
        status: true,
      },
      // 表单参数
      form: {
        parentId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        mobile: undefined,
        email: undefined,
        status: true,
      },
      id: undefined,
      // 表单校验
      rules: {
        parentId: [{ required: true, message: '上级部门不能为空', trigger: 'blur' }],
        deptName: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }],
        orderNum: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }],
        email: [
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change'],
          },
        ],
        // mobile: [
        //   {
        //     pattern: /^1[3-9|]\d{9}$/,
        //     message: '请输入正确的手机号码',
        //     trigger: 'blur',
        //   },
        // ],
      },
    }
  },
  computed: {},
  created() {
    this.getList()
  },
  methods: {
    // 查询部门列表
    getList() {
      this.loading = true
      useApiGetDeptList(this.queryParams).then((response) => {
        this.deptList = handleTree(response, 'deptId')
        this.loading = false
      })
    },
    // 转换部门数据结构
    normalizer(node) {
      if (node.children && node.children.length === 0) {
        delete node.children
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        parentId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        mobile: undefined,
        email: undefined,
        status: true,
      }
      this.id = undefined
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 新增按钮操作
    handleAdd(row) {
      this.reset()
      if (row !== undefined) {
        this.form.parentId = row.deptId
      }
      console.log(this.form)
      this.open = true
      this.title = '添加部门'
      useApiGetDeptList().then((response) => {
        this.deptOptions = handleTree(response, 'deptId')
      })
    },
    // 展开/折叠操作
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      useApiGetDept(row.deptId).then((response) => {
        // this.form = response
        this.form = {
          parentId: response.parentId,
          deptName: response.deptName,
          orderNum: response.orderNum,
          leader: response.leader,
          mobile: response.mobile,
          email: response.email,
          status: response.status,
        }
        this.id = response.deptId
        this.open = true
        this.title = '修改部门'
        useApiGetDeptExcludeChildList(row.deptId).then((response) => {
          this.deptOptions = handleTree(response, 'deptId')
          if (this.deptOptions.length === 0) {
            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] }
            this.deptOptions.push(noResultsOptions)
          }
        })
      })
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.id === undefined) {
            useApiAddDept(this.form).then((response) => {
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          } else {
            useApiUpdateDept(this.id, this.form).then((response) => {
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      appModal
        .confirm(`是否确认删除名称为"${row.deptName}"的数据项？`)
        .then(() => {
          return useApiDeleteDept(row.deptId)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
  },
}
</script>
