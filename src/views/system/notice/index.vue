<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="queryParams" size="small">
        <ElFormItem label="公告标题" prop="noticeTitle">
          <ElInput v-model="queryParams.noticeTitle" clearable placeholder="请输入公告标题" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="操作人员" prop="createBy">
          <ElInput v-model="queryParams.createBy" clearable placeholder="请输入操作人员" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="类型" prop="noticeType">
          <ElSelect v-model="queryParams.noticeType" clearable placeholder="公告类型">
            <ElOption v-for="opt in enumOptions('NoticeTypeEnum')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:notice:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:notice:edit']" :disabled="single" icon="el-icon-edit" plain size="mini" type="success" @click="handleUpdate">修改</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:notice:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>
      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="noticeList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="序号" prop="noticeId" width="100" />
          <ElTableColumn align="left" header-align="center" label="公告标题" prop="noticeTitle" />
          <ElTableColumn align="center" header-align="center" label="公告类型" prop="noticeType" width="100">
            <template #default="{ row }">
              <span>{{ enumLabel('NoticeTypeEnum', row.noticeType) }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="状态" prop="status" width="100">
            <template #default="{ row }">
              <BooleanTag enum-key="PublishedBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="创建者" prop="createBy" width="100" />
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="170">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['system:notice:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
              <ElButton v-hasPermission="['system:notice:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>

      <Pagination v-show="total > 0" slot="pagination" :limit.sync="queryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <div>
      <!-- 添加或修改公告对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="780px">
        <ElForm ref="form" label-width="80px" :model="form" :rules="rules">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="公告标题" prop="noticeTitle">
                <ElInput v-model="form.noticeTitle" placeholder="请输入公告标题" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="公告类型" prop="noticeType">
                <ElSelect v-model="form.noticeType" placeholder="请选择公告类型">
                  <ElOption v-for="opt in enumOptions('NoticeTypeEnum')" :key="opt.value" :label="opt.label" :value="opt.value"></ElOption>
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="状态">
                <ElRadioGroup v-model="form.status">
                  <ElRadio v-for="opt in enumOptions('PublishedBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="内容">
                <Editor v-model="form.noticeContent" :min-height="192" />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        noticeTitle: undefined,
        createBy: undefined,
        status: true,
      },
      // 表单参数
      form: {
        ...this.form,
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: true,
      },
      // 表单校验
      rules: {
        noticeTitle: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
        noticeType: [{ required: true, message: '公告类型不能为空', trigger: 'change' }],
      },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询公告列表
    getList() {
      this.loading = true
      useApiGetNoticeList(this.queryParams).then((response) => {
        this.noticeList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: true,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.noticeId)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加公告'
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      const noticeId = row.noticeId || this.ids
      useApiGetNotice(noticeId).then((response) => {
        this.form = { ...this.form, ...response }
        this.open = true
        this.title = '修改公告'
      })
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.noticeId === undefined) {
            useApiAddNotice(this.form).then((response) => {
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          } else {
            useApiUpdateNotice(this.form.noticeId, this.form).then((response) => {
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      const noticeIds = row.noticeId || this.ids
      appModal
        .confirm(`是否确认删除公告 ID 为"${noticeIds}"的数据项？`)
        .then(() => {
          return useApiDeleteNotice(noticeIds)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
  },
}
</script>
