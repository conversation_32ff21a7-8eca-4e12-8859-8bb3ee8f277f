<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" :model="queryParams">
        <ElFormItem label="菜单名称" prop="menuName">
          <ElInput v-model="queryParams.menuName" clearable placeholder="请输入菜单名称" size="small" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="queryParams.status" clearable placeholder="菜单状态" size="small">
            <ElOption v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <ElRow slot="actionLeftBtn" class="mb-2" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:menu:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton icon="el-icon-sort" plain size="mini" type="info" @click="toggleExpandAll">展开/折叠</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @query-table="getList"></RightToolbar>

      <template #rightTable="{ height: tableHeight }">
        <ElTable
          v-if="refreshTable"
          v-loading="loading"
          :data="menuList"
          :default-expand-all="isExpandAll"
          :max-height="tableHeight"
          row-key="menuId"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <ElTableColumn align="left" header-align="center" label="菜单名称" width="250">
            <template #default="{ row }">
              <span class="space-x-1"
                ><Icon :icon="row.icon"></Icon><span>{{ row.menuName }} </span>
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="排序" prop="orderNum" width="50"></ElTableColumn>
          <ElTableColumn align="left" header-align="center" label="组件路径" min-width="100">
            <template #default="{ row }">
              <template v-if="row.component">
                @/views/<span class="text-blue-400">{{ row.component }}</span
                >.vue
              </template>
            </template>
          </ElTableColumn>
          <ElTableColumn align="left" header-align="center" label="路由路径" min-width="100">
            <template #default="{ row }">
              <div v-if="row.fullPath">
                <ElTag v-if="row.menuType === 'D'" size="mini" type="warning">
                  <span v-for="(item, index) in row.fullPathArray" :key="index" class="font-bold" :class="{ 'text-blue-400': item === row.path }">/{{ item }}</span>
                </ElTag>
                <div v-else class="cursor-pointer hover:underline" @click="openRouter(row.fullPath)">
                  <i class="el-icon-link"></i>
                  <span v-for="(item, index) in row.fullPathArray" :key="index" :class="{ 'text-blue-400': item === row.path }">/{{ item }}</span>
                </div>
              </div>
              <span v-else class="text-green-400">{{ row.path }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="left" header-align="center" label="权限标识" min-width="60" prop="perms"></ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="状态" prop="status" width="70">
            <template #default="{ row }">
              <BooleanTag enum-key="EnabledBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="显示" prop="visible" width="70">
            <template #default="{ row }">
              <BooleanTag enum-key="VisibleBoolean" :value="row.visible" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="160">
            <template #default="{ row }">
              <span class="text-sm">{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作" width="180">
            <template #default="{ row }">
              <ElButton v-hasPermission="['system:menu:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
              <ElButton v-hasPermission="['system:menu:add']" icon="el-icon-plus" size="mini" type="text" @click="handleAdd(row)">新增</ElButton>
              <ElButton v-hasPermission="['system:menu:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
    </TreeTable>
    <div>
      <!-- 添加或修改菜单对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="680px">
        <ElForm ref="form" label-width="100px" :model="form" :rules="rules">
          <ElRow>
            <ElCol :span="24">
              <ElFormItem label="上级菜单">
                <Treeselect v-model="form.parentId" :normalizer="normalizer" :options="menuOptions" placeholder="选择上级菜单" :show-count="true" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="菜单类型" prop="menuType">
                <ElRadioGroup v-model="form.menuType">
                  <ElRadio v-for="opt in enumOptions('MenuTypeEnum')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem v-if="form.menuType !== 'B'" label="菜单图标">
                <ElPopover placement="bottom-start" trigger="click" width="540" @show="$refs.iconSelect.reset()">
                  <IconSelect ref="iconSelect" @selected="selected" />
                  <template #reference>
                    <ElInput v-model="form.icon" placeholder="点击选择图标" readonly>
                      <template #prefix>
                        <Icon v-if="form.icon" class="el-input__icon h-6 text-gray-600 w-3" :icon="form.icon" />
                        <i v-else class="el-icon-search el-input__icon" />
                      </template>
                    </ElInput>
                  </template>
                </ElPopover>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="菜单名称" prop="menuName">
                <ElInput v-model="form.menuName" placeholder="请输入菜单名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="显示排序" prop="orderNum">
                <ElInputNumber v-model="form.orderNum" controls-position="right" :min="0" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem v-if="form.menuType !== 'B'">
                <template #label>
                  <ElTooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
                    <i class="el-icon-question"></i>
                  </ElTooltip>
                  是否外链
                </template>
                <ElRadioGroup v-model="form.isFrame">
                  <ElRadio v-for="opt in enumOptions('YesNoBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem v-if="form.menuType !== 'B'" prop="path">
                <template #label>
                  <ElTooltip
                    content="格式全部小写 kebab-case，访问的路由地址，会自动拼接上级路由名称，如：`user` 则生成路由为 `system/user`，如外网地址需内链访问则以`http(s)://`开头"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </ElTooltip>
                  路由地址
                </template>
                <ElInput v-model="form.path" placeholder="只输入本级路径的路由" />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="form.menuType === 'M'" :span="12">
              <ElFormItem prop="component">
                <template #label>
                  <ElTooltip content="格式全部小写 kebab-case，访问的组件路径，views 目录下，如：`system/user/auth-role`" placement="top">
                    <i class="el-icon-question"></i>
                  </ElTooltip>
                  组件路径
                </template>
                <ElInput v-model="form.component" placeholder="如 system/user/auth-role" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem v-if="form.menuType !== 'D'">
                <ElInput v-model="form.perms" maxlength="100" placeholder="请输入权限标识" />
                <template #label>
                  <ElTooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermission('system:user:list')`)" placement="top">
                    <i class="el-icon-question"></i>
                  </ElTooltip>
                  权限字符
                </template>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem v-if="form.menuType === 'M'">
                <ElInput v-model="form.query" maxlength="255" placeholder="请输入路由参数" />
                <template #label>
                  <ElTooltip content="访问路由的默认传递参数，如：{ 'id': 1}" placement="top">
                    <i class="el-icon-question"></i>
                  </ElTooltip>
                  路由参数
                </template>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem v-if="form.menuType === 'M'">
                <template #label>
                  <ElTooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
                    <i class="el-icon-question"></i>
                  </ElTooltip>
                  是否缓存
                </template>
                <ElRadioGroup v-model="form.isCache">
                  <ElRadio v-for="opt in enumOptions('YesNoBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem v-if="form.menuType !== 'B'">
                <template #label>
                  <ElTooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                    <i class="el-icon-question"></i>
                  </ElTooltip>
                  显示状态
                </template>
                <ElRadioGroup v-model="form.visible">
                  <ElRadio v-for="opt in enumOptions('VisibleBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem v-if="form.menuType !== 'B'">
                <template #label>
                  <ElTooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
                    <i class="el-icon-question"></i>
                  </ElTooltip>
                  菜单状态
                </template>
                <ElRadioGroup v-model="form.status">
                  <ElRadio v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
// 检查是否无效路径
const invalidPath = (item) => {
  // 无效情况：null、#、http 开头、按钮类型 F
  return !item.path || item.path === '#' || item.path.startsWith('http') || item.menuType === 'B'
}
// 获取转换后路径
const getFullPath = (item) => {
  return invalidPath(item) ? '' : item.parentPath ? `${item.parentPath}/${item.path}` : item.path
}
// 循环获取转换后的子项目
const getItemChildren = (item) => {
  // 无 children 停止循环
  if (!item.children) return item

  const children = item.children.map((subItem) => {
    const loopItem = {
      ...subItem,
      parentPath: getFullPath(item),
      fullPath: getFullPath(subItem),
      fullPathArray: getFullPath(subItem).split('/'),
    }

    return getItemChildren(loopItem)
  })

  return {
    ...item,
    children,
    parentPath: getFullPath(item),
    fullPath: getFullPath(item),
    fullPathArray: getFullPath(item).split('/'),
  }
}

export default {
  data() {
    return {
      // 当前编辑的菜单 ID
      id: undefined,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        menuName: undefined,
        visible: undefined,
      },
      // 表单参数
      form: {
        parentId: 0,
        menuName: undefined,
        icon: undefined,
        menuType: 'D',
        orderNum: undefined,
        isFrame: false,
        isCache: false,
        visible: true,
        status: true,
        path: undefined,
      },
      // 表单校验
      rules: {
        menuName: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
        orderNum: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }],
        path: [{ required: true, message: '路由地址不能为空', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  created() {
    this.getList()
  },
  methods: {
    // 选择图标
    selected(name) {
      this.form.icon = name
    },
    // 查询菜单列表
    getList() {
      this.loading = true

      // useApiGetMenuListWithPath(this.queryParams).then((response) => {
      //   console.log('🚀 ‣ useApiGetMenuListWithPath:', response)
      //   this.menuList = response
      // })

      useApiGetMenuList(this.queryParams).then((response) => {
        this.menuList = handleTree(response, 'menuId')

        // this.menuList = menuList.map((itemA) => {
        //   return getItemChildren(itemA)
        // })

        console.log('🚀 ‣ this.menuList', this.menuList)
        this.loading = false
      })
    },
    // 转换菜单数据结构
    normalizer(node) {
      if (node.children && node.children.length === 0) {
        delete node.children
      }
      return {
        id: node.menuId,
        label: node.menuName,
        children: node.children,
      }
    },
    // 查询菜单下拉树结构
    getTreeselect() {
      useApiGetMenuList().then((response) => {
        this.menuOptions = []
        const menu = { menuId: 0, menuName: '主类目', children: [] }
        menu.children = handleTree(response, 'menuId')
        this.menuOptions.push(menu)
        console.log('this.menuOptions', this.menuOptions)
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        parentId: 0,
        menuName: undefined,
        icon: undefined,
        menuType: 'D',
        orderNum: undefined,
        isFrame: false,
        isCache: false,
        visible: true,
        status: true,
        path: undefined,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 新增按钮操作
    handleAdd(row) {
      this.reset()
      this.getTreeselect()
      this.form.parentId = row !== undefined && row.menuId ? row.menuId : 0
      this.open = true
      this.title = '添加菜单'
    },
    // 展开/折叠操作
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      this.getTreeselect()
      this.id = row.menuId
      useApiGetMenu(this.id).then((response) => {
        this.form = {
          ...this.form,
          menuName: response.menuName,
          parentId: response.parentId,
          orderNum: response.orderNum,
          path: response.path,
          component: response.component,
          query: response.query,
          isFrame: response.isFrame,
          isCache: response.isCache,
          menuType: response.menuType,
          visible: response.visible,
          status: response.status,
          perms: response.perms,
          icon: response.icon,
        }
        this.open = true
        this.title = '修改菜单'
      })
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 强制转换格式
          const { menuType, path, component, menuId } = this.form
          this.form = {
            ...this.form,
            component: useRouterCase.component(component),
            path: menuType === 'B' ? '' : useRouterCase.path(path),
          }
          // FIXME: menuType === 'B' 时，客户端提交 path 为 ''，服务端返回为 '#'
          if (this.id) {
            useApiUpdateMenu(this.id, this.form).then((response) => {
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            useApiAddMenu(this.form).then((response) => {
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      appModal
        .confirm(`是否确认删除名称为"${row.menuName}"的数据项？`)
        .then(() => {
          return useApiDeleteNotice(row.menuId)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    openRouter(path) {
      console.log('🚀 ‣ path', `/${path}`)
      this.$router.push({ path: `/${path}` })
    },
  },
}
</script>
