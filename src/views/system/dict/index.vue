<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="baseQueryParams" size="small">
        <ElFormItem label="字典名称" prop="dictName">
          <ElInput v-model="baseQueryParams.dictName" class="w-60" clearable placeholder="请输入字典名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="字典类型" prop="dictType">
          <ElInput v-model="baseQueryParams.dictType" class="w-60" clearable placeholder="请输入字典类型" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="baseQueryParams.status" class="w-60" clearable placeholder="字典状态">
            <ElOption v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="创建时间">
          <ElDatePicker
            v-model="dateRange"
            class="w-60"
            :default-time="['00:00:00', '23:59:59']"
            end-placeholder="结束日期"
            range-separator="-"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></ElDatePicker>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:edit']" :disabled="single" icon="el-icon-edit" plain size="mini" type="success" @click="handleUpdate">修改</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:remove']" icon="el-icon-refresh" plain size="mini" type="danger" @click="handleRefreshCache">刷新缓存</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>
      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="typeList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="字典ID" prop="dictId" width="100" />
          <ElTableColumn align="center" header-align="center" label="字典名称" prop="dictName" />
          <ElTableColumn align="center" header-align="center" label="字典类型">
            <template #default="{ row }">
              <RouterLink class="link-type" :to="{ name: 'DictData', params: { dictId: row.dictId } }">
                <span>{{ row.dictType }}</span>
              </RouterLink>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="状态" prop="status">
            <template #default="{ row }">
              <BooleanTag enum-key="EnabledBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="备注" prop="remark" />
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="180">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['system:dict:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
              <ElButton v-hasPermission="['system:dict:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <Pagination v-show="total > 0" slot="pagination" :limit.sync="baseQueryParams.size" :page.sync="baseQueryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <div>
      <!-- 添加或修改参数配置对话框 -->
      <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px">
        <ElForm ref="form" label-width="80px" :model="form" :rules="rules">
          <ElFormItem label="字典名称" prop="dictName">
            <ElInput v-model="form.dictName" placeholder="请输入字典名称" />
          </ElFormItem>
          <ElFormItem label="字典类型" prop="dictType">
            <ElInput v-model="form.dictType" placeholder="请输入字典类型" />
          </ElFormItem>
          <ElFormItem label="状态" prop="status">
            <ElRadioGroup v-model="form.status">
              <ElRadio v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="form.remark" placeholder="请输入内容" type="textarea"></ElInput>
          </ElFormItem>
        </ElForm>
        <template #footer>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典表格数据
      typeList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 基础查询参数
      baseQueryParams: {
        page: 1,
        size: 10,
        dictName: undefined,
        dictType: undefined,
        status: true,
      },
      // 表单参数
      form: {
        dictId: undefined,
        dictName: undefined,
        dictType: undefined,
        status: true,
        remark: undefined,
      },
      // 表单校验
      rules: {
        dictName: [{ required: true, message: '字典名称不能为空', trigger: 'blur' }],
        dictType: [{ required: true, message: '字典类型不能为空', trigger: 'blur' }],
      },
    }
  },
  computed: {
    queryParams() {
      return {
        ...this.baseQueryParams,
        beginTime: this.dateRange?.[0],
        endTime: this.dateRange?.[1],
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询字典类型列表
    getList(parameters = this.queryParams) {
      this.loading = true
      useApiGetDictTypeList(parameters).then((response) => {
        this.typeList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        dictId: undefined,
        dictName: undefined,
        dictType: undefined,
        status: true,
        remark: undefined,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList({
        ...this.baseQueryParams,
        page: 1,
      })
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加字典类型'
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.dictId)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      const dictId = row.dictId || this.ids
      useApiGetDictType(dictId).then((response) => {
        this.form = { ...this.form, ...response }
        this.open = true
        this.title = '修改字典类型'
      })
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.dictId === undefined) {
            useApiAddDictType(this.form).then((response) => {
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          } else {
            useApiUpdateDictType(this.form.dictId, this.form).then((response) => {
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      if (row.dictId) {
        // 单个删除
        appModal
          .confirm(`是否确认删除字典 ID 为"${row.dictId}"的数据项？`)
          .then(() => {
            return useApiDelDictType(row.dictId)
          })
          .then(() => {
            this.getList()
            appModal.msgSuccess('删除成功')
          })
          .catch(() => {})
      } else {
        // 批量删除
        if (this.ids.length === 0) {
          appModal.msgWarning('请选择要删除的数据项')
          return
        }
        appModal
          .confirm(`是否确认删除字典 ID 为"${this.ids.join(', ')}"的数据项？`)
          .then(() => {
            return useApiDelDictTypeBatch(this.ids)
          })
          .then(() => {
            this.getList()
            appModal.msgSuccess('删除成功')
          })
          .catch(() => {})
      }
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'system/dict/type/export',
        data: this.queryParams,
        filename: '字典类型',
      })
    },
    // 刷新缓存按钮操作
    handleRefreshCache() {
      useApiRefreshDictTypeCache().then(() => {
        appModal.msgSuccess('刷新成功')
        this.$store.dispatch('dict/cleanDict')
      })
    },
  },
}
</script>
