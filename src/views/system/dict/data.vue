<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="queryParams" size="small">
        <ElFormItem label="字典名称" prop="dictType">
          <ElSelect v-model="queryParams.dictType">
            <ElOption v-for="item in dictOptions" :key="item.value" :disabled="item.disabled" :label="item.label" :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="字典标签" prop="dictLabel">
          <ElInput v-model="queryParams.dictLabel" clearable placeholder="请输入字典标签" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="queryParams.status" clearable placeholder="数据状态">
            <ElOption v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.label" :value="opt.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:add']" icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:edit']" :disabled="single" icon="el-icon-edit" plain size="mini" type="success" @click="handleUpdate">修改</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['system:dict:export']" icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton icon="el-icon-close" plain size="mini" type="warning" @click="handleClose">关闭</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>

      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="dataList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55" />
          <ElTableColumn align="center" header-align="center" label="字典编码" prop="dictCode" />
          <ElTableColumn align="center" header-align="center" label="字典标签" prop="dictLabel">
            <template #default="{ row }">
              <span v-if="(row.listClass === '' || row.listClass === 'default') && (row.cssClass === '' || row.cssClass === null)">
                {{ row.dictLabel }}
              </span>
              <ElTag v-else :class="row.cssClass" :type="row.listClass === 'primary' ? '' : row.listClass">{{ row.dictLabel }}</ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="字典键值" prop="dictValue" />
          <ElTableColumn align="center" header-align="center" label="字典排序" prop="dictSort" />
          <ElTableColumn align="center" header-align="center" label="状态" prop="status">
            <template #default="{ row }">
              <BooleanTag enum-key="EnabledBoolean" :value="row.status" />
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="备注" prop="remark" />
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="180">
            <template #default="{ row }">
              <span>{{ row.createTime }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['system:dict:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(row)">修改</ElButton>
              <ElButton v-hasPermission="['system:dict:remove']" icon="el-icon-delete" size="mini" type="text" @click="handleDelete(row)">删除</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>

      <Pagination v-show="total > 0" slot="pagination" :limit.sync="queryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </TreeTable>

    <!-- 添加或修改参数配置对话框 -->
    <ElDialog append-to-body :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px">
      <ElForm ref="form" label-width="80px" :model="form" :rules="rules">
        <ElFormItem label="字典类型">
          <ElInput v-model="form.dictType" :disabled="true" />
        </ElFormItem>
        <ElFormItem label="数据标签" prop="dictLabel">
          <ElInput v-model="form.dictLabel" placeholder="请输入数据标签" />
        </ElFormItem>
        <ElFormItem label="数据键值" prop="dictValue">
          <ElInput v-model="form.dictValue" placeholder="请输入数据键值" />
        </ElFormItem>
        <ElFormItem label="样式属性" prop="cssClass">
          <ElInput v-model="form.cssClass" placeholder="请输入样式属性" />
        </ElFormItem>
        <ElFormItem label="显示排序" prop="dictSort">
          <ElInputNumber v-model="form.dictSort" controls-position="right" :min="0" />
        </ElFormItem>
        <ElFormItem label="回显样式" prop="listClass">
          <ElSelect v-model="form.listClass">
            <ElOption v-for="item in listClassOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value"></ElOption>
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElRadioGroup v-model="form.status">
            <ElRadio v-for="opt in enumOptions('EnabledBoolean')" :key="opt.value" :label="opt.value">{{ opt.label }}</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <ElFormItem label="备注" prop="remark">
          <ElInput v-model="form.remark" placeholder="请输入内容" type="textarea"></ElInput>
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton type="primary" @click="submitForm">确 定</ElButton>
        <ElButton @click="cancel">取 消</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典表格数据
      dataList: [],
      // 默认字典类型
      defaultDictType: '',
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 数据标签回显样式
      listClassOptions: [
        {
          value: 'default',
          label: '默认',
        },
        {
          value: 'primary',
          label: '主要',
        },
        {
          value: 'success',
          label: '成功',
        },
        {
          value: 'info',
          label: '信息',
        },
        {
          value: 'warning',
          label: '警告',
        },
        {
          value: 'danger',
          label: '危险',
        },
      ],
      // 类型数据字典
      dictOptions: [],
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        dictLabel: undefined,
        dictType: undefined,
        status: true,
      },
      // 表单参数
      form: {
        ...this.form,
        dictCode: undefined,
        dictLabel: undefined,
        dictValue: undefined,
        cssClass: undefined,
        listClass: 'default',
        dictSort: 0,
        status: true,
        remark: undefined,
      },
      // 表单校验
      rules: {
        dictLabel: [{ required: true, message: '数据标签不能为空', trigger: 'blur' }],
        dictValue: [{ required: true, message: '数据键值不能为空', trigger: 'blur' }],
        dictSort: [{ required: true, message: '数据顺序不能为空', trigger: 'blur' }],
      },
    }
  },
  computed: {
    dictId() {
      return this.$route.params?.dictId
    },
  },
  created() {
    this.getDictType(this.dictId)
    this.getTypeList()
  },
  methods: {
    // 查询字典类型详细
    getDictType(dictId) {
      useApiGetDictType(dictId).then((response) => {
        this.queryParams.dictType = response.dictType
        this.defaultDictType = response.dictType
        this.getList()
      })
    },
    // 查询字典类型列表
    getTypeList() {
      useApiGetDictOptionSelect().then((response) => {
        this.dictOptions = response
      })
    },
    // 查询字典数据列表
    getList() {
      this.loading = true
      useApiGetDictDataList(this.queryParams).then((response) => {
        this.dataList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        dictCode: undefined,
        dictLabel: undefined,
        dictValue: undefined,
        cssClass: undefined,
        listClass: 'default',
        dictSort: 0,
        status: true,
        remark: undefined,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 返回按钮操作
    handleClose() {
      const object = { path: '/system/dict' }
      appTab.closeOpenPage(object)
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams.dictType = this.defaultDictType
      this.handleQuery()
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加字典数据'
      this.form.dictType = this.queryParams.dictType
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.dictCode)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      const dictCode = row.dictCode || this.ids
      useApiGetDictData(dictCode).then((response) => {
        this.form = { ...this.form, ...response }
        this.open = true
        this.title = '修改字典数据'
      })
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.dictCode === undefined) {
            useApiAddDictData(this.form).then((response) => {
              this.$store.dispatch('dict/removeDict', this.queryParams.dictType)
              appModal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          } else {
            useApiUpdateDictData(this.form.dictCode, this.form).then((response) => {
              this.$store.dispatch('dict/removeDict', this.queryParams.dictType)
              appModal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      if (row.dictCode) {
        // 单个删除
        appModal
          .confirm(`是否确认删除字典编码为"${row.dictCode}"的数据项？`)
          .then(() => {
            return useApiDelDictData(row.dictCode)
          })
          .then(() => {
            this.getList()
            appModal.msgSuccess('删除成功')
            this.$store.dispatch('dict/removeDict', this.queryParams.dictType)
          })
          .catch(() => {})
      } else {
        // 批量删除
        if (this.ids.length === 0) {
          appModal.msgWarning('请选择要删除的数据项')
          return
        }
        appModal
          .confirm(`是否确认删除字典编码为"${this.ids.join(', ')}"的数据项？`)
          .then(() => {
            return useApiDelDictDataBatch(this.ids)
          })
          .then(() => {
            this.getList()
            appModal.msgSuccess('删除成功')
            this.$store.dispatch('dict/removeDict', this.queryParams.dictType)
          })
          .catch(() => {})
      }
    },
    // 导出按钮操作
    handleExport() {
      useDownloader.xlsx({
        url: 'system/dict/data/export',
        data: this.queryParams,
        filename: '字典数据',
      })
    },
  },
}
</script>
