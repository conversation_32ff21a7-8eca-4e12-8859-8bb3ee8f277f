<template>
  <!-- 导入表 -->
  <ElDialog append-to-body :close-on-click-modal="false" title="导入表" top="5vh" :visible.sync="visible" width="800px">
    <ElForm ref="queryForm" :inline="true" :model="queryParams" size="small">
      <ElFormItem label="表名称" prop="tableName">
        <ElInput v-model="queryParams.tableName" clearable placeholder="请输入表名称" @keyup.enter="handleQuery" />
      </ElFormItem>
      <ElFormItem label="表描述" prop="tableComment">
        <ElInput v-model="queryParams.tableComment" clearable placeholder="请输入表描述" @keyup.enter="handleQuery" />
      </ElFormItem>
      <ElFormItem>
        <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
        <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <ElRow>
      <ElTable ref="table" :data="dbTableList" height="260px" @row-click="clickRow" @selection-change="handleSelectionChange">
        <ElTableColumn align="center" header-align="center" type="selection" width="55"></ElTableColumn>
        <ElTableColumn align="center" header-align="center" label="表名称" prop="tableName"></ElTableColumn>
        <ElTableColumn align="center" header-align="center" label="表描述" prop="tableComment"></ElTableColumn>
        <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime"></ElTableColumn>
        <ElTableColumn align="center" header-align="center" label="更新时间" prop="updateTime"></ElTableColumn>
      </ElTable>
      <Pagination v-show="total > 0" :limit.sync="queryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </ElRow>
    <template #footer>
      <ElButton type="primary" @click="handleImportTable">确 定</ElButton>
      <ElButton @click="visible = false">取 消</ElButton>
    </template>
  </ElDialog>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      tables: [],
      // 总条数
      total: 0,
      // 表数据
      dbTableList: [],
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        tableName: undefined,
        tableComment: undefined,
      },
    }
  },
  methods: {
    // 显示弹框
    show() {
      this.getList()
      this.visible = true
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tables = selection.map((item) => item.tableName)
    },
    // 查询表数据
    getList() {
      useApiGetDatabaseTableList(this.queryParams).then((response) => {
        this.dbTableList = response.records
        this.total = response.total
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 导入按钮操作
    handleImportTable() {
      const tableNames = this.tables.join(',')
      if (tableNames === '') {
        appModal.msgError('请选择要导入的表')
        return
      }
      useApiImportTable(tableNames).then((response) => {
        appModal.msgSuccess('操作成功')
        this.visible = false
        this.$emit('ok')
      })
    },
  },
}
</script>
