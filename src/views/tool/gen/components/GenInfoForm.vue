<template>
  <ElForm ref="genInfoForm" label-width="150px" :model="info" :rules="rules">
    <ElRow>
      <ElCol :span="12">
        <ElFormItem prop="tplCategory">
          <template #label>生成模板</template>
          <ElSelect v-model="data.tplCategory" @change="tplSelectChange">
            <ElOption label="单表（增删改查）" value="crud" />
            <ElOption label="树表（增删改查）" value="tree" />
            <ElOption label="主子表（增删改查）" value="sub" />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem prop="tplWebType">
          <template #label>前端类型</template>
          <ElSelect v-model="data.tplWebType">
            <ElOption label="Vue2 Element UI 模版" value="element-ui" />
            <ElOption label="Vue3 Element Plus 模版" value="element-plus" />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem prop="packageName">
          <template #label>
            生成包路径
            <ElTooltip content="生成在哪个java包下，例如 com.ruoyi.system" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElInput v-model="data.packageName" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem prop="moduleName">
          <template #label>
            生成模块名
            <ElTooltip content="可理解为子系统名，例如 system" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElInput v-model="data.moduleName" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem prop="businessName">
          <template #label>
            生成业务名
            <ElTooltip content="可理解为功能英文名，例如 user" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElInput v-model="data.businessName" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem prop="functionName">
          <template #label>
            生成功能名
            <ElTooltip content="用作类描述，例如 用户" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElInput v-model="data.functionName" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem prop="genType">
          <template #label>
            生成代码方式
            <ElTooltip content="默认为zip压缩包下载，也可以自定义生成路径" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElRadio v-model="data.genType" label="0">zip 压缩包</ElRadio>
          <ElRadio v-model="data.genType" label="1">自定义路径</ElRadio>
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem>
          <template #label>
            上级菜单
            <ElTooltip content="分配到指定菜单下，例如 系统管理" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <Treeselect v-model="data.parentMenuId" :append-to-body="true" :normalizer="normalizer" :options="menus" placeholder="请选择系统菜单" :show-count="true" />
        </ElFormItem>
      </ElCol>

      <ElCol v-if="data.genType === '1'" :span="24">
        <ElFormItem prop="genPath">
          <template #label>
            自定义路径
            <ElTooltip content="填写磁盘绝对路径，若不填写，则生成到当前Web项目下" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElInput v-model="data.genPath">
            <template #append>
              <ElDropdown>
                <ElButton type="primary">
                  最近路径快速选择
                  <i class="el-icon--right el-icon-arrow-down"></i>
                </ElButton>
                <template #dropdown>
                  <ElDropdownMenu>
                    <ElDropdownItem @click="data.genPath = '/'">恢复默认的生成基础路径</ElDropdownItem>
                  </ElDropdownMenu>
                </template>
              </ElDropdown>
            </template>
          </ElInput>
        </ElFormItem>
      </ElCol>
    </ElRow>

    <ElRow v-show="data.tplCategory === 'tree'">
      <h4 class="border-b border-gray-300 mb-25px mt-2 mx-10px pb-5px text-base" :style="{ color: $store.state.settings.theme }">其他信息</h4>
      <ElCol :span="12">
        <ElFormItem>
          <template #label>
            树编码字段
            <ElTooltip content="树显示的编码字段名， 如：dept_id" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElSelect v-model="data.treeCode" placeholder="请选择">
            <ElOption v-for="(column, index) in data.columns" :key="index" :label="`${column.columnName}：${column.columnComment}`" :value="column.columnName"></ElOption>
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem>
          <template #label>
            树父编码字段
            <ElTooltip content="树显示的父编码字段名， 如：parent_Id" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElSelect v-model="data.treeParentCode" placeholder="请选择">
            <ElOption v-for="(column, index) in data.columns" :key="index" :label="`${column.columnName}：${column.columnComment}`" :value="column.columnName"></ElOption>
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem>
          <template #label>
            树名称字段
            <ElTooltip content="树节点的显示名称字段名， 如：dept_name" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElSelect v-model="data.treeName" placeholder="请选择">
            <ElOption v-for="(column, index) in data.columns" :key="index" :label="`${column.columnName}：${column.columnComment}`" :value="column.columnName"></ElOption>
          </ElSelect>
        </ElFormItem>
      </ElCol>
    </ElRow>
    <ElRow v-show="data.tplCategory === 'sub'">
      <h4 class="border-b border-gray-300 mb-25px mt-2 mx-10px pb-5px text-base" :style="{ color: $store.state.settings.theme }">关联信息</h4>
      <ElCol :span="12">
        <ElFormItem>
          <template #label>
            关联子表的表名
            <ElTooltip content="关联子表的表名， 如：sys_user" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElSelect v-model="data.subTableName" placeholder="请选择" @change="subSelectChange">
            <ElOption v-for="(table, index) in tables" :key="index" :label="`${table.tableName}：${table.tableComment}`" :value="table.tableName"></ElOption>
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem>
          <template #label>
            子表关联的外键名
            <ElTooltip content="子表关联的外键名， 如：user_id" placement="top">
              <i class="el-icon-question"></i>
            </ElTooltip>
          </template>
          <ElSelect v-model="data.subTableFkName" placeholder="请选择">
            <ElOption v-for="(column, index) in subColumns" :key="index" :label="`${column.columnName}：${column.columnComment}`" :value="column.columnName"></ElOption>
          </ElSelect>
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    tables: {
      type: Array,
      default: () => [],
    },
    menus: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      subColumns: [],
      rules: {
        tplCategory: [{ required: true, message: '请选择生成模板', trigger: 'blur' }],
        packageName: [{ required: true, message: '请输入生成包路径', trigger: 'blur' }],
        moduleName: [{ required: true, message: '请输入生成模块名', trigger: 'blur' }],
        businessName: [{ required: true, message: '请输入生成业务名', trigger: 'blur' }],
        functionName: [{ required: true, message: '请输入生成功能名', trigger: 'blur' }],
      },
      data: {},
    }
  },
  watch: {
    'data.subTableName': function (value) {
      this.setSubTableColumns(value)
    },
    'data.tplWebType': function (value) {
      if (value === '') {
        this.data.tplWebType = 'element-ui'
      }
    },
  },
  created() {
    this.data = { ...this.info }
  },
  methods: {
    // 转换菜单数据结构
    normalizer(node) {
      if (node.children && node.children.length === 0) {
        delete node.children
      }
      return {
        id: node.menuId,
        label: node.menuName,
        children: node.children,
      }
    },
    // 选择子表名触发
    subSelectChange(value) {
      this.data.subTableFkName = ''
    },
    // 选择生成模板触发
    tplSelectChange(value) {
      if (value !== 'sub') {
        this.data.subTableName = ''
        this.data.subTableFkName = ''
      }
    },
    // 设置关联外键
    setSubTableColumns(value) {
      for (const item in this.tables) {
        const name = this.tables[item].tableName
        if (value === name) {
          this.subColumns = this.tables[item].columns
          break
        }
      }
    },
  },
}
</script>
