<template>
  <ElForm ref="basicInfoForm" label-width="150px" :model="info" :rules="rules">
    <ElRow>
      <ElCol :span="12">
        <ElFormItem label="表名称" prop="tableName">
          <ElInput v-model="data.tableName" placeholder="请输入仓库名称" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="表描述" prop="tableComment">
          <ElInput v-model="data.tableComment" placeholder="请输入" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="实体类名称" prop="className">
          <ElInput v-model="data.className" placeholder="请输入" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="作者" prop="functionAuthor">
          <ElInput v-model="data.functionAuthor" placeholder="请输入" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="备注" prop="remark">
          <ElInput v-model="data.remark" :rows="3" type="textarea"></ElInput>
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      rules: {
        tableName: [{ required: true, message: '请输入表名称', trigger: 'blur' }],
        tableComment: [{ required: true, message: '请输入表描述', trigger: 'blur' }],
        className: [{ required: true, message: '请输入实体类名称', trigger: 'blur' }],
        functionAuthor: [{ required: true, message: '请输入作者', trigger: 'blur' }],
      },
      data: {
        tableName: '',
        tableComment: '',
        className: '',
        functionAuthor: '',
        remark: '',
      },
    }
  },
  created() {
    this.data = { ...this.info }
  },
}
</script>
