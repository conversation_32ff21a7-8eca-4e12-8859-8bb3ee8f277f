<template>
  <ElDialog append-to-body :before-close="handleClose" class="font-sans scrollbar" :close-on-click-modal="false" :title="title" top="5vh" :visible.sync="visible" width="85%">
    <!-- 对话框内容 -->
    <div v-if="loading" class="flex flex-col items-center justify-center py-12">
      <div class="loading-spinner"></div>
      <div class="mt-3 text-gray-500">正在加载代码预览...</div>
    </div>

    <div v-else class="p-5">
      <!-- 文件统计信息 -->
      <div class="bg-blue-50 border border-blue-200 mb-4 p-3 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-blue-700 text-sm">
              <i class="el-icon-document mr-1"></i>
              共 {{ codeData.length }} 个文件
            </span>
            <span class="text-blue-600 text-sm">
              <i class="el-icon-time mr-1"></i>
              预览时间：{{ currentTime }}
            </span>
          </div>
          <ElButton :disabled="loading" plain size="mini" type="primary" @click="copyAllCode">
            <i class="el-icon-document-copy mr-1"></i>
            复制全部代码
          </ElButton>
        </div>
      </div>

      <!-- 代码标签页 -->
      <ElTabs v-model="activeTab" class="code-tabs">
        <ElTabPane v-for="(item, index) in codeData" :key="index" :label="item.vmName" :name="item.vmName">
          <!-- 文件头部信息 -->
          <div class="bg-gradient-to-br border border-gray-300 from-gray-50 mb-3 p-2 rounded to-gray-100">
            <div class="flex items-center justify-between">
              <div class="flex gap-3 items-center">
                <span class="bg-blue-100 font-mono px-2 py-1 rounded text-blue-800 text-xs">{{ item.language }}</span>
                <span class="font-mono text-gray-500 text-xs">{{ item.code.split('\n').length }} 行</span>
                <span class="font-mono text-gray-400 text-xs">{{ formatFileSize(item.code.length) }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <ElButton :disabled="!item.highlighted" size="mini" type="text" @click="copySingleCode(item)">
                  <i class="el-icon-document-copy"></i>
                  复制
                </ElButton>
                <ElButton :disabled="!item.highlighted" size="mini" type="text" @click="downloadSingleFile(item)">
                  <i class="el-icon-download"></i>
                  下载
                </ElButton>
              </div>
            </div>
          </div>

          <!-- 代码预览区域 -->
          <div class="border border-gray-200 overflow-hidden rounded-md">
            <div v-if="!item.highlighted" class="bg-gray-50 border border-dashed flex h-32 items-center justify-center rounded">
              <div class="loading-spinner small"></div>
              <span class="ml-2 text-gray-400">语法高亮中...</span>
            </div>
            <div v-else class="bg-white font-mono leading-relaxed max-h-120 overflow-auto p-4 text-sm" v-html="item.html"></div>
          </div>
        </ElTabPane>
      </ElTabs>
    </div>

    <!-- 底部按钮区域 -->
    <template #footer>
      <div class="border-gray-100 border-t pt-4">
        <div class="flex items-center justify-between">
          <div class="text-gray-500 text-sm">
            <i class="el-icon-info mr-1"></i>
            提示：点击文件名可切换不同文件的代码预览
          </div>
          <div class="flex items-center space-x-3">
            <ElButton @click="handleClose">关闭</ElButton>
          </div>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<script>
import { codeToHtml } from 'shiki'

// 缓存高亮结果
const highlightCache = new Map()
// 代码高亮器实例
let highlighter

export default {
  name: 'CodePreviewDialog',
  props: {
    // 对话框标题
    title: {
      type: String,
      default: '代码预览',
    },
    // 是否显示对话框
    value: {
      type: Boolean,
      default: false,
    },
    // 预览的参数
    previewParams: {
      type: [Object, String, Number],
      default: undefined,
    },
  },
  data() {
    return {
      loading: false,
      codeData: [],
      activeTab: 'domain.java',
      currentTime: new Date().toLocaleString(),
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
    allFilesHighlighted() {
      return this.codeData.length > 0 && this.codeData.every((item) => item.highlighted)
    },
  },
  watch: {
    visible(newValue) {
      if (newValue && this.previewParams) {
        this.loadPreview()
      } else if (!newValue) {
        this.clearData()
      }
    },
  },
  async created() {
    await this.initHighlighter()
  },
  beforeDestroy() {
    this.clearData()
    highlightCache.clear()
    highlighter = undefined
  },
  methods: {
    // 初始化代码高亮器
    async initHighlighter() {
      if (!highlighter) {
        try {
          const { codeToHtml } = await import('shiki')
          highlighter = { codeToHtml }
        } catch (error) {
          console.warn('Shiki highlighter initialization failed:', error)
        }
      }
    },

    // 加载预览数据
    async loadPreview() {
      if (!this.previewParams) return

      this.loading = true
      this.codeData = []

      try {
        const response = await useApiPreviewCode(this.previewParams)
        await this.processCodeFiles(response)
      } catch (error) {
        console.error('Preview failed:', error)
        appModal?.msgError('预览失败')
      } finally {
        this.loading = false
      }
    },

    // 处理代码文件
    async processCodeFiles(response) {
      const files = Object.entries(response)

      for (const [key, code] of files) {
        const vmName = key.slice(key.lastIndexOf('/') + 1, key.indexOf('.vm'))
        const language = vmName.slice(vmName.indexOf('.') + 1)

        this.codeData.push({
          key,
          vmName,
          language,
          code,
          html: this.escapeHtml(code),
          highlighted: false,
        })
      }

      // 设置默认激活的 tab
      if (this.codeData.length > 0) {
        this.activeTab = this.codeData[0].vmName
      }

      // 异步高亮处理
      await this.highlightAllCodes()
    },

    // 异步高亮所有代码
    async highlightAllCodes() {
      const items = this.codeData.filter((item) => !item.highlighted)

      for (const item of items) {
        try {
          item.html = await this.highlightCode(item.code, item.language)
          item.highlighted = true
          this.$forceUpdate()
          await new Promise((resolve) => setTimeout(resolve, 0))
        } catch (error) {
          console.warn(`Failed to highlight ${item.vmName}:`, error)
        }
      }
    },

    // 异步代码高亮
    async highlightCode(code, language) {
      if (!highlighter) return this.escapeHtml(code)

      const cacheKey = `${code.length}_${language}_${code.slice(0, 50)}`

      if (highlightCache.has(cacheKey)) {
        return highlightCache.get(cacheKey)
      }

      try {
        const html = await codeToHtml(code, {
          lang: language,
          theme: 'github-light',
        })

        highlightCache.set(cacheKey, html)

        if (highlightCache.size > 50) {
          const firstKey = highlightCache.keys().next().value
          highlightCache.delete(firstKey)
        }

        return html
      } catch (error) {
        console.warn('Code highlighting failed:', error)
        return this.escapeHtml(code)
      }
    },

    // HTML 转义
    escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    },

    // 复制成功回调
    onCopySuccess() {
      appModal?.msgSuccess('复制成功')
    },

    // 清理数据
    clearData() {
      this.codeData = []
      this.loading = false
    },

    // 关闭对话框
    handleClose() {
      this.visible = false
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes < 1024) return `${bytes} B`
      if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
    },

    // 复制单个文件代码
    copySingleCode(item) {
      if (!item.code) return
      this.copyToClipboard(item.code, `已复制 ${item.vmName} 的代码`)
    },

    // 复制所有代码
    copyAllCode() {
      const allCode = this.codeData
        .map((item) => {
          return `// ${item.vmName}\n${item.code}\n`
        })
        .join('\n')
      this.copyToClipboard(allCode, '已复制所有文件的代码')
    },

    // 下载单个文件
    downloadSingleFile(item) {
      const blob = new Blob([item.code], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = item.vmName
      a.click()
      URL.revokeObjectURL(url)
      appModal?.msgSuccess(`已下载 ${item.vmName}`)
    },

    // 复制到剪贴板
    copyToClipboard(text, successMessage) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          appModal?.msgSuccess(successMessage)
        })
        .catch((error) => {
          console.error('Copy failed:', error)
          appModal?.msgError('复制失败')
        })
    },
  },
}
</script>

<style scoped>
/* Element UI 深度选择器样式 */
.font-sans :deep(.el-dialog__body) {
  padding: 0;
  max-height: 80vh;
  overflow: hidden;
}

.code-tabs :deep(.el-tabs__header) {
  margin-bottom: 16px;
}

.code-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

.code-tabs :deep(.el-tabs__item) {
  font-size: 13px;
  padding: 0 16px;
  height: 36px;
  line-height: 36px;
}

.code-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 500;
  color: #409eff;
}

/* 按钮悬停效果 */
.border-t .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 加载状态样式 */
.loading-spinner {
  border: 2px solid #f3f3f4;
  border-top: 2px solid #409eff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 1.5px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.font-mono::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.font-mono::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

.font-mono::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.font-mono::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
