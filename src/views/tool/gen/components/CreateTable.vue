<template>
  <!-- 创建表 -->
  <ElDialog append-to-body title="创建表" top="5vh" :visible.sync="visible" width="800px">
    <span>创建表语句 (支持多个建表语句)：</span>
    <ElInput v-model="content" placeholder="请输入文本" :rows="10" type="textarea"></ElInput>
    <div slot="footer" class="dialog-footer">
      <ElButton type="primary" @click="handleCreateTable">确 定</ElButton>
      <ElButton @click="visible = false">取 消</ElButton>
    </div>
  </ElDialog>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      visible: false,
      // 文本内容
      content: '',
    }
  },
  methods: {
    // 显示弹框
    show() {
      this.visible = true
    },
    /** 创建按钮操作 */
    handleCreateTable() {
      if (this.content === '') {
        appModal.msgError('请输入建表语句')
        return
      }
      useApiCreateTable(this.content).then((response) => {
        appModal.msgSuccess('操作成功')
        this.visible = false
        this.$emit('ok')
      })
    },
  },
}
</script>
