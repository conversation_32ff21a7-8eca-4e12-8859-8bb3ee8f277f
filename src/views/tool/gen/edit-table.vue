<template>
  <ElCard>
    <ElTabs v-model="activeName">
      <ElTabPane label="基本信息" name="basic">
        <BasicInfoForm ref="basicInfo" :info="info" />
      </ElTabPane>
      <ElTabPane label="字段信息" name="columnInfo">
        <ElTable ref="dragTable" :data="columns" :max-height="tableHeight" row-key="columnId">
          <ElTableColumn class-name="allowDrag" label="序号" min-width="5%" type="index" />
          <ElTableColumn align="center" header-align="center" label="字段列名" min-width="10%" prop="columnName" />
          <ElTableColumn align="center" header-align="center" label="字段描述" min-width="10%">
            <template #default="{ row }">
              <ElInput v-model="row.columnComment"></ElInput>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="物理类型" min-width="10%" prop="columnType" />
          <ElTableColumn align="center" header-align="center" label="Java类型" min-width="11%">
            <template #default="{ row }">
              <ElSelect v-model="row.javaType">
                <ElOption label="Long" value="Long" />
                <ElOption label="String" value="String" />
                <ElOption label="Integer" value="Integer" />
                <ElOption label="Double" value="Double" />
                <ElOption label="BigDecimal" value="BigDecimal" />
                <ElOption label="Date" value="Date" />
                <ElOption label="Boolean" value="Boolean" />
              </ElSelect>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="java属性" min-width="10%">
            <template #default="{ row }">
              <ElInput v-model="row.javaField"></ElInput>
            </template>
          </ElTableColumn>

          <ElTableColumn align="center" header-align="center" label="插入" min-width="5%">
            <template #default="{ row }">
              <ElCheckbox v-model="row.isInsert"></ElCheckbox>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="编辑" min-width="5%">
            <template #default="{ row }">
              <ElCheckbox v-model="row.isEdit"></ElCheckbox>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="列表" min-width="5%">
            <template #default="{ row }">
              <ElCheckbox v-model="row.isList"></ElCheckbox>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="查询" min-width="5%">
            <template #default="{ row }">
              <ElCheckbox v-model="row.isQuery"></ElCheckbox>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="查询方式" min-width="10%">
            <template #default="{ row }">
              <ElSelect v-model="row.queryType">
                <ElOption label="=" value="EQ" />
                <ElOption label="!=" value="NE" />
                <ElOption label=">" value="GT" />
                <ElOption label=">=" value="GTE" />
                <ElOption label="<" value="LT" />
                <ElOption label="<=" value="LTE" />
                <ElOption label="LIKE" value="LIKE" />
                <ElOption label="BETWEEN" value="BETWEEN" />
              </ElSelect>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="必填" min-width="5%">
            <template #default="{ row }">
              <ElCheckbox v-model="row.isRequired"></ElCheckbox>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="显示类型" min-width="12%">
            <template #default="{ row }">
              <ElSelect v-model="row.htmlType">
                <ElOption label="文本框" value="input" />
                <ElOption label="文本域" value="textarea" />
                <ElOption label="下拉框" value="select" />
                <ElOption label="单选框" value="radio" />
                <ElOption label="复选框" value="checkbox" />
                <ElOption label="日期控件" value="datetime" />
                <ElOption label="图片上传" value="imageUpload" />
                <ElOption label="文件上传" value="fileUpload" />
                <ElOption label="富文本控件" value="editor" />
              </ElSelect>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="字典类型" min-width="12%">
            <template #default="{ row }">
              <ElSelect v-model="row.dictType" clearable filterable placeholder="请选择">
                <ElOption v-for="item in dictOptions" :key="item.value" :disabled="item.disabled" :label="item.label" :value="item.value">
                  <span class="float-left">{{ item.label }}</span>
                  <span class="float-right text-gray-500 text-sm">{{ item.value }}</span>
                </ElOption>
              </ElSelect>
            </template>
          </ElTableColumn>
        </ElTable>
      </ElTabPane>
      <ElTabPane label="生成信息" name="genInfo">
        <GenInfoForm ref="genInfo" :info="info" :menus="menus" :tables="tables" />
      </ElTabPane>
    </ElTabs>
    <ElForm label-width="100px">
      <ElFormItem class="-ml-25 mt-2.5 text-center">
        <ElButton type="primary" @click="submitForm()">提交</ElButton>
        <ElButton @click="close()">返回</ElButton>
      </ElFormItem>
    </ElForm>
  </ElCard>
</template>

<script>
import Sortable from 'sortablejs'

export default {
  data() {
    return {
      // 选中选项卡的 name
      activeName: 'columnInfo',
      // 表格的高度
      tableHeight: `${document.documentElement.scrollHeight - 245}px`,
      // 表信息
      tables: [],
      // 表列信息
      columns: [],
      // 字典信息
      dictOptions: [],
      // 菜单信息
      menus: [],
      // 表详细信息
      info: {},
    }
  },
  computed: {
    tableId() {
      return this.$route.params?.tableId
    },
  },
  created() {
    if (this.tableId) {
      // 获取表详细信息
      useApiGetGenTableInfo(this.tableId).then((response) => {
        this.columns = response.rows
        this.info = response.info
        this.tables = response.tables
      })
      // 查询字典下拉列表
      useApiGetDictOptionSelect().then((response) => {
        this.dictOptions = response
      })
      // 查询菜单下拉列表
      useApiGetMenuList().then((response) => {
        this.menus = handleTree(response, 'menuId')
      })
    }
  },
  mounted() {
    const element = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
    const sortable = Sortable.create(element, {
      handle: '.allowDrag',
      onEnd: (event_) => {
        const targetRow = this.columns.splice(event_.oldIndex, 1)[0]
        this.columns.splice(event_.newIndex, 0, targetRow)
        for (const index in this.columns) {
          this.columns[index].sort = Number.parseInt(index) + 1
        }
      },
    })
  },
  methods: {
    // 提交按钮
    submitForm() {
      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm
      const genForm = this.$refs.genInfo.$refs.genInfoForm
      Promise.all([basicForm, genForm].map((element) => this.getFormPromise(element))).then((response) => {
        const validateResult = response.every((item) => !!item)
        if (validateResult) {
          const genTable = Object.assign({}, basicForm.model, genForm.model)
          genTable.columns = this.columns
          genTable.params = {
            treeCode: genTable.treeCode,
            treeName: genTable.treeName,
            treeParentCode: genTable.treeParentCode,
            parentMenuId: genTable.parentMenuId,
          }
          useApiUpdateGenTable(this.tableId, genTable).then((response) => {
            appModal.msgSuccess('操作成功')
            this.close()
          })
        } else {
          appModal.msgError('表单校验未通过，请重新检查提交内容')
        }
      })
    },
    getFormPromise(form) {
      return new Promise((resolve) => {
        form.validate((response) => {
          resolve(response)
        })
      })
    },
    // 关闭按钮
    close() {
      const object = { path: '/tool/gen', query: { t: Date.now(), page: this.$route.query.page } }
      appTab.closeOpenPage(object)
    },
  },
}
</script>
