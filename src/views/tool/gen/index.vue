<template>
  <div>
    <TreeTable>
      <ElForm v-show="showSearch" ref="queryForm" slot="tableSearch" :inline="true" label-width="68px" :model="baseQueryParams" size="small">
        <ElFormItem label="表名称" prop="tableName">
          <ElInput v-model="baseQueryParams.tableName" clearable placeholder="请输入表名称" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="表描述" prop="tableComment">
          <ElInput v-model="baseQueryParams.tableComment" clearable placeholder="请输入表描述" @keyup.enter="handleQuery" />
        </ElFormItem>
        <ElFormItem label="创建时间">
          <ElDatePicker
            v-model="dateRange"
            class="w-60"
            :default-time="['00:00:00', '23:59:59']"
            end-placeholder="结束日期"
            range-separator="-"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></ElDatePicker>
        </ElFormItem>
        <ElFormItem>
          <ElButton icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</ElButton>
          <ElButton icon="el-icon-refresh" size="mini" @click="resetQuery">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <ElRow slot="actionLeftBtn" class="mb-3" :gutter="10">
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['tool:gen:code']" :disabled="multiple" icon="el-icon-download" plain size="mini" type="primary" @click="handleGenTable">生成</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasRole="['ADMIN']" icon="el-icon-plus" plain size="mini" type="primary" @click="openCreateTable">创建</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['tool:gen:import']" icon="el-icon-upload" plain size="mini" type="info" @click="openImportTable">导入</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['tool:gen:edit']" :disabled="single" icon="el-icon-edit" plain size="mini" type="success" @click="handleEditTable">修改</ElButton>
        </ElCol>
        <ElCol :span="1.5">
          <ElButton v-hasPermission="['tool:gen:remove']" :disabled="multiple" icon="el-icon-delete" plain size="mini" type="danger" @click="handleDeleteBatch">删除</ElButton>
        </ElCol>
      </ElRow>
      <RightToolbar slot="actionRightBtn" :show-search.sync="showSearch" @queryTable="getList"></RightToolbar>

      <template #rightTable="{ height: tableHeight }">
        <ElTable v-loading="loading" :data="tableList" :max-height="tableHeight" @selection-change="handleSelectionChange">
          <ElTableColumn align="center" header-align="center" type="selection" width="55"></ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="序号" type="index" width="50">
            <template #default="{ row, $index }">
              <span>{{ (queryParams.page - 1) * baseQueryParams.size + $index + 1 }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn align="center" header-align="center" label="表名称" prop="tableName" width="120" />
          <ElTableColumn align="center" header-align="center" label="表描述" prop="tableComment" width="120" />
          <ElTableColumn align="center" header-align="center" label="实体" prop="className" width="120" />
          <ElTableColumn align="center" header-align="center" label="创建时间" prop="createTime" width="160" />
          <ElTableColumn align="center" header-align="center" label="更新时间" prop="updateTime" width="160" />
          <ElTableColumn align="center" class-name="small-padding fixed-width" header-align="center" label="操作">
            <template #default="{ row }">
              <ElButton v-hasPermission="['tool:gen:preview']" icon="el-icon-view" size="small" type="text" @click="handlePreview(row)">预览</ElButton>
              <ElButton v-hasPermission="['tool:gen:edit']" icon="el-icon-edit" size="small" type="text" @click="handleEditTable(row)">编辑</ElButton>
              <ElButton v-hasPermission="['tool:gen:remove']" icon="el-icon-delete" size="small" type="text" @click="handleDelete(row)">删除</ElButton>
              <ElButton v-hasPermission="['tool:gen:edit']" icon="el-icon-refresh" size="small" type="text" @click="handleSynchDb(row)">同步</ElButton>
              <ElButton v-hasPermission="['tool:gen:code']" icon="el-icon-download" size="small" type="text" @click="handleGenTable(row)">生成代码</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <Pagination v-show="total > 0" slot="pagination" :limit.sync="baseQueryParams.size" :page.sync="queryParams.page" :total="total" @pagination="getList" />
    </TreeTable>
    <div>
      <!-- 预览界面 -->
      <CodePreviewDialog v-model="previewVisible" :preview-params="previewTableId" />
      <ImportTable ref="import" @ok="handleQuery" />
      <CreateTable ref="create" @ok="handleQuery" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 唯一标识符
      uniqueId: '',
      // 选中数组
      ids: [],
      // 选中表数组
      tableNames: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表数据
      tableList: [],
      // 日期范围
      dateRange: '',
      // 基础查询参数
      baseQueryParams: {
        page: 1,
        size: 10,
        tableName: undefined,
        tableComment: undefined,
      },
      // 预览对话框
      previewVisible: false,
      previewTableId: undefined,
    }
  },
  computed: {
    queryParams() {
      return {
        ...this.baseQueryParams,
        page: this.$route.query?.page || this.baseQueryParams.page,
        beginTime: this.dateRange?.[0],
        endTime: this.dateRange?.[1],
      }
    },
  },
  created() {
    this.getList()
  },
  activated() {
    const time = this.$route.query.t
    if (time && time !== this.uniqueId) {
      this.uniqueId = time
      this.getList()
    }
  },
  methods: {
    // 查询表集合
    getList(parameters = this.queryParams) {
      this.loading = true
      useApiGetGenTableList(parameters).then((response) => {
        this.tableList = response.records
        this.total = response.total
        this.loading = false
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList({
        ...this.queryParams,
        page: 1,
      })
    },
    // 生成代码操作
    handleGenTable(row) {
      const tableNames = row.tableName || this.tableNames
      if (tableNames === '') {
        appModal.msgError('请选择要生成的数据')
        return
      }
      if (row.genType === '1') {
        useApiGenerateCode(row.tableName).then(() => {
          appModal.msgSuccess(`成功生成到自定义路径：${row.genPath}`)
        })
      } else {
        useDownloader.zip({
          url: '/tool/gen/batch-download',
          data: {
            tables: tableNames,
          },
          filename: `代码生成_${tableNames}`,
        })
      }
    },
    // 同步数据库操作
    handleSynchDb(row) {
      const tableName = row.tableName
      appModal
        .confirm(`确认要强制同步"${tableName}"表结构吗？`)
        .then(() => {
          return useApiSyncDatabase(tableName)
        })
        .then(() => {
          appModal.msgSuccess('同步成功')
        })
        .catch(() => {})
    },
    // 打开导入表弹窗
    openImportTable() {
      this.$refs.import.show()
    },
    // 打开创建表弹窗
    openCreateTable() {
      this.$refs.create.show()
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 预览按钮
    handlePreview(row) {
      this.previewTableId = row.tableId
      this.previewVisible = true
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.tableId)
      this.tableNames = selection.map((item) => item.tableName)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },
    // 修改按钮操作
    handleEditTable(row) {
      const tableId = row.tableId || this.ids[0]
      const tableName = row.tableName || this.tableNames[0]
      const parameters = { page: 1 }
      appTab.openPage(`修改 [${tableName}] 生成配置`, `/tool/gen-edit/index/${tableId}`, parameters)
    },
    // 单个删除按钮操作
    handleDelete(row) {
      const tableId = row.tableId
      const tableName = row.tableName
      appModal
        .confirm(`是否确认删除表"${tableName}"？`)
        .then(() => {
          return useApiDeleteGenTable(tableId)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    // 批量删除按钮操作
    handleDeleteBatch() {
      const selectedCount = this.ids.length
      appModal
        .confirm(`是否确认删除选中的 ${selectedCount} 个表？`)
        .then(() => {
          return useApiBatchDeleteGenTables(this.ids)
        })
        .then(() => {
          this.getList()
          appModal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
  },
}
</script>
