<template>
  <div class="bg-cover flex h-screen items-center justify-center" :style="{ 'background-image': `url(${backgroundImage})` }">
    <ElForm ref="registerForm" class="bg-white p-5 pb-1 rounded-lg w-100" :model="registerForm" :rules="registerRules">
      <h3 class="mb-7 text-center text-gray-500 text-lg">{{ appTitle }}</h3>
      <ElFormItem prop="username">
        <ElInput v-model="registerForm.username" auto-complete="off" placeholder="账号" prefix-icon="el-icon-user" type="text"> </ElInput>
      </ElFormItem>
      <ElFormItem prop="password">
        <ElInput v-model="registerForm.password" auto-complete="off" placeholder="密码" prefix-icon="el-icon-lock" type="password" @keyup.enter="handleRegister"> </ElInput>
      </ElFormItem>
      <ElFormItem prop="confirmPassword">
        <ElInput v-model="registerForm.confirmPassword" auto-complete="off" placeholder="确认密码" prefix-icon="el-icon-lock" type="password" @keyup.enter="handleRegister">
        </ElInput>
      </ElFormItem>
      <ElFormItem v-if="captchaEnabled" prop="code">
        <div class="flex justify-between">
          <ElInput v-model="registerForm.code" auto-complete="off" class="flex-1" placeholder="验证码" prefix-icon="el-icon-chat-dot-square" @keyup.enter="handleRegister">
          </ElInput>
          <div class="bg-center bg-cover h-9 ml-2 w-24" :style="{ 'background-image': `url(${captchaImage})` }" @click="getCode"></div>
        </div>
      </ElFormItem>
      <ElFormItem class="w-full">
        <ElButton class="w-full" :loading="loading" size="medium" type="primary" @click.prevent="handleLogin">
          <span>{{ loading ? '登 录 中...' : '登 录' }}</span>
        </ElButton>
        <div class="float-right">
          <RouterLink class="link-type" :to="{ name: 'Login' }">使用已有账户登录</RouterLink>
        </div>
      </ElFormItem>
    </ElForm>
    <div class="bottom-3 fixed mx-auto text-white text-xs tracking-wide">{{ appCopyright }}</div>
  </div>
</template>

<script>
import backgroundImage from '@/assets/images/login-background.jpg'
export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password === value) {
        callback()
      } else {
        callback(new Error('两次输入的密码不一致'))
      }
    }
    return {
      backgroundImage,
      captchaImage: '',
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        code: '',
        uuid: '',
      },
      registerRules: {
        username: [
          { required: true, trigger: 'blur', message: '请输入您的账号' },
          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' },
        ],
        password: [
          { required: true, trigger: 'blur', message: '请输入您的密码' },
          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
          { pattern: /^[^"'<>\\|]+$/, message: String.raw`不能包含非法字符：< > " ' \ |`, trigger: 'blur' },
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', message: '请再次输入您的密码' },
          { required: true, validator: equalToPassword, trigger: 'blur' },
        ],
        code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
      },
      loading: false,
      captchaEnabled: true,
    }
  },
  computed: {
    appTitle() {
      return useEnvironment.VITE_TITLE
    },
    appCopyright() {
      return useEnvironment.VITE_COPYRIGHT
    },
  },
  created() {
    this.getCode()
  },
  methods: {
    getCode() {
      useApiGetCaptcha().then((response) => {
        this.captchaEnabled = response.enabled
        this.captchaImage = response?.image
        this.registerForm.uuid = response?.key
      })
    },
    handleRegister() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.loading = true
          useApiRegister(this.registerForm)
            .then((response) => {
              const username = this.registerForm.username
              this.$alert(`<font color='red'>恭喜你，您的账号 ${username} 注册成功！</font>`, '系统提示', {
                dangerouslyUseHTMLString: true,
                type: 'success',
              })
                .then(() => {
                  this.$router.push({ name: 'Login' })
                })
                .catch(() => {})
            })
            .catch(() => {
              this.loading = false
              if (this.captchaEnabled) {
                this.getCode()
              }
            })
        }
      })
    },
  },
}
</script>
