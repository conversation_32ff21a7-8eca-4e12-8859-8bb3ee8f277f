// 登录方法
export function useApiLogin(identifier, credential, code = '', uuid = '', authType = 'USERNAME') {
  const data = {
    identifier,
    credential,
    code,
    uuid,
    authType,
  }
  return useMyFetch({
    url: '/login',
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: 'post',
    data,
  })
}

// 注册方法
export function useApiRegister(data) {
  return useMyFetch({
    url: '/register',
    headers: {
      isToken: false,
    },
    method: 'post',
    data,
  })
}

// 退出方法
export function useApiLogout() {
  return useMyFetch({
    url: '/logout',
    method: 'post',
  })
}

// 获取验证码
export function useApiGetCaptcha() {
  return useMyFetch({
    url: '/captcha',
    method: 'get',
  })
}

// TODO 获取短信验证码
export function useApiGetSmsCaptcha(mobile) {
  return useMyFetch({
    url: '/sms-captcha',
    method: 'post',
    data: { mobile },
  })
}
