/**
 * 部门管理 API 接口模块
 *
 * 本模块提供部门管理的完整 RESTful API 接口，包括：
 * - 部门基础 CRUD 操作
 * - 部门树结构查询
 * - 部门排除节点查询
 *
 * 所有接口遵循 RESTful 设计规范，使用统一的错误处理和响应格式。
 *
 * @module system/depts
 */

/**
 * 查询部门列表
 * @param {Object} query - 查询参数
 * @param {string} [query.deptName] - 部门名称（模糊匹配）
 * @param {boolean} [query.status] - 部门状态（true-正常，false-停用）
 * @returns {Promise<Array>} 返回部门列表
 * @example
 * const depts = await useApiGetDeptList({ deptName: '技术部' })
 */
export function useApiGetDeptList(query) {
  return useMyFetch({
    url: '/system/depts',
    method: 'get',
    params: query,
  })
}

/**
 * 查询部门列表（排除指定节点及其子节点）
 * @param {number|string} deptId - 要排除的部门 ID
 * @returns {Promise<Array>} 返回排除指定节点后的部门列表
 * @example
 * const depts = await useApiGetDeptExcludeChildList(100)
 */
export function useApiGetDeptExcludeChildList(deptId) {
  return useMyFetch({
    url: `/system/depts/exclude/${deptId}`,
    method: 'get',
  })
}

/**
 * 查询部门详细信息
 * @param {number|string} deptId - 部门 ID
 * @returns {Promise<Object>} 返回部门详细信息
 * @example
 * const dept = await useApiGetDept(1)
 */
export function useApiGetDept(deptId) {
  return useMyFetch({
    url: `/system/depts/${deptId}`,
    method: 'get',
  })
}

/**
 * 新增部门
 * @param {Object} data - 部门数据
 * @param {string} data.deptName - 部门名称
 * @param {string} [data.leader] - 负责人
 * @param {string} [data.phone] - 联系电话
 * @param {string} [data.email] - 邮箱
 * @param {number} [data.orderNum] - 显示顺序
 * @param {boolean} [data.status=true] - 部门状态
 * @param {number|string} [data.parentId=0] - 父部门 ID
 * @returns {Promise<Object>} 返回创建成功的部门信息
 * @example
 * const newDept = await useApiAddDept({
 *   deptName: '技术部',
 *   leader: '张三',
 *   phone: '13800138000',
 *   email: '<EMAIL>',
 *   orderNum: 1
 * })
 */
export function useApiAddDept(data) {
  return useMyFetch({
    url: '/system/depts',
    method: 'post',
    data,
  })
}

/**
 * 修改部门信息
 * @param {number|string} deptId - 部门 ID
 * @param {Object} data - 部门数据（支持部分更新）
 * @param {string} [data.deptName] - 部门名称
 * @param {string} [data.leader] - 负责人
 * @param {string} [data.phone] - 联系电话
 * @param {string} [data.email] - 邮箱
 * @param {number} [data.orderNum] - 显示顺序
 * @param {boolean} [data.status] - 部门状态
 * @param {number|string} [data.parentId] - 父部门 ID
 * @returns {Promise<Object>} 返回更新后的部门信息
 * @example
 * await useApiUpdateDept(1, {
 *   deptName: '研发部',
 *   leader: '李四'
 * })
 */
export function useApiUpdateDept(deptId, data) {
  return useMyFetch({
    url: `/system/depts/${deptId}`,
    method: 'put',
    data,
  })
}

/**
 * 删除部门
 * @param {number|string} deptId - 部门 ID
 * @returns {Promise<void>} 无返回值
 * @example
 * await useApiDeleteDept(1)
 */
export function useApiDeleteDept(deptId) {
  return useMyFetch({
    url: `/system/depts/${deptId}`,
    method: 'delete',
  })
}

/**
 * 查询部门下拉树结构（用于用户管理中的部门选择）
 * @returns {Promise<Array>} 返回部门树结构
 * @example
 * const deptTree = await useApiGetDeptTreeSelect()
 */
export function useApiGetDeptTreeSelect() {
  return useMyFetch({
    url: '/system/depts/tree',
    method: 'get',
  })
}

/**
 * 获取部门下拉选项列表
 * @returns {Promise<Array>} 返回部门下拉选项列表
 * @example
 * const deptOptions = await useApiGetDeptOptions()
 */
export function useApiGetDeptOptions() {
  return useMyFetch({
    url: '/system/depts/options',
    method: 'get',
  })
}
