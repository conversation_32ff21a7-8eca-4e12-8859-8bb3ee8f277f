// 查询岗位列表
export function useApiGetPostList(query) {
  return useMyFetch({
    url: '/system/posts',
    method: 'get',
    params: query,
  })
}

// 查询岗位详细
export function useApiGetPost(postId) {
  return useMyFetch({
    url: `/system/posts/${postId}`,
    method: 'get',
  })
}

// 新增岗位
export function useApiAddPost(data) {
  return useMyFetch({
    url: '/system/posts',
    method: 'post',
    data,
  })
}

// 修改岗位
export function useApiUpdatePost(postId, data) {
  return useMyFetch({
    url: `/system/posts/${postId}`,
    method: 'put',
    data,
  })
}

// 删除岗位
export function useApiDelPost(postId) {
  return useMyFetch({
    url: `/system/posts/${postId}`,
    method: 'delete',
  })
}

// 获取岗位下拉选项列表
export function useApiGetPostOptions() {
  return useMyFetch({
    url: '/system/posts/options',
    method: 'get',
  })
}
