/**
 * 角色管理 API 接口模块
 *
 * 本模块提供角色管理的完整 RESTful API 接口，包括：
 * - 角色基础 CRUD 操作
 * - 角色状态和数据权限管理
 * - 用户角色授权管理
 * - 部门权限查询
 *
 * 所有接口遵循 RESTful 设计规范，使用统一的错误处理和响应格式。
 *
 * @module system/roles
 */

/**
 * 查询角色列表
 * @param {Object} query - 查询参数
 * @param {number} query.page - 页码（从 1 开始）
 * @param {number} query.size - 每页大小
 * @param {string} [query.roleName] - 角色名称（模糊匹配）
 * @param {string} [query.roleKey] - 角色标识（模糊匹配）
 * @param {boolean} [query.status] - 角色状态（true-启用，false-停用）
 * @param {string} [query.beginTime] - 开始时间
 * @param {string} [query.endTime] - 结束时间
 * @returns {Promise<{records: Array, total: number}>} 返回角色列表和总数
 * @example
 * const { records, total } = await useApiGetRoleList({ page: 1, size: 10, roleName: '管理员' })
 */
export function useApiGetRoleList(query) {
  return useMyFetch({
    url: '/system/roles',
    method: 'get',
    params: query,
  })
}

/**
 * 查询角色详细信息
 * @param {number|string} roleId - 角色 ID
 * @returns {Promise<Object>} 返回角色详细信息
 * @example
 * const role = await useApiGetRole(1)
 */
export function useApiGetRole(roleId) {
  return useMyFetch({
    url: `/system/roles/${roleId}`,
    method: 'get',
  })
}

/**
 * 新增角色
 * @param {Object} data - 角色数据
 * @param {string} data.roleName - 角色名称
 * @param {string} data.roleKey - 角色标识（建议大写）
 * @param {number} data.roleSort - 角色排序
 * @param {boolean} [data.status=true] - 角色状态
 * @param {Array<number>} [data.menuIds] - 菜单权限 ID 列表
 * @param {string} [data.remark] - 角色备注
 * @returns {Promise<Object>} 返回创建成功的角色信息
 * @example
 * const newRole = await useApiAddRole({
 *   roleName: '测试角色',
 *   roleKey: 'TEST',
 *   roleSort: 100,
 *   status: true,
 *   menuIds: [1, 2, 3]
 * })
 */
export function useApiAddRole(data) {
  return useMyFetch({
    url: '/system/roles',
    method: 'post',
    data,
  })
}

/**
 * 修改角色信息（统一接口，支持基本信息、状态、数据权限等）
 * @param {number|string} roleId - 角色 ID
 * @param {Object} data - 角色数据（支持部分更新）
 * @param {string} [data.roleName] - 角色名称
 * @param {string} [data.roleKey] - 角色标识
 * @param {number} [data.roleSort] - 角色排序
 * @param {boolean} [data.status] - 角色状态（true-启用，false-停用）
 * @param {string} [data.dataScope] - 数据权限范围（1-全部，2-自定义，3-本部门，4-本部门及以下，5-仅本人）
 * @param {Array<number>} [data.deptIds] - 部门权限 ID 列表（当 dataScope=2 时生效）
 * @param {Array<number>} [data.menuIds] - 菜单权限 ID 列表
 * @param {string} [data.remark] - 角色备注
 * @returns {Promise<Object>} 返回更新后的角色信息
 * @example
 * // 修改角色状态
 * await useApiUpdateRole(1, { status: false })
 *
 * // 修改数据权限
 * await useApiUpdateRole(1, {
 *   dataScope: '2',
 *   deptIds: [1, 2, 3]
 * })
 *
 * // 修改基本信息
 * await useApiUpdateRole(1, {
 *   roleName: '新角色名',
 *   roleKey: 'NEW_KEY'
 * })
 */
export function useApiUpdateRole(roleId, data) {
  return useMyFetch({
    url: `/system/roles/${roleId}`,
    method: 'put',
    data,
  })
}

/**
 * 删除角色（单条删除）
 * @param {number|string} roleId - 角色 ID
 * @returns {Promise<void>} 无返回值
 * @example
 * await useApiDelRole(1)
 */
export function useApiDelRole(roleId) {
  return useMyFetch({
    url: `/system/roles/${roleId}`,
    method: 'delete',
  })
}

/**
 * 批量删除角色
 * @param {Array<number|string>} roleIds - 角色 ID 数组
 * @returns {Promise<void>} 无返回值
 * @example
 * await useApiDelRoles([1, 2, 3])
 */
export function useApiDelRoles(roleIds) {
  return useMyFetch({
    url: '/system/roles',
    method: 'delete',
    data: roleIds,
  })
}

/**
 * 查询角色已授权用户列表
 * @param {number|string} roleId - 角色 ID
 * @param {Object} query - 查询参数
 * @param {number} query.page - 页码（从 1 开始）
 * @param {number} query.size - 每页大小
 * @param {string} [query.username] - 用户名称（模糊匹配）
 * @param {string} [query.mobile] - 手机号码（模糊匹配）
 * @returns {Promise<{records: Array, total: number}>} 返回用户列表和总数
 * @example
 * const { records, total } = await useApiGetRoleUsers(1, { page: 1, size: 10 })
 */
export function useApiGetRoleUsers(roleId, query) {
  return useMyFetch({
    url: `/system/roles/${roleId}/users`,
    method: 'get',
    params: query,
  })
}

/**
 * 查询角色未授权用户列表（候选用户）
 * @param {number|string} roleId - 角色 ID
 * @param {Object} query - 查询参数
 * @param {number} query.page - 页码（从 1 开始）
 * @param {number} query.size - 每页大小
 * @param {string} [query.username] - 用户名称（模糊匹配）
 * @param {string} [query.mobile] - 手机号码（模糊匹配）
 * @returns {Promise<{records: Array, total: number}>} 返回候选用户列表和总数
 * @example
 * const { records, total } = await useApiGetRoleUserCandidates(1, { page: 1, size: 10 })
 */
export function useApiGetRoleUserCandidates(roleId, query) {
  return useMyFetch({
    url: `/system/roles/${roleId}/users/candidates`,
    method: 'get',
    params: query,
  })
}

/**
 * 撤销用户角色授权（支持单个和批量撤销）
 * @param {number|string} roleId - 角色 ID
 * @param {Array<number|string>} userIds - 用户 ID 数组
 * @returns {Promise<void>} 无返回值
 * @example
 * // 单个撤销
 * await useApiRemoveRoleUsers(1, [100])
 *
 * // 批量撤销
 * await useApiRemoveRoleUsers(1, [100, 101, 102])
 */
export function useApiRemoveRoleUsers(roleId, userIds) {
  return useMyFetch({
    url: `/system/roles/${roleId}/users`,
    method: 'delete',
    data: userIds,
  })
}

/**
 * 授权用户角色（支持单个和批量授权）
 * @param {number|string} roleId - 角色 ID
 * @param {Array<number|string>} userIds - 用户 ID 数组
 * @returns {Promise<void>} 无返回值
 * @example
 * // 单个授权
 * await useApiAddRoleUsers(1, [100])
 *
 * // 批量授权
 * await useApiAddRoleUsers(1, [100, 101, 102])
 */
export function useApiAddRoleUsers(roleId, userIds) {
  return useMyFetch({
    url: `/system/roles/${roleId}/users`,
    method: 'put',
    data: userIds,
  })
}

/**
 * 根据角色 ID 查询部门树结构（用于数据权限设置）
 * @param {number|string} roleId - 角色 ID
 * @returns {Promise<{depts: Array, checkedKeys: Array}>} 返回部门树结构和已选中的部门 ID
 * @example
 * const { depts, checkedKeys } = await useApiGetRoleDeptTree(1)
 */
export function useApiGetRoleDeptTree(roleId) {
  return useMyFetch({
    url: `/system/roles/${roleId}/dept-tree`,
    method: 'get',
  })
}

/**
 * 获取角色下拉选项列表
 * @returns {Promise<Array>} 返回角色下拉选项列表
 * @example
 * const roleOptions = await useApiGetRoleOptions()
 */
export function useApiGetRoleOptions() {
  return useMyFetch({
    url: '/system/roles/options',
    method: 'get',
  })
}
