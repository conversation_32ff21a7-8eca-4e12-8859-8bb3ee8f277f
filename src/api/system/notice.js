/**
 * 公告管理 API 接口模块
 *
 * 本模块提供公告管理的完整 RESTful API 接口，包括：
 * - 公告基础 CRUD 操作
 * - 公告状态管理
 * - 公告类型管理
 *
 * 所有接口遵循 RESTful 设计规范，使用统一的错误处理和响应格式。
 *
 * @module system/notices
 */

/**
 * 查询公告列表
 * @param {Object} query - 查询参数
 * @param {string} [query.noticeTitle] - 公告标题（模糊匹配）
 * @param {string} [query.noticeType] - 公告类型
 * @param {string} [query.createBy] - 创建者
 * @param {boolean} [query.status] - 公告状态（true-正常，false-关闭）
 * @returns {Promise<{records: Array, total: number}>} 返回公告列表和总数
 * @example
 * const { records, total } = await useApiGetNoticeList({ noticeTitle: '系统维护', status: true })
 */
export function useApiGetNoticeList(query) {
  return useMyFetch({
    url: '/system/notices',
    method: 'get',
    params: query,
  })
}

/**
 * 查询公告详细信息
 * @param {number|string} noticeId - 公告 ID
 * @returns {Promise<Object>} 返回公告详细信息
 * @example
 * const notice = await useApiGetNotice(1)
 */
export function useApiGetNotice(noticeId) {
  return useMyFetch({
    url: `/system/notices/${noticeId}`,
    method: 'get',
  })
}

/**
 * 新增公告
 * @param {Object} data - 公告数据
 * @param {string} data.noticeTitle - 公告标题
 * @param {string} data.noticeType - 公告类型
 * @param {string} data.noticeContent - 公告内容
 * @param {boolean} [data.status=true] - 公告状态（true-正常，false-关闭）
 * @param {string} [data.remark] - 公告备注
 * @returns {Promise<Object>} 返回创建成功的公告信息
 * @example
 * const newNotice = await useApiAddNotice({
 *   noticeTitle: '系统维护通知',
 *   noticeType: '1',
 *   noticeContent: '系统将于今晚 22:00-24:00 进行维护',
 *   status: true
 * })
 */
export function useApiAddNotice(data) {
  return useMyFetch({
    url: '/system/notices',
    method: 'post',
    data,
  })
}

/**
 * 修改公告信息
 * @param {Object} data - 公告数据
 * @param {number|string} noticeId - 公告 ID
 * @param {string} [data.noticeTitle] - 公告标题
 * @param {string} [data.noticeType] - 公告类型
 * @param {string} [data.noticeContent] - 公告内容
 * @param {boolean} [data.status] - 公告状态
 * @param {string} [data.remark] - 公告备注
 * @returns {Promise<Object>} 返回更新后的公告信息
 * @example
 * await useApiUpdateNotice({
 *   noticeId: 1,
 *   noticeTitle: '系统维护通知（更新）',
 *   noticeContent: '系统维护时间调整为 23:00-01:00'
 * })
 */
export function useApiUpdateNotice(noticeId, data) {
  return useMyFetch({
    url: `/system/notices/${noticeId}`,
    method: 'put',
    data,
  })
}

/**
 * 删除公告
 * @param {number|string} noticeId - 公告 ID
 * @returns {Promise<void>} 无返回值
 * @example
 * await useApiDeleteNotice(1)
 */
export function useApiDeleteNotice(noticeId) {
  return useMyFetch({
    url: `/system/notices/${noticeId}`,
    method: 'delete',
  })
}
