/**
 * 菜单管理 API 接口模块
 *
 * 本模块提供菜单管理的完整 RESTful API 接口，包括：
 * - 菜单基础 CRUD 操作
 * - 菜单树结构查询
 * - 角色菜单权限查询
 * - 菜单路径查询
 *
 * 所有接口遵循 RESTful 设计规范，使用统一的错误处理和响应格式。
 *
 * @module system/menus
 */

/**
 * 查询菜单列表
 * @param {Object} query - 查询参数
 * @param {string} [query.menuName] - 菜单名称（模糊匹配）
 * @param {boolean} [query.status] - 菜单状态（true-显示，false-隐藏）
 * @returns {Promise<Array>} 返回菜单列表
 * @example
 * const menus = await useApiGetMenuList({ menuName: '系统管理' })
 */
export function useApiGetMenuList(query) {
  return useMyFetch({
    url: '/system/menus',
    method: 'get',
    params: query,
  })
}

/**
 * 查询菜单列表（包含完整路径信息）
 * @param {Object} query - 查询参数
 * @param {string} [query.menuName] - 菜单名称（模糊匹配）
 * @param {boolean} [query.status] - 菜单状态（true-显示，false-隐藏）
 * @returns {Promise<Array>} 返回包含路径信息的菜单列表
 * @example
 * const menus = await useApiGetMenuListWithPath({ menuName: '系统管理' })
 */
export function useApiGetMenuListWithPath(query) {
  return useMyFetch({
    url: '/system/menus/list-with-path',
    method: 'get',
    params: query,
  })
}

/**
 * 查询菜单详细信息
 * @param {number|string} menuId - 菜单 ID
 * @returns {Promise<Object>} 返回菜单详细信息
 * @example
 * const menu = await useApiGetMenu(1)
 */
export function useApiGetMenu(menuId) {
  return useMyFetch({
    url: `/system/menus/${menuId}`,
    method: 'get',
  })
}

/**
 * 查询菜单下拉树结构
 * @returns {Promise<Array>} 返回菜单树结构
 * @example
 * const menuTree = await useApiGetMenuTreeselect()
 */
export function useApiGetMenuTreeselect() {
  return useMyFetch({
    url: '/system/menus/tree',
    method: 'get',
  })
}

/**
 * 根据角色 ID 查询菜单下拉树结构
 * @param {number|string} roleId - 角色 ID
 * @returns {Promise<{menus: Array, checkedKeys: Array}>} 返回菜单树结构和已选中的菜单 ID
 * @example
 * const { menus, checkedKeys } = await useApiGetMenuTreeselectByRole(1)
 */
export function useApiGetMenuTreeselectByRole(roleId) {
  return useMyFetch({
    url: `/system/menus/roles/${roleId}/menu-tree`,
    method: 'get',
  })
}

/**
 * 新增菜单
 * @param {Object} data - 菜单数据
 * @param {string} data.menuName - 菜单名称
 * @param {string} data.menuType - 菜单类型（M-目录，C-菜单，F-按钮）
 * @param {string} [data.path] - 路由地址
 * @param {string} [data.component] - 组件路径
 * @param {string} [data perms] - 权限标识
 * @param {string} [data.icon] - 菜单图标
 * @param {number} [data.orderNum] - 显示顺序
 * @param {boolean} [data.visible=true] - 是否显示
 * @param {boolean} [data.status=true] - 菜单状态
 * @param {number|string} [data.parentId=0] - 父菜单 ID
 * @returns {Promise<Object>} 返回创建成功的菜单信息
 * @example
 * const newMenu = await useApiAddMenu({
 *   menuName: '用户管理',
 *   menuType: 'C',
 *   path: 'user',
 *   component: 'system/user/index',
 *   perms: 'system:user:list',
 *   orderNum: 1
 * })
 */
export function useApiAddMenu(data) {
  return useMyFetch({
    url: '/system/menus',
    method: 'post',
    data,
  })
}

/**
 * 修改菜单信息
 * @param {number|string} menuId - 菜单 ID
 * @param {Object} data - 菜单数据（支持部分更新）
 * @param {string} [data.menuName] - 菜单名称
 * @param {string} [data.menuType] - 菜单类型
 * @param {string} [data.path] - 路由地址
 * @param {string} [data.component] - 组件路径
 * @param {string} [data.perms] - 权限标识
 * @param {string} [data.icon] - 菜单图标
 * @param {number} [data.orderNum] - 显示顺序
 * @param {boolean} [data.visible] - 是否显示
 * @param {boolean} [data.status] - 菜单状态
 * @param {number|string} [data.parentId] - 父菜单 ID
 * @returns {Promise<Object>} 返回更新后的菜单信息
 * @example
 * await useApiUpdateMenu(1, {
 *   menuName: '用户管理',
 *   path: 'user-management'
 * })
 */
export function useApiUpdateMenu(menuId, data) {
  return useMyFetch({
    url: `/system/menus/${menuId}`,
    method: 'put',
    data,
  })
}

/**
 * 删除菜单
 * @param {number|string} menuId - 菜单 ID
 * @returns {Promise<void>} 无返回值
 * @example
 * await useApiDeleteMenu(1)
 */
export function useApiDeleteMenu(menuId) {
  return useMyFetch({
    url: `/system/menus/${menuId}`,
    method: 'delete',
  })
}
