// 查询字典类型列表
export function useApiGetDictTypeList(query) {
  return useMyFetch({
    url: '/system/dict/type',
    method: 'get',
    params: query,
  })
}

// 查询字典类型详细
export function useApiGetDictType(dictId) {
  return useMyFetch({
    url: `/system/dict/type/${dictId}`,
    method: 'get',
  })
}

// 新增字典类型
export function useApiAddDictType(data) {
  return useMyFetch({
    url: '/system/dict/type',
    method: 'post',
    data,
  })
}

// 修改字典类型
export function useApiUpdateDictType(dictId, data) {
  return useMyFetch({
    url: `/system/dict/type/${dictId}`,
    method: 'put',
    data,
  })
}

// 删除字典类型（单个）
export function useApiDelDictType(dictId) {
  return useMyFetch({
    url: `/system/dict/type/${dictId}`,
    method: 'delete',
  })
}

// 批量删除字典类型
export function useApiDelDictTypeBatch(dictIds) {
  return useMyFetch({
    url: '/system/dict/type/batch',
    method: 'delete',
    data: dictIds,
  })
}

// 刷新字典缓存
export function useApiRefreshDictTypeCache() {
  return useMyFetch({
    url: '/system/dict/type/cache',
    method: 'delete',
  })
}

// 获取字典选择框列表
export function useApiGetDictOptionSelect() {
  return useMyFetch({
    url: '/system/dict/type/options',
    method: 'get',
  })
}
