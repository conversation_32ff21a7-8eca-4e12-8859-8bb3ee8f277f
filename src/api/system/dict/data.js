// 查询字典数据列表
export function useApiGetDictDataList(query) {
  return useMyFetch({
    url: '/system/dict/data',
    method: 'get',
    params: query,
  })
}

// 查询字典数据详细
export function useApiGetDictData(dictCode) {
  return useMyFetch({
    url: `/system/dict/data/${dictCode}`,
    method: 'get',
  })
}

// 根据字典类型查询字典数据信息
export function useApiGetDictByType(dictType) {
  return useMyFetch({
    url: `/system/dict/data/type/${dictType}`,
    method: 'get',
  })
}

// 新增字典数据
export function useApiAddDictData(data) {
  return useMyFetch({
    url: '/system/dict/data',
    method: 'post',
    data,
  })
}

// 修改字典数据
export function useApiUpdateDictData(dictCode, data) {
  return useMyFetch({
    url: `/system/dict/data/${dictCode}`,
    method: 'put',
    data,
  })
}

// 删除字典数据（单个）
export function useApiDelDictData(dictCode) {
  return useMyFetch({
    url: `/system/dict/data/${dictCode}`,
    method: 'delete',
  })
}

// 批量删除字典数据
export function useApiDelDictDataBatch(dictCodes) {
  return useMyFetch({
    url: '/system/dict/data/batch',
    method: 'delete',
    data: dictCodes,
  })
}
