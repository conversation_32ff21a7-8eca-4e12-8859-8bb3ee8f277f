// 查询参数列表
export function useApiGetConfigList(query) {
  return useMyFetch({
    url: '/system/config',
    method: 'get',
    params: query,
  })
}

// 查询参数详细
export function useApiGetConfig(configId) {
  return useMyFetch({
    url: `/system/config/${configId}`,
    method: 'get',
  })
}

// 根据参数键名查询参数值
export function useApiGetConfigKey(configKey) {
  return useMyFetch({
    url: `/system/config/configKey/${configKey}`,
    method: 'get',
  })
}

// 新增参数配置
export function useApiAddConfig(data) {
  return useMyFetch({
    url: '/system/config',
    method: 'post',
    data,
  })
}

// 修改参数配置
export function useApiUpdateConfig(configId, data) {
  return useMyFetch({
    url: `/system/config/${configId}`,
    method: 'put',
    data,
  })
}

// 删除参数配置
export function useApiDelConfig(configId) {
  return useMyFetch({
    url: `/system/config/${configId}`,
    method: 'delete',
  })
}

// 刷新参数缓存
export function useApiRefreshConfigCache() {
  return useMyFetch({
    url: '/system/config/refreshCache',
    method: 'delete',
  })
}
