// 查询用户列表
export function useApiGetUserList(query) {
  return useMyFetch({
    url: '/system/users',
    method: 'get',
    params: query,
  })
}

// 查询用户详细
export function useApiGetUser(userId) {
  return useMyFetch({
    url: `/system/users/${userId}`,
    method: 'get',
  })
}

// 新增用户
export function useApiAddUser(data) {
  return useMyFetch({
    url: '/system/users',
    method: 'post',
    data,
  })
}

// 修改用户
export function useApiUpdateUser(userId, data) {
  return useMyFetch({
    url: `/system/users/${userId}`,
    method: 'put',
    data,
  })
}

// 删除用户
export function useApiDelUser(userId) {
  return useMyFetch({
    url: `/system/users/${userId}`,
    method: 'delete',
  })
}

// 用户密码重置
export function useApiResetUserPassword(userId, password) {
  const data = {
    newPassword: password,
  }
  return useMyFetch({
    url: `/system/users/${userId}/password`,
    method: 'put',
    data,
  })
}

// 用户状态修改
export function useApiUpdateUserStatus(userId, status) {
  const data = {
    status,
  }
  return useMyFetch({
    url: `/system/users/${userId}/status`,
    method: 'put',
    data,
  })
}

// 查询授权角色
export function useApiGetAuthRole(userId) {
  return useMyFetch({
    url: `/system/users/${userId}/roles`,
    method: 'get',
  })
}

// 保存授权角色
export function useApiUpdateAuthRole(userId, roleIds) {
  const data = {
    roleIds,
  }
  return useMyFetch({
    url: `/system/users/${userId}/roles`,
    method: 'put',
    data,
  })
}

// 查询部门下拉树结构
// 废弃
export function useApiGetUserDeptTreeSelect() {
  return useMyFetch({
    url: '/system/users/dept-tree',
    method: 'get',
  })
}
