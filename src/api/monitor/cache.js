// 查询缓存详细
export function useApiGetCache() {
  return useMyFetch({
    url: '/monitor/cache',
    method: 'get',
  })
}

// 查询缓存名称列表
export function useApiGetCacheNameList() {
  return useMyFetch({
    url: '/monitor/cache/names',
    method: 'get',
  })
}

// 查询缓存键名列表
export function useApiGetCacheKeyList(cacheName) {
  return useMyFetch({
    url: `/monitor/cache/${cacheName}/keys`,
    method: 'get',
  })
}

// 查询缓存内容
export function useApiGetCacheValue(cacheName, cacheKey) {
  return useMyFetch({
    url: `/monitor/cache/${cacheName}/keys/${cacheKey}`,
    method: 'get',
  })
}

// 清理指定名称缓存
export function useApiCleanCacheName(cacheName) {
  return useMyFetch({
    url: `/monitor/cache/${cacheName}`,
    method: 'delete',
  })
}

// 清理指定键名缓存
export function useApiCleanCacheKey(cacheKey) {
  return useMyFetch({
    url: `/monitor/cache/keys/${cacheKey}`,
    method: 'delete',
  })
}

// 清理全部缓存
export function useApiCleanCacheAll() {
  return useMyFetch({
    url: '/monitor/cache/all',
    method: 'delete',
  })
}
