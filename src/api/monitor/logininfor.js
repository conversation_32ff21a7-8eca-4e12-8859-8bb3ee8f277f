// 查询登录日志列表
export function useApiGetLoginLogList(query) {
  return useMyFetch({
    url: '/monitor/logininfor',
    method: 'get',
    params: query,
  })
}

// 删除登录日志
export function useApiDelLoginLog(infoId) {
  return useMyFetch({
    url: `/monitor/logininfor/${infoId}`,
    method: 'delete',
  })
}

// 批量删除登录日志
export function useApiBatchDelLoginLog(infoIds) {
  return useMyFetch({
    url: '/monitor/logininfor/batch',
    method: 'delete',
    data: infoIds,
  })
}

// 解锁用户登录状态
export function useApiUnlockLoginStatus(userId) {
  return useMyFetch({
    url: `/monitor/logininfor/unlock/${userId}`,
    method: 'post',
  })
}

// 清空登录日志
export function useApiCleanLoginLog() {
  return useMyFetch({
    url: '/monitor/logininfor/clean',
    method: 'delete',
  })
}
