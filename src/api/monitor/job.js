/**
 * 获取定时任务详情
 * @param {number|string} jobId - 任务 ID
 * @returns {Promise} 返回任务详情数据
 */
export function useApiGetJob(jobId) {
  return useMyFetch({
    url: `/monitor/job/${jobId}`,
    method: 'get',
  })
}

/**
 * 分页查询定时任务列表
 * @param {Object} query - 查询参数
 * @param {number} query.pageNum - 页码
 * @param {number} query.pageSize - 每页数量
 * @param {string} query.jobName - 任务名称（可选）
 * @param {string} query.jobGroup - 任务组名（可选）
 * @param {string} query.status - 任务状态（可选）
 * @returns {Promise} 返回任务列表数据
 */
export function useApiGetJobList(query) {
  return useMyFetch({
    url: '/monitor/job',
    method: 'get',
    params: query,
  })
}

/**
 * 导出定时任务列表
 * @param {Object} query - 查询参数
 * @param {string} query.jobName - 任务名称（可选）
 * @param {string} query.jobGroup - 任务组名（可选）
 * @param {string} query.status - 任务状态（可选）
 * @returns {Promise} 返回导出文件数据
 */
export function useApiExportJob(query) {
  return useMyFetch({
    url: '/monitor/job/export',
    method: 'post',
    params: query,
  })
}

/**
 * 创建定时任务
 * @param {Object} data - 任务数据
 * @param {string} data.jobName - 任务名称
 * @param {string} data.jobGroup - 任务组名（DEFAULT/SYSTEM）
 * @param {string} data.invokeTarget - 调用目标（完整类路径）
 * @param {string} data.cronExpression - cron 表达式
 * @param {string} [data.misfirePolicy] - 错误策略（默认/立即执行/执行一次）
 * @param {string|boolean} data.concurrent - 是否并发执行（0/1 或 false/true）
 * @param {string|boolean} data.status - 任务状态（0/1 或 false/true）
 * @param {string} [data.remark] - 备注
 * @returns {Promise} 返回创建结果
 *
 * @example
 * const jobData = {
 *   jobName: "测试任务",
 *   jobGroup: "DEFAULT",
 *   invokeTarget: "com.xinjian.quartz.task.TestTask.execute",
 *   cronExpression: "0/30 * * * * ?",
 *   concurrent: "0",
 *   status: true
 * };
 * useApiAddJob(jobData)
 */
export function useApiAddJob(data) {
  return useMyFetch({
    url: '/monitor/job',
    method: 'post',
    data,
  })
}

/**
 * 完整更新定时任务
 * @param {number|string} jobId - 任务 ID
 * @param {Object} data - 任务数据
 * @returns {Promise} 返回更新结果
 */
export function useApiUpdateJob(jobId, data) {
  return useMyFetch({
    url: `/monitor/job/${jobId}`,
    method: 'put',
    data,
  })
}

/**
 * 部分更新定时任务
 * @param {number|string} jobId - 任务 ID
 * @param {Object} data - 部分更新的任务数据
 * @returns {Promise} 返回更新结果
 */
export function useApiPatchJob(jobId, data) {
  return useMyFetch({
    url: `/monitor/job/${jobId}`,
    method: 'patch',
    data,
  })
}

/**
 * 删除定时任务
 * @param {number|string} jobId - 任务 ID
 * @returns {Promise} 返回删除结果
 */
export function useApiDelJob(jobId) {
  return useMyFetch({
    url: `/monitor/job/${jobId}`,
    method: 'delete',
  })
}

/**
 * 批量删除定时任务
 * @param {Array<number|string>} jobIds - 任务 ID 数组
 * @returns {Promise} 返回删除结果
 */
export function useApiBatchDelJob(jobIds) {
  return useMyFetch({
    url: '/monitor/job/batch',
    method: 'delete',
    data: { jobIds },
  })
}

/**
 * 立即执行定时任务一次
 * @param {number|string} jobId - 任务 ID
 * @param {string} jobGroup - 任务组名
 * @returns {Promise} 返回执行结果
 */
export function useApiRunJob(jobId, jobGroup) {
  const data = {
    jobId,
    jobGroup,
  }
  return useMyFetch({
    url: `/monitor/job/${jobId}/execute`,
    method: 'post',
    data,
  })
}
