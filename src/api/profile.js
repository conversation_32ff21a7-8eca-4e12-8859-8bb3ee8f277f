// 获取用户详细信息
export function useApiGetInfo() {
  return useMyFetch({
    url: '/user/authinfo',
    method: 'get',
  })
}

// 查询用户个人信息
export function useApiGetUserProfile() {
  return useMyFetch({
    url: '/user/profile',
    method: 'get',
  })
}

// 修改用户个人信息
export function useApiUpdateUserProfile(data) {
  return useMyFetch({
    url: '/user/profile',
    method: 'put',
    data,
  })
}

// 用户密码重置
export function useApiUpdateUserPassword(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword,
  }
  return useMyFetch({
    url: '/user/profile/password',
    method: 'put',
    data,
  })
}

// 用户头像上传
export function useApiUploadAvatar(data) {
  return useMyFetch({
    url: '/user/profile/avatar',
    method: 'put',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data,
  })
}
