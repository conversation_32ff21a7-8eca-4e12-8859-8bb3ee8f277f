// 分页查询代码生成列表
export function useApiGetGenTableList(query) {
  return useMyFetch({
    url: '/tool/gen',
    method: 'get',
    params: query,
  })
}

// 查询数据库表列表
export function useApiGetDatabaseTableList(query) {
  return useMyFetch({
    url: '/tool/gen/db',
    method: 'get',
    params: query,
  })
}

// 根据表 ID 获取代码生成详细信息
export function useApiGetGenTableInfo(tableId) {
  return useMyFetch({
    url: `/tool/gen/${tableId}`,
    method: 'get',
  })
}

// 更新表
export function useApiUpdateGenTable(tableId, data) {
  return useMyFetch({
    url: `/tool/gen/${tableId}`,
    method: 'put',
    data,
  })
}

// 导入表
export function useApiImportTable(tables) {
  return useMyFetch({
    url: '/tool/gen/import',
    method: 'post',
    params: { tables },
  })
}

// 创建表
export function useApiCreateTable(sql) {
  return useMyFetch({
    url: '/tool/gen/create',
    method: 'post',
    params: { sql },
  })
}

// 查询数据表字段列表
export function useApiGetGenTableColumns(tableId) {
  return useMyFetch({
    url: `/tool/gen/${tableId}/columns`,
    method: 'get',
  })
}

// 预览代码
export function useApiPreviewCode(tableId) {
  return useMyFetch({
    url: `/tool/gen/${tableId}/preview`,
    method: 'get',
  })
}

// 下载代码
export function useApiDownloadCode(tableName) {
  return useMyFetch({
    url: `/tool/gen/${tableName}/download`,
    method: 'get',
  })
}

// 批量下载代码
export function useApiBatchDownload(tables) {
  return useMyFetch({
    url: '/tool/gen/batch-download',
    method: 'get',
    params: { tables },
  })
}

// 删除代码生成表
export function useApiDeleteGenTable(tableId) {
  return useMyFetch({
    url: `/tool/gen/${tableId}`,
    method: 'delete',
  })
}

// 批量删除代码生成表
export function useApiBatchDeleteGenTables(tableIds) {
  return useMyFetch({
    url: '/tool/gen/batch',
    method: 'delete',
    data: tableIds,
  })
}

// 生成代码（自定义路径）
export function useApiGenerateCode(tableName) {
  return useMyFetch({
    url: `/tool/gen/${tableName}/generate`,
    method: 'post',
  })
}

// 同步数据库
export function useApiSyncDatabase(tableName) {
  return useMyFetch({
    url: `/tool/gen/sync/${tableName}`,
    method: 'post',
  })
}
