import Vue from 'vue'
import VueECharts from 'vue-echarts'
import 'echarts/theme/macarons.js'
import 'echarts/theme/macarons2.js'

import { use } from 'echarts/core'

// import ECharts modules manually to reduce bundle size
import { <PERSON>vas<PERSON>enderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import { LegendComponent, TitleComponent, TooltipComponent } from 'echarts/components'

use([<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ge<PERSON><PERSON>, TitleComponent, TooltipComponent, LegendComponent])

Vue.component('<PERSON>hart', VueECharts)
