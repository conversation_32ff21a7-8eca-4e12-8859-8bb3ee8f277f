import Vue from 'vue'
import Router from 'vue-router'
import store from '@/store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import modules from './modules/common'

Vue.use(Router)

// 防止连续点击多次路由报错
const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((error) => error)
}

// 白名单路由，不需要携带 token 的页面
const whiteList = new Set(['/login', '/oauth', '/dingtalk', '/auth-redirect', '/bind', '/register'])

const router = new Router({
  mode: useEnvironment.VITE_ROUTER_MODE,
  scrollBehavior: () => ({ y: 0 }),
  routes: modules,
  base: useEnvironment.VITE_BASE_URL,
})

NProgress.configure({ showSpinner: false })
router.beforeEach((to, from, next) => {
  NProgress.start()
  // 统一处理标题，未配置 meta.title 则置空，由 settings 模块按开关拼接基础标题
  store.dispatch('settings/setTitle', to.meta.title || '')
  if (useMyCookies.get('token')) {
    /* has token */
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else if (store.getters.roles.length === 0) {
      // 判断当前用户是否已拉取完 user_info 信息
      store
        .dispatch('GetInfo')
        .then(() => {
          // 用户信息获取成功后，加载枚举数据
          ensureEnumsLoaded().catch(() => {})
          store.dispatch('GenerateRoutes').then((accessRoutes) => {
            // 根据 roles 权限生成可访问的路由表
            for (const item of accessRoutes) {
              router.addRoute(item)
            }
            next({ ...to, replace: true }) // hack 方法 确保 addRoutes 已完成
          })
        })
        .catch((error) => {
          console.log('🚀 ‣ error:', error)
          // Message.error(error)
          // store.dispatch('LogOut')
        })
    } else {
      next()
    }
  } else {
    // 没有 token
    if (whiteList.has(to.path)) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next('/login')
      // next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

export default router
