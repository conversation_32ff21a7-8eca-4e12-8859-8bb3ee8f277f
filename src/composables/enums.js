// - ensureEnumsLoaded(): 可选，一次性拉取 /enums
// - enumOptions(name): 若不存在则异步拉取 /enums/:name，先返回 []
// - enumMap(name): 基于现有选项构造 value->label
// - enumLabel(name, value): 回显 label（!value 判空）
// - enumFormatter(name): 返回 (value)=>label

const state = reactive({ data: {} })

function normalize(list) {
  return Array.isArray(list) ? list.map((index) => ({ label: index.label, value: index.value })) : []
}

// 内置轻量枚举（无需请求）：布尔类，名称统一为 PascalCase + (Enum|Boolean)
const builtinEnums = {}

async function loadAll() {
  const all = await useApiGetEnums()
  const data = {}
  if (all && typeof all === 'object') for (const k of Object.keys(all)) data[k] = normalize(all[k])
  state.data = data
}

async function loadOne(name) {
  const list = await useApiGetEnum(name)
  state.data = { ...state.data, [name]: normalize(list) }
}

export function ensureEnumsLoaded() {
  return loadAll().catch(() => {})
}

export function enumOptions(name) {
  if (!state.data?.[name]) {
    if (builtinEnums[name]) {
      state.data = { ...state.data, [name]: builtinEnums[name] }
    } else {
      loadOne(name).catch(() => {})
    }
  }
  return state.data?.[name] || []
}

export function enumMap(name) {
  const m = {}
  for (const item of enumOptions(name)) m[item.value] = item.label
  return m
}

export function enumLabel(name, value, fallback = value) {
  const m = enumMap(name)
  const key = value ? String(value) : ''
  return m[key] ?? m[value] ?? fallback
}

export function enumFormatter(name, { fallbackToValue = true } = {}) {
  return (value) => enumLabel(name, value, fallbackToValue ? value : '')
}
