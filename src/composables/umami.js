export function useUmami(tag) {
  // 仅在打包模式加载 Umami 脚本
  if (useEnvironment.NODE_ENV !== 'production') return

  // 仅在正式部署环境加载 Umami 脚本
  if (useEnvironment.VITE_ENV !== 'production') return

  // 如果未设置 VITE_UMAMI_WEBSITE_ID，则不加载 Umami 脚本
  const websiteId = useEnvironment.VITE_UMAMI_WEBSITE_ID
  if (!websiteId) return

  const EXISTING_SCRIPT_ID = 'umami-analytics-script'
  if (document.querySelector(`#${EXISTING_SCRIPT_ID}`)) return

  const script = document.createElement('script')
  script.id = EXISTING_SCRIPT_ID
  script.defer = true
  script.src = 'https://support.wfgxic.com/script.js'
  script.dataset.websiteId = websiteId
  script.dataset.autoTrack = 'true' // 自动跟踪
  if (tag) {
    script.dataset.tag = tag
  }
  document.head.append(script)
}
