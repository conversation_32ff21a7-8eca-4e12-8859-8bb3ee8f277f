/**
 * 格式化工具函数
 * 替代 Vue2 全局过滤器，兼容 Vue3
 */

/**
 * 格式化日期时间
 * @param {string|Date} value - 要格式化的日期值
 * @param {string} formatStr - 格式化字符串，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (value, formatString = 'YYYY-MM-DD HH:mm:ss') => {
  try {
    return useDateFormat(value, formatString).value
  } catch {
    return value
  }
}

// 为了保持兼容性，导出 format 别名
export const format = formatDate

// 其他格式化函数可以在这里添加
export default {
  formatDate,
  format,
}
