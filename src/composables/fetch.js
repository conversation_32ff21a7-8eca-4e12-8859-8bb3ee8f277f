import axios from 'axios'
import { Message, MessageBox, Notification } from 'element-ui'
import store from '@/store'
import router from '@/router'

/**
 * 创建 axios 实例
 */
export const useMyFetch = axios.create({
  baseURL: useEnvironment.VITE_BASE_API,
  timeout: 30_000,
})

/**
 * 请求拦截器
 */
useMyFetch.interceptors.request.use(
  (config) => {
    // 设置 token
    const token = useMyCookies.get('token')
    if (token) config.headers.Authorization = `Bearer ${token}`

    return config
  },
  (error) => {
    Message({ message: '网络连接失败', type: 'error' })
    return Promise.reject(error)
  },
)

/**
 * 响应拦截器
 */
useMyFetch.interceptors.response.use(
  async (response) => {
    const { responseType } = response.config

    // 处理 blob 响应
    if (responseType === 'blob') {
      if (response.config.url.includes('files/image')) {
        return URL.createObjectURL(response.data)
      }
      try {
        JSON.parse(await response.data.text())
        return URL.createObjectURL(response.data)
      } catch {
        return new Blob([response.data], { type: response.data.type || 'application/octet-stream' })
      }
    }

    // 处理 arraybuffer 响应
    if (responseType === 'arraybuffer') {
      const image = btoa(new Uint8Array(response.data).reduce((data, byte) => data + String.fromCodePoint(byte), ''))
      return `data:${response.headers['content-type'].toLowerCase()};base64,${image}`
    }

    return response.data
  },
  async (error) => {
    console.log('🚀 ‣ error:', error)
    if (!error.response) {
      Message({ message: '网络请求超时', type: 'error' })
      throw error
    }

    const { status, statusText, data: responseData } = error.response

    // 处理 blob 错误响应
    let data = responseData
    if (responseData instanceof Blob && responseData.type.includes('json')) {
      try {
        data = JSON.parse(await responseData.text())
      } catch {
        data = { detail: '未知异常' }
      }
    }

    const message = data?.errors?.length > 0 ? `[${data.errors[0].field}]: ${data.errors[0].message}` : data.detail || data.title || statusText

    // 处理不同状态码
    switch (status) {
      case 401: {
        MessageBox.confirm('登录已过期，请重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          type: 'warning',
        }).then(() =>
          store.dispatch('LogOut').then(() => {
            router.replace({ name: 'Login' })
          }),
        )
        break
      }
      case 500: {
        const errorMessage = typeof data === 'string' ? data : data.title || data.detail || ''
        const title = errorMessage.includes('Proxy error') ? '后端服务未启动' : '服务端异常，请联系开发人员修复'

        Notification({
          title,
          message,
          duration: 0,
          showClose: true,
          type: 'error',
        })
        break
      }
      default: {
        Message({ message, type: 'warning' })
      }
    }

    throw data
  },
)
