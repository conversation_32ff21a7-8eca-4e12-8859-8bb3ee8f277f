import Cookies from 'js-cookie'

// 获取环境变量
const Slug = useEnvironment.VITE_SLUG.toUpperCase()
const Environment = useEnvironment.VITE_ENV
const Path = useEnvironment.VITE_BASE_URL

// 生成前缀键，用于区分不同环境和应用
const prefixKey = `${Slug}__${Environment}__`

// 封装 js-cookie，统一添加应用路径，使用 path 属性存储，避免多项目冲突
export const useMyCookies = {
  get: (name) => (name ? Cookies.get(name) : Cookies.get()),
  set: (name, value, options) =>
    Cookies.set(name, value, {
      expires: 1, // 默认过期时间为 1 天，需要和后端统一
      path: Path,
      ...options,
    }),
  remove: (name, options) =>
    Cookies.remove(name, {
      path: Path,
      ...options,
    }),
}

/**
 * 用于操作 localStorage 的工具对象
 */
export const useMyLocalStorage = {
  /**
   * 获取指定键的 localStorage 值
   *
   * @param {string} key - 要获取的 localStorage 键
   * @returns {*} - 对应的 localStorage 值
   */
  get(key) {
    const value = localStorage.getItem(prefixKey + key)
    try {
      return JSON.parse(value)
    } catch (error) {
      console.error('[useMyLocalStorage.get]', error)
      return value
    }
  },

  /**
   * 设置指定键的 localStorage 值
   *
   * @param {string} key - 要设置的 localStorage 键
   * @param {*} value - 要设置的 localStorage 值
   */
  set(key, value) {
    try {
      if (typeof value === 'object') {
        localStorage.setItem(prefixKey + key, JSON.stringify(value))
      } else {
        localStorage.setItem(prefixKey + key, value)
      }
    } catch (error) {
      console.error('[useMyLocalStorage.set]', error)
    }
  },

  /**
   * 移除指定键的 localStorage
   *
   * @param {string} key - 要移除的 localStorage 键
   */
  remove(key) {
    return localStorage.removeItem(prefixKey + key)
  },

  /**
   * 清空 localStorage
   */
  clear() {
    return localStorage.clear()
  },
}

/**
 * 用于操作 sessionStorage 的工具对象
 */
export const useMySessionStorage = {
  /**
   * 获取指定键的 sessionStorage 值
   *
   * @param {string} key - 要获取的 sessionStorage 键
   * @returns {*} - 对应的 sessionStorage 值
   */
  get(key) {
    const value = sessionStorage.getItem(prefixKey + key)
    try {
      return JSON.parse(value)
    } catch (error) {
      console.error('[useMySessionStorage.get]', error)
      return value
    }
  },

  /**
   * 设置指定键的 sessionStorage 值
   *
   * @param {string} key - 要设置的 sessionStorage 键
   * @param {*} value - 要设置的 sessionStorage 值
   */
  set(key, value) {
    try {
      if (typeof value === 'object') {
        sessionStorage.setItem(prefixKey + key, JSON.stringify(value))
      } else {
        sessionStorage.setItem(prefixKey + key, value)
      }
    } catch (error) {
      console.error('[useMySessionStorage.set]', error)
    }
  },

  /**
   * 移除指定键的 sessionStorage
   *
   * @param {string} key - 要移除的 sessionStorage 键
   */
  remove(key) {
    return sessionStorage.removeItem(prefixKey + key)
  },

  /**
   * 清空 sessionStorage
   */
  clear() {
    return sessionStorage.clear()
  },
}
