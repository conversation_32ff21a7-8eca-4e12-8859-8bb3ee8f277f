import { encode } from 'js-base64'

function addFieldsIndex(array) {
  let count = 0
  function traverse(object) {
    if (object.columns) {
      for (const column of object.columns) {
        traverse(column)
      }
    } else {
      object.field = String(count++)
      if (object.field === '0') {
        object.widthGrow = 2
        object.hozAlign = 'left'
      }
    }
  }
  for (const object of array) {
    traverse(object)
  }
  return array
}

function transformArray(array) {
  return array.map((item) => {
    const newItem = {
      title: item.name,
      columns: item.children ? transformArray(item.children) : undefined,
    }
    if (!newItem.columns) {
      delete newItem.columns
    }
    return newItem
  })
}

export const useConvert = {
  string2array(value, separator = ',') {
    if (!value) return []
    if (typeof value === 'string') return value.split(separator)
    if (Array.isArray(value)) return value
    return []
  },
  array2string(value, separator = ',') {
    if (!value) return ''
    return Array.isArray(value) ? value.join(separator) : value
  },
  tree2table(value) {
    const transformedArray = transformArray(value)
    return addFieldsIndex(transformedArray)
  },
}

export const useEscape = {
  string2html(value) {
    if (!value) return ''
    const fixValue = this.fix(value)
    return fixValue.replaceAll('【', '<p contenteditable>').replaceAll('】', '</p>')
  },
  html2string(value) {
    if (!value) return ''
    return value.replaceAll('</p>', '】').replaceAll('<p contenteditable>', '【').replaceAll('<p contenteditable="">', '【')
  },
  fix(value) {
    if (!value?.includes(String.raw`\n`)) return value
    return value.replaceAll(String.raw`\n`, '\n')
  },
}

/**
 * 静态文件的相对路径
 * @param {*} value
 * @returns /api/demo/files 开头
 */
export const useStaticFileUrl = (value) => {
  if (!value) return ''
  return value.startsWith(useEnvironment.VITE_FILE_BASE_URL) || value.startsWith('blob') || value.startsWith('http') ? value : `${useEnvironment.VITE_FILE_BASE_URL}${value}`
}

/**
 * 可预览的路径
 * @param {*} value
 * @returns
 */
export const usePreviewFileUrl = (value) => {
  if (!value) return ''

  const staticFileUrl = useStaticFileUrl(value)
  // 开发模式下使用环境变量中的 DOMAIN_URL，部署模式下使用 location.origin
  const locationOrigin = useEnvironment.PROD ? location.origin : useEnvironment.DOMAIN_URL
  const absoluteStaticFileUrl = `${locationOrigin}${staticFileUrl}`
  console.log('[文件原始路径]', absoluteStaticFileUrl)
  return `${useEnvironment.FILE_PREVIEW_SERVER}/onlinePreview?url=${encodeURIComponent(encode(absoluteStaticFileUrl))}`
}

/**
 * http 跳转 https
 * @param {*} value
 * @returns
 */
export const useHttps = () => {
  if (useEnvironment.PROD) {
    const { host, protocol, href } = globalThis.location

    const isIp = (ip) => {
      const reg = /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/
      return reg.test(ip)
    }
    // ip 访问不跳转 https
    if (isIp(host)) return

    const targetProtocol = 'https:'
    if (protocol !== targetProtocol) {
      globalThis.location.href = href.replace(protocol, targetProtocol)
    }
  }
}

/**
 * 生成随机二维数组
 * @param {*} subcolumns
 * @param {*} rows
 * @param {*} subrows
 * @returns
 */
export const useGenerate2DArray = (subcolumns = 10, rows = 10, subrows = 1) => {
  // eslint-disable-next-line unicorn/consistent-function-scoping
  function generateRandomArray(rows, columns) {
    const array = []
    for (let index = 0; index < rows; index++) {
      array[index] = []
      for (let index_ = 0; index_ < columns; index_++) {
        array[index][index_] = Math.floor(Math.random() * 101)
      }
    }
    return array
  }

  const array2d = []

  for (let index = 0; index < rows; index++) {
    array2d.push(generateRandomArray(subrows, subcolumns)[0])
  }
  return array2d
}

export const useFilterDict = (value, options) => {
  const result = options.find((option) => option.value === value)
  return result?.label || ''
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function useTransParameters(parameters) {
  // 用于收集参数对的数组
  const result = []

  /**
   * 添加参数对到结果数组
   * @param {string} key - 参数名称
   * @param {any} value - 参数值
   */
  function addParameter(key, value) {
    if (value !== undefined && value !== '') {
      // 编码并添加参数对
      result.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    }
  }

  // 遍历参数对象的每个属性
  for (const propertyName in parameters) {
    if (Object.prototype.hasOwnProperty.call(parameters, propertyName)) {
      const value = parameters[propertyName]
      // 检查属性值的类型
      if (typeof value === 'object') {
        // 如果属性值是对象，遍历其内部属性
        for (const key in value) {
          if (Object.prototype.hasOwnProperty.call(value, key)) {
            // 添加嵌套参数对
            addParameter(`${propertyName}[${key}]`, value[key])
          }
        }
      } else {
        // 添加普通参数对
        addParameter(propertyName, value)
      }
    }
  }

  // 将参数对数组组合成字符串并返回
  return result.join('&')
}
