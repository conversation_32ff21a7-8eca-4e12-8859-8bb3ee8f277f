import Vue from 'vue'
import * as Sentry from '@sentry/vue'

export function useSentry(router) {
  // 仅在打包模式加载 Sentry 脚本
  if (useEnvironment.NODE_ENV !== 'production') return

  // 仅在正式部署环境加载 Sentry 脚本
  if (useEnvironment.VITE_ENV !== 'production') return

  // 仅在配置了 DSN 时加载 Sentry 脚本
  const dsn = useEnvironment.VITE_SENTRY_DSN
  if (!dsn) return

  Sentry.init({
    Vue,
    dsn: useEnvironment.VITE_SENTRY_DSN,
    integrations: [Sentry.browserTracingIntegration({ router }), Sentry.replayIntegration({ maskAllText: false, blockAllMedia: false })],
    // Tracing
    tracesSampleRate: 1, //  Capture 100% of the transactions
    // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
    // tracePropagationTargets: [/^https:\/\/*\.wfgxic\.com/],
    // Session Replay
    replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
    replaysOnErrorSampleRate: 1, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
  })
}
