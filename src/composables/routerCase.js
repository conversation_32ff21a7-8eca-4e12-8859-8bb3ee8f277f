import * as changeCase from 'change-case'

// 路由名称标准化转换
export const useRouterCase = {
  // name 为 大驼峰
  name: (name) => {
    if (!name) return ''
    return changeCase.pascalCase(name)
  },
  // path 为 小写短横线
  path: (name) => {
    if (!name) return ''
    return name.startsWith('http')
      ? name
      : name
          .split('/')
          .map((x) => changeCase.kebabCase(x))
          .join('/')
  },
  // component 为 小写短横线
  component: (name) => {
    if (!name) return ''
    return ['Layout', 'ParentView', 'InnerLink'].includes(name)
      ? name
      : name
          .split('/')
          .map((x) => changeCase.kebabCase(x))
          .join('/')
  },
  log: (type = '', oldName, newName) => {
    if (oldName !== newName) {
      console.log('🚀 路由转换', type, oldName, '➡️', newName, '赶紧去菜单管理更新')
    }
  },
}
