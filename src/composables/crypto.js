import JSEncrypt from 'jsencrypt/bin/jsencrypt.min.js'

// 公钥
const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH\n' + 'nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='

// 私钥
const privateKey =
  'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY\n' +
  '7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN\n' +
  'PuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA\n' +
  'kM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow\n' +
  'cSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv\n' +
  'DQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh\n' +
  'YhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3\n' +
  'UP8iWi1Qw0Y='

/**
 * 创建加密实例
 *
 * @param {string} publicKey - 公钥
 * @returns {JSEncrypt} - 配置了公钥的 JSEncrypt 实例
 */
const createEncrypt = (publicKey) => {
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(publicKey)
  return encrypt
}

/**
 * 创建解密实例
 *
 * @param {string} privateKey - 私钥
 * @returns {JSEncrypt} - 配置了私钥的 JSEncrypt 实例
 */
const createDecrypt = (privateKey) => {
  const decrypt = new JSEncrypt()
  decrypt.setPrivateKey(privateKey)
  return decrypt
}

// 创建加密和解密实例
const encrypt = createEncrypt(publicKey)
const decrypt = createDecrypt(privateKey)

export const useCrypto = {
  /**
   * 加密文本
   *
   * @param {string} txt - 要加密的文本
   * @returns {string} - 加密后的文本
   */
  encrypt: (txt) => encrypt.encrypt(txt),

  /**
   * 解密文本
   *
   * @param {string} txt - 要解密的文本
   * @returns {string} - 解密后的文本
   */
  decrypt: (txt) => decrypt.decrypt(txt),
}
