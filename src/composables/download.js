import FileSaver from 'file-saver'

export const useDownloader = {
  /**
   * 自定义下载
   * @param {Object} options - 下载选项
   * @param {string} options.url - 下载的 URL
   * @param {Object} options.data - 请求的参数数据
   * @param {string} options.filename - 下载文件名
   * @param {string} options.filetype - 下载文件类型
   */
  async common({ url, data, filename, filetype }) {
    const blob = await useMyFetch({
      url,
      method: 'get',
      params: {
        id: data.id,
        fileName: data.fileName || data.filename || filename,
        delete: true, // 删除缓存
      },
      responseType: 'blob',
    })
    this._saveFile(blob, filename, filetype)
  },

  /**
   * 通过文件名下载
   * @param {Object} options - 下载选项
   * @param {string} options.name - 文件名
   * @param {string} options.filename - 下载文件名
   * @param {string} options.filetype - 下载文件类型
   */
  async byName({ name, filename, filetype }) {
    const blob = await useMyFetch({
      url: '/common/download',
      method: 'get',
      params: {
        fileName: name,
        delete: true, // 删除缓存
      },
      responseType: 'blob',
    })
    this._saveFile(blob, filename, filetype)
  },

  /**
   * 通过 ID 下载
   * @param {Object} options - 下载选项
   * @param {string} options.id - 文件 ID
   * @param {string} options.filename - 下载文件名
   * @param {string} options.filetype - 下载文件类型
   */
  async byId({ id, filename, filetype }) {
    const blob = await useMyFetch({
      url: `/common/download/${id}`,
      method: 'get',
      params: {
        delete: true, // 删除缓存
      },
      responseType: 'blob',
    })
    this._saveFile(blob, filename, filetype)
  },

  /**
   * 通过资源名下载
   * @param {Object} options - 下载选项
   * @param {string} options.resource - 资源名
   * @param {string} options.filename - 下载文件名
   * @param {string} options.filetype - 下载文件类型
   */
  async byResource({ resource, filename, filetype }) {
    const blob = await useMyFetch({
      url: '/common/download/resource',
      method: 'get',
      params: {
        resource,
        delete: true, // 删除缓存
      },
      responseType: 'blob',
    })
    this._saveFile(blob, filename, filetype)
  },

  /**
   * 下载压缩包
   * @param {Object} options - 下载选项
   * @param {string} options.url - 下载的 URL
   * @param {Object} options.data - 请求的参数数据
   * @param {string} options.filename - 下载文件名
   * @param {string} options.filetype - 下载文件类型，默认为'zip'
   */
  async zip({ url, data, filename, filetype = 'zip' }) {
    const blob = await useMyFetch({
      url,
      method: 'get',
      params: {
        tables: data.tables,
      },
      responseType: 'blob',
    })
    this._saveFile(blob, filename, filetype)
  },

  /**
   * 导出表格
   * @param {Object} options - 下载选项
   * @param {string} options.url - 下载的 URL
   * @param {Object} options.data - 请求的参数数据
   * @param {string} options.filename - 下载文件名
   * @param {string} options.filetype - 下载文件类型，默认为'xlsx'
   */
  async xlsx({ url, data, filename, filetype = 'xlsx' }) {
    const blob = await useMyFetch({
      url,
      method: 'post',
      data,
      responseType: 'blob',
    })
    this._saveFile(blob, filename, filetype)
  },

  /**
   * 保存文件
   * @param {Blob} blob - 文件数据
   * @param {string} filename - 文件名
   * @param {string} filetype - 文件类型
   */
  _saveFile(blob, filename, filetype) {
    const formatted = useDateFormat(useNow(), 'YYMMDD_HHmmss').value
    const fileName = filetype ? `${filename}_${formatted}.${filetype}` : `${filename}_${formatted}`
    console.warn('[下载文件]', fileName)
    FileSaver.saveAs(blob, fileName)
  },
}
