import Watermark from 'watermark-plus'

/**
 * 添加水印到页面
 *
 * @param {Object} options - 水印选项
 * @param {string} options.username - 用户名
 * @param {string} options.nickname - 昵称
 * @param {string} options.mobile - 电话号码
 */
export const useWatermark = ({ username = '', nickname = '', mobile = '' }) => {
  // 仅在生产环境中添加水印
  if (useEnvironment.PROD) {
    // 获取当前日期时间的格式化字符串
    const now = useMyDateFormat(new Date(), 'YY/MM/DD HH:mm')

    // 创建水印实例，内容包括用户名、昵称和电话号码
    const watermark = new Watermark({
      content: `${username} ${nickname} ${mobile}`,
      tip: `${useEnvironment.VITE_TITLE} ${now}`,
    })

    // 生成水印
    watermark.create()
  }
}
