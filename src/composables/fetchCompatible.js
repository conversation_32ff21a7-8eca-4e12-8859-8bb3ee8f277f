import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '@/store'

/**
 * 创建一个带有默认配置的 axios 实例
 */
export const useMyFetchCompatible = axios.create({
  baseURL: useEnvironment.VITE_BASE_API, // API 基础 URL
  timeout: 90_000, // 请求超时时间，单位毫秒
})

/**
 * 请求拦截器，用于在请求发送之前对请求进行处理
 */
useMyFetchCompatible.interceptors.request.use(
  (config) => {
    // 设置 Authorization 头部
    const token = useMyCookies.get('token')
    if (token) config.headers.Authorization = `Bearer ${token}`

    return config
  },
  (error) => {
    // 处理请求错误
    Message({ message: '网络连接失败', type: 'error' })
    return Promise.reject(error)
  },
)

/**
 * 响应拦截器，用于在响应到达之前对响应进行处理
 */
useMyFetchCompatible.interceptors.response.use(
  async (response) => {
    if (!response.config) {
      console.error('存在网络故障')
      return
    }

    const STATUS = response?.status
    const METHOD = response?.config?.method?.toUpperCase()
    const API_URL = response?.config?.url
    // console.groupCollapsed(`%c[${STATUS}] ${METHOD} ${API_URL}`, 'color: green;')
    // console.dir(response)

    const { responseType } = response.config

    if (responseType === 'blob') {
      if (API_URL.includes('files/image')) {
        return URL.createObjectURL(response.data)
      }

      try {
        JSON.parse(await response.data.text())
        return URL.createObjectURL(response.data)
      } catch {
        const blob = new Blob([response.data], { type: response.data.type || 'application/octet-stream' })
        return blob
        // return FileSaver.saveAs(blob, decodeURI(response.headers['download-filename']))
      }
    }

    if (responseType === 'arraybuffer') {
      const image = btoa(new Uint8Array(response.data).reduce((data, byte) => data + String.fromCodePoint(byte), ''))
      const mimetype = response.headers['content-type'].toLowerCase()
      return `data:${mimetype};base64,${image}`
    }

    // TODO: 兼容返回格式
    const responseData = Object.keys(response.data)
    if (!response.data?.code && !response.data?.msg) {
      return response.data
    }

    if (response.data?.code === 0 || response.data?.code === 200) {
      // console.log(`%c返回 response.data.data`, 'color: orange;', response.data.data)
      // console.groupEnd()
      return response.data?.data || response.data
    }

    // console.groupEnd()

    const message = response.data?.msg || '异常错误'
    const data = response.data

    // 根据状态码处理响应
    switch (response.data.code) {
      case 401: {
        MessageBox.confirm('登录状态已过期，请重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          type: 'warning',
        }).then(() => store.dispatch('LogOut'))
        throw data
      }

      case 500: {
        Message({ message: useEnvironment.PROD ? '系统异常，请联系开发人员' : message, type: 'error' })
        throw data
      }

      default: {
        Message({ message, type: 'error' })
        throw data
      }
    }
  },
  (error) => {
    if (!error.response) {
      Message({ message: '网络请求超时', type: 'error' })
      return Promise.reject(error)
    }

    const STATUS = error.response.status
    const METHOD = error.response.config.method.toUpperCase()
    const API_URL = error.response.config.baseURL + error.response.config.url
    console.group(`%c[${STATUS}] ${METHOD} ${API_URL}`, 'color: red;')
    console.table(error.response.data)
    console.groupEnd()

    const { status, statusText, data } = error.response // HTTP 响应字段

    let message = data.detail || data.title || statusText // 提示顺序

    // 根据 HTTP 状态码处理响应错误
    switch (status) {
      case 400: {
        if (data?.fieldErrors && data?.fieldErrors.length) {
          message = `[${data.fieldErrors[0].field}] ${data.fieldErrors[0].message}`
        }

        Message({ message, type: 'error' })
        return Promise.reject(data)
      }

      case 401: {
        MessageBox.confirm('登录状态已过期，请重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          type: 'warning',
        }).then(() => store.dispatch('LogOut'))
        return Promise.reject(data)
      }

      case 500: {
        Message({ message: useEnvironment.PROD ? '系统异常，请联系开发人员' : message, type: 'error' })
        return Promise.reject(data)
      }

      default: {
        Message({ message, type: 'error' })
        return Promise.reject(data)
      }
    }
  },
)
