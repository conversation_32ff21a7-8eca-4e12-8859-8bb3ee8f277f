<template>
  <div v-loading="loading" element-loading-text="正在加载页面，请稍候！" :style="`height:${height}`">
    <iframe :id="iframeId" class="h-full w-full" frameborder="no" :src="src"></iframe>
  </div>
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      default: '/',
    },
    iframeId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      height: `${document.documentElement.clientHeight - 94.5}px;`,
    }
  },
  mounted() {
    const iframeId = `#${this.iframeId}`.replaceAll('/', String.raw`\/`)
    const iframe = document.querySelector(iframeId)
    // iframe 页面 loading 控制
    if (iframe.attachEvent) {
      this.loading = true
      iframe.attachEvent('onload', () => {
        this.loading = false
      })
    } else {
      this.loading = true
      iframe.addEventListener('load', () => {
        this.loading = false
      })
    }
  },
}
</script>
