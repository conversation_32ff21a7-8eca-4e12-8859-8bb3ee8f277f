<template>
  <!-- 左树右表布局 -->
  <div class="h-[calc(100vh-84px)] min-h-450px overflow-auto p-10px relative">
    <!-- Flex 布局：小屏纵向，md 起左右分栏 -->
    <div class="flex flex-col h-full md:flex-row">
      <!-- 左侧树默认宽度 240px -->
      <div v-if="showLeftAuto" class="border border-gray-200 flex flex-col h-auto mb-4 md:h-full md:mb-0 md:min-w-240px md:w-240px rounded w-full">
        <div class="p-2">
          <!-- 机构搜索框 -->
          <slot name="treeSearch"></slot>
        </div>
        <div class="flex-1 overflow-y-auto">
          <slot name="tree"></slot>
        </div>
        <!-- 操作树 -->
      </div>

      <!-- 右侧主体（v-resize 绑定自定义监听事件） -->
      <div v-if="!customRight" class="border border-gray-200 flex flex-col h-full overflow-hidden relative rounded w-full" :class="{ 'md:ml-2': showLeftAuto }">
        <!-- 顶部操作区：有搜索/操作按钮/提示任一存在即显示 -->
        <div v-if="$slots.tableSearch || showActionAuto || showAlert" ref="action" class="px-3 py-2">
          <slot name="tableSearch"></slot>

          <div v-if="showActionAuto" class="flex flex-wrap gap-2 items-center justify-between">
            <slot name="actionLeftBtn"></slot>
            <div class="min-w-68px"><slot name="actionRightBtn"></slot></div>
          </div>

          <slot v-if="showAlert" name="alert"></slot>
        </div>

        <!-- 表格区 -->
        <div v-resize="tableResize" class="border-gray-200 border-t flex-1 overflow-hidden">
          <slot :height="height" name="rightTable"></slot>
        </div>

        <!-- 底部区域（分页/更新时间） -->
        <div v-if="showPagination || showDate" class="flex items-center" :class="showPagination && showDate ? 'justify-between' : 'justify-end'">
          <slot v-if="showDate" name="updateDate"></slot>
          <slot v-if="showPagination" name="pagination"></slot>
        </div>
      </div>

      <!-- 自定义右侧内容（不包裹工具条和分页） -->
      <div v-if="customRight" class="h-full overflow-y-auto w-full" :class="{ 'md:ml-2': showLeftAuto }">
        <slot :height="height" name="rightTable"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 右侧操作按钮
    showAction: {
      type: Boolean,
      default: true,
    },
    // 左侧
    showLeft: {
      type: Boolean,
      default: true,
    },
    // 其他说明
    showAlert: {
      type: Boolean,
      default: false,
    },
    // 分页
    showPagination: {
      type: Boolean,
      default: true,
    },
    // 全屏显示 table
    showZoom: {
      type: Boolean,
      default: true,
    },
    showDate: {
      type: Boolean,
      default: false,
    },
    customRight: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 右侧表格可用高度（像素）
      height: 0,
    }
  },
  computed: {
    // 自动判断是否展示左侧树：当显式传入 showLeft=false 或无 tree/treeSearch 插槽时隐藏
    showLeftAuto() {
      const hasTree = Boolean(this.$slots.tree || this.$slots.treeSearch)
      return this.showLeft && hasTree
    },
    // 自动判断是否显示操作按钮区域：需要显式允许 + 存在任一操作插槽
    showActionAuto() {
      const hasAction = Boolean(this.$slots.actionLeftBtn || this.$slots.actionRightBtn)
      return this.showAction && hasAction
    },
  },
  methods: {
    // 直接监听表格容器（中间区域）尺寸，以获取精准高度
    tableResize({ height }) {
      const next = typeof height === 'number' ? height : 0
      if (next !== this.height) this.height = next
      this.$emit('height', this.height)
    },
  },
}
</script>
