<template>
  <div class="sidebar-logo-container" :class="[{ collapse }, sideTheme]">
    <Transition name="sidebar-logo-fade">
      <RouterLink v-if="collapse" key="collapse" class="sidebar-logo-link" :to="{ name: 'Index' }">
        <img v-if="logo" class="sidebar-logo" :src="logo" />
        <h1 v-else class="sidebar-title">{{ title }}</h1>
      </RouterLink>
      <RouterLink v-else key="expand" class="sidebar-logo-link" :to="{ name: 'Index' }">
        <img v-if="logo" class="sidebar-logo" :src="logo" />
        <h1 class="sidebar-title">{{ title }}</h1>
      </RouterLink>
    </Transition>
  </div>
</template>

<script>
import logoImg from '@/assets/logo/logo.png'

export default {
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      title: useEnvironment.VITE_TITLE,
      logo: logoImg,
    }
  },
  computed: {
    sideTheme() {
      return this.$store.state.settings.sideTheme
    },
  },
}
</script>

<style lang="scss" scoped>
.sidebar-logo-fade-enter-active {
  transition: opacity 1.5s;
}

.sidebar-logo-fade-enter,
.sidebar-logo-fade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: var(--menu-dark-background);
  text-align: center;
  overflow: hidden;

  &.theme-light {
    background-color: var(--menu-light-background);
  }

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: var(--menu-dark-logo-title-color);
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family:
        Avenir,
        Helvetica Neue,
        Arial,
        Helvetica,
        sans-serif;
      vertical-align: middle;
    }
  }

  &.theme-light .sidebar-title {
    color: var(--menu-light-logo-title-color);
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
