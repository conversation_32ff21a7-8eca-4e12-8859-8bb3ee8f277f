<template>
  <ElDrawer :append-to-body="true" :show-close="false" size="280px" :visible="visible" :with-header="false">
    <div class="break-words leading-6 p-5 text-sm">
      <div>
        <div>
          <div>
            <h3 class="leading-6 mb-3 text-gray-800 text-sm">主题风格设置</h3>
          </div>
          <div class="flex items-center mb-5 mt-2.5">
            <div class="cursor-pointer mr-4 relative rounded" @click="handleTheme('theme-dark')">
              <img alt="dark" class="h-12 w-12" src="@/assets/images/dark.svg" />
              <div v-if="sideTheme === 'theme-dark'" class="absolute font-bold inset-0 pl-6 pt-4 text-blue-500 text-sm">
                <i aria-label="图标: check" class="anticon anticon-check">
                  <svg aria-hidden="true" class="" data-icon="check" :fill="theme" focusable="false" height="1em" viewBox="64 64 896 896" width="1em">
                    <path
                      d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                    />
                  </svg>
                </i>
              </div>
            </div>
            <div class="cursor-pointer mr-4 relative rounded" @click="handleTheme('theme-light')">
              <img alt="light" class="h-12 w-12" src="@/assets/images/light.svg" />
              <div v-if="sideTheme === 'theme-light'" class="absolute font-bold inset-0 pl-6 pt-4 text-blue-500 text-sm">
                <i aria-label="图标: check" class="anticon anticon-check">
                  <svg aria-hidden="true" class="" data-icon="check" :fill="theme" focusable="false" height="1em" viewBox="64 64 896 896" width="1em">
                    <path
                      d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                    />
                  </svg>
                </i>
              </div>
            </div>
          </div>

          <div class="py-3 text-gray-600 text-sm">
            <span>主题颜色</span>
            <ThemePicker class="-mt-1 float-right h-6 mr-2" @change="themeChange" />
          </div>
        </div>

        <ElDivider />

        <h3 class="leading-6 mb-3 text-gray-800 text-sm">系统布局配置</h3>

        <div class="py-3 text-gray-600 text-sm">
          <span>开启 TopNav</span>
          <ElSwitch v-model="topNav" class="float-right" />
        </div>

        <div class="py-3 text-gray-600 text-sm">
          <span>开启 Tags-Views</span>
          <ElSwitch v-model="tagsView" class="float-right" />
        </div>

        <div class="py-3 text-gray-600 text-sm">
          <span>固定 Header</span>
          <ElSwitch v-model="fixedHeader" class="float-right" />
        </div>

        <div class="py-3 text-gray-600 text-sm">
          <span>显示 Logo</span>
          <ElSwitch v-model="sidebarLogo" class="float-right" />
        </div>

        <div class="py-3 text-gray-600 text-sm">
          <span>动态标题</span>
          <ElSwitch v-model="dynamicTitle" class="float-right" />
        </div>

        <ElDivider />

        <ElButton icon="el-icon-document-add" plain size="small" type="primary" @click="saveSetting">保存配置</ElButton>
        <ElButton icon="el-icon-refresh" plain size="small" @click="resetSetting">重置配置</ElButton>
      </div>
    </div>
  </ElDrawer>
</template>

<script>
export default {
  data() {
    return {
      theme: this.$store.state.settings.theme,
      sideTheme: this.$store.state.settings.sideTheme,
    }
  },
  computed: {
    visible: {
      get() {
        return this.$store.state.settings.showSettings
      },
    },
    fixedHeader: {
      get() {
        return this.$store.state.settings.fixedHeader
      },
      set(value) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'fixedHeader',
          value,
        })
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      },
      set(value) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'topNav',
          value,
        })
        if (!value) {
          this.$store.dispatch('app/toggleSideBarHide', false)
          this.$store.commit('SET_SIDEBAR_ROUTES', this.$store.state.permission.defaultRoutes)
        }
      },
    },
    tagsView: {
      get() {
        return this.$store.state.settings.tagsView
      },
      set(value) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'tagsView',
          value,
        })
      },
    },
    sidebarLogo: {
      get() {
        return this.$store.state.settings.sidebarLogo
      },
      set(value) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'sidebarLogo',
          value,
        })
      },
    },
    dynamicTitle: {
      get() {
        return this.$store.state.settings.dynamicTitle
      },
      set(value) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'dynamicTitle',
          value,
        })
      },
    },
  },
  methods: {
    themeChange(value) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'theme',
        value,
      })
      this.theme = value
    },
    handleTheme(value) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'sideTheme',
        value,
      })
      this.sideTheme = value
    },
    saveSetting() {
      appModal.loading('正在保存到本地，请稍候...')
      const settings = {
        topNav: this.topNav,
        tagsView: this.tagsView,
        fixedHeader: this.fixedHeader,
        sidebarLogo: this.sidebarLogo,
        dynamicTitle: this.dynamicTitle,
        sideTheme: this.sideTheme,
        theme: this.theme,
      }
      useMyLocalStorage.set('layout-setting', settings)
      setTimeout(() => appModal.closeLoading(), 1000)
    },
    resetSetting() {
      appModal.loading('正在清除设置缓存并刷新，请稍候...')
      useMyLocalStorage.remove('layout-setting')
      setTimeout(() => {
        globalThis.location.reload()
      }, 1000)
    },
  },
}
</script>
