<script lang="jsx">
export default {
  functional: true,
  props: {
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },
  render(h, context) {
    const { icon, title } = context.props
    const vNodes = []

    if (icon) {
      vNodes.push(<Icon icon={icon} class="svg-icon mr-2" />)
    }

    if (title) {
      vNodes.push(
        <span slot="title" class={['font-500']}>
          {title}
        </span>,
      )
    }
    return vNodes
  },
}
</script>
