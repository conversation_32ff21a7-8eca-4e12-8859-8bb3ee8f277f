<template>
  <div v-if="!item.hidden">
    <!-- 单子级（仅一个可见子且该子无 children，或无可见子时退化为父）且未设置 alwaysShow -->
    <template v-if="isSingle">
      <AppLink v-if="singleChild && singleChild.meta" :to="itemTo(singleChild)">
        <ElMenuItem :class="{ 'submenu-title-no-dropdown': !isNest }" :index="itemIndex(singleChild)">
          <Item :icon="singleChild.meta.icon || (item.meta && item.meta.icon)" :level="level" :title="singleChild.meta.title" />
        </ElMenuItem>
      </AppLink>
    </template>

    <!-- 多子级，渲染子菜单 -->
    <ElSubmenu v-else ref="subMenu" :index="submenuIndex(item)" popper-append-to-body>
      <template #title>
        <Item v-if="item.meta" :icon="item.meta && item.meta.icon" :level="level" :title="item.meta.title" />
      </template>
      <SidebarItem
        v-for="(child, index) in visibleChildren"
        :key="itemIndex(child)"
        :base-path="itemIndex(child)"
        class="nest-menu"
        :is-nest="true"
        :item="child"
        :level="level + 1"
      />
    </ElSubmenu>
  </div>
</template>

<script>
// 移除 path 导入，避免在浏览器环境中导致 process.cwd 错误
import fixBug from '@/mixins/fixBug'

export default {
  name: 'SidebarItem', // 可以自己调用自己
  mixins: [fixBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true,
    },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: '',
    },
    level: {
      type: Number,
      default: 1,
    },
  },
  computed: {
    // 可见子节点（过滤 hidden）
    visibleChildren() {
      const children = this.item && Array.isArray(this.item.children) ? this.item.children : []
      return children.filter((c) => !c.hidden)
    },
    // 是否渲染为单子级（只有一个可见子且该子无 children，或没有任何可见子）并且未设置 alwaysShow
    isSingle() {
      if (this.item && this.item.alwaysShow) return false
      const length = this.visibleChildren.length
      if (length === 0) return true
      if (length === 1) {
        const only = this.visibleChildren[0]
        return !only.children || only.children.length === 0
      }
      return false
    },
    // 单子级对应的节点（可见子或回退为父）
    singleChild() {
      const length = this.visibleChildren.length
      if (length === 0) return { ...this.item, path: '', noShowingChildren: true }
      return this.visibleChildren[0]
    },
  },
  methods: {
    // 拼接路径，避免出现多余斜杠，同时兼容外链
    joinPath(base, sub) {
      // base 为空时，确保返回绝对路径，避免 'index' 这类相对路径导致在不同模块下变成 '/xxx/index'
      if (!base) {
        if (!sub) return ''
        if (typeof sub === 'string' && isExternal(sub)) return sub
        const cleanedSub = String(sub).replace(/^\//, '')
        return `/${cleanedSub}`
      }
      if (!sub) return base
      if (typeof sub === 'string' && isExternal(sub)) return sub
      if (typeof base === 'string' && isExternal(base)) return base
      const cleanedBase = String(base).replace(/\/$/, '')
      const cleanedSub = String(sub).replace(/^\//, '')
      return `${cleanedBase}/${cleanedSub}`
    },
    // 当前项的 index，用于 ElMenuItem/index 和递归时的 base-path
    itemIndex(routeLike) {
      const path = routeLike && routeLike.path ? routeLike.path : ''
      // 外链或绝对地址直接返回
      if (typeof path === 'string' && isExternal(path)) return path
      if (typeof this.basePath === 'string' && isExternal(this.basePath)) return this.basePath
      return this.joinPath(this.basePath, path)
    },
    // 子菜单本身的 index：优先使用累计的 basePath，避免 base 与自身 path 叠加
    submenuIndex(item) {
      if (this.basePath) return this.basePath
      const path = item && item.path ? item.path : ''
      return path || '/'
    },
    // 生成 router-link/AppLink 的 to 属性（兼容 query 为字符串或对象）
    itemTo(routeLike) {
      const fullPath = this.itemIndex(routeLike)
      const routeQuery = routeLike && routeLike.query
      if (routeQuery) {
        try {
          const query = typeof routeQuery === 'string' ? JSON.parse(routeQuery) : routeQuery
          return { path: fullPath, query }
        } catch {
          // 解析失败时退化为直接 path
        }
      }
      return fullPath
    },
  },
}
</script>
