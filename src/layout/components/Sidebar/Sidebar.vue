<template>
  <div class="sidebar-container" :class="[{ 'has-logo': showLogo }, settings.sideTheme]">
    <AppLogo v-if="showLogo" :collapse="isCollapse" />
    <ElScrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
      <ElMenu
        :active-text-color="settings.theme"
        :collapse="isCollapse"
        :collapse-transition="false"
        :default-active="activeMenu"
        :default-openeds="defaultOpeneds"
        mode="vertical"
        :unique-opened="true"
      >
        <SidebarItem v-for="(route, index) in sidebarRoutes" :key="route.path + index" :base-path="route.path" :item="route" />
      </ElMenu>
    </ElScrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'

export default {
  computed: {
    ...mapState(['settings']),
    ...mapGetters(['sidebarRoutes', 'sidebar']),
    // 打开当前路由链上的所有子菜单
    defaultOpeneds() {
      return this.$route.matched.map((r) => r.path).filter(Boolean)
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    isCollapse() {
      return !this.sidebar.opened
    },
  },
}
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: 100%;
  background-color: var(--menu-dark-background);
  font-size: 0;
  overflow: hidden;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);

  &.theme-light {
    background-color: var(--menu-light-background);
  }
}
</style>
