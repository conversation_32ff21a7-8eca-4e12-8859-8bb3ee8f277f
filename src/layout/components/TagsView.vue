<template>
  <div class="bg-white border-b border-gray-300 h-[34px] relative shadow-sm tags-view-container w-full">
    <ScrollPane ref="scrollPane" @scroll="handleScroll">
      <RouterLink
        v-for="(tag, index) in visitedViews.filter((tag, i) => i !== 0)"
        :key="tag.fullPath || tag.path"
        ref="tag"
        class="bg-white border border-gray-300 cursor-pointer duration-200 first:ml-[15px] h-[26px] hover:bg-gray-100 inline-flex items-center justify-center last:mr-[15px] ml-[5px] mt-[2px] px-2 relative rounded-sm tag-item text-gray-700 text-xs transition-all"
        :class="[isAffix(tag) ? 'px-2' : 'pl-2 pr-1']"
        :style="activeStyle(tag)"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        @dblclick.native.prevent="refreshSelectedTag(tag)"
      >
        <span class="flex items-center" @click.middle="closeSelectedTag(tag)" @contextmenu.prevent="openMenu(tag, $event)">
          {{ tag.title }}
          <i
            v-if="!isAffix(tag)"
            class="close-icon duration-200 flex h-3 hover:bg-gray-200/30 items-center justify-center ml-1 rounded-full transition-all w-3"
            @click.prevent.stop="closeSelectedTag(tag)"
          >
            <svg class="h-2 w-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
            </svg>
          </i>
        </span>
      </RouterLink>
    </ScrollPane>
    <ul
      v-show="visible"
      class="absolute bg-white border border-gray-200 context-menu min-w-[105px] py-1 rounded shadow-lg text-gray-800 text-xs z-[3000]"
      :style="{ left: `${left}px`, top: `${top}px` }"
    >
      <li
        v-for="item in contextMenuItems"
        v-show="item.visible"
        :key="item.id"
        class="cursor-pointer duration-200 hover:bg-gray-100 menu-item px-3 py-1.5 transition-colors"
        @click="onContextItemClick(item.id)"
      >
        <i class="mr-2 text-gray-800 text-xs" :class="item.icon"></i> {{ item.label }}
      </li>
    </ul>
  </div>
</template>

<script>
// 移除 path 导入，避免在浏览器环境中导致 process.cwd 错误

export default {
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    },
    theme() {
      return this.$store.state.settings.theme
    },
    // 右键菜单项（更优雅的生成方式）
    contextMenuItems() {
      return [
        { id: 'refresh', label: '刷新页面', icon: 'el-icon-refresh-right', visible: true },
        { id: 'closeCurrent', label: '关闭当前', icon: 'el-icon-close', visible: !this.isAffix(this.selectedTag) },
        { id: 'closeOthers', label: '关闭其他', icon: 'el-icon-circle-close', visible: true },
        { id: 'closeLeft', label: '关闭左侧', icon: 'el-icon-back', visible: !this.isFirstView() },
        { id: 'closeRight', label: '关闭右侧', icon: 'el-icon-right', visible: !this.isLastView() },
        { id: 'closeAll', label: '全部关闭', icon: 'el-icon-circle-close', visible: true },
      ]
    },
  },
  watch: {
    $route() {
      this.addTags()
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
  },
  mounted() {
    this.initTags()
    this.addTags()
  },
  beforeDestroy() {
    document.body.removeEventListener('click', this.closeMenu)
  },
  methods: {
    isActive(route) {
      return route.path === this.$route.path
    },
    activeStyle(tag) {
      if (!this.isActive(tag)) return {}
      return {
        'background-color': this.theme,
        'border-color': this.theme,
        color: '#fff',
      }
    },
    isAffix(tag) {
      return tag && tag.meta && tag.meta.affix
    },
    isFirstView() {
      try {
        return this.selectedTag.fullPath === this.visitedViews[1].fullPath || this.selectedTag.fullPath === '/index'
      } catch {
        return false
      }
    },
    isLastView() {
      try {
        return this.selectedTag.fullPath === this.visitedViews.at(-1).fullPath
      } catch {
        return false
      }
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      for (const route of routes) {
        if (route.meta && route.meta.affix) {
          // 简单的路径拼接，避免使用 path.resolve（在浏览器环境中会导致 process.cwd 错误）
          const tagPath = basePath === '/' ? `/${route.path}` : `${basePath}/${route.path}`
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta },
          })
        }

        if (route.children) {
          const temporaryTags = this.filterAffixTags(route.children, route.path)
          if (temporaryTags.length > 0) {
            tags = [...tags, ...temporaryTags]
          }
        }
      }

      return tags
    },
    initTags() {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes))
      for (const tag of affixTags) {
        // must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
    },
    addTags() {
      const { name } = this.$route
      if (name) {
        this.$store.dispatch('tagsView/addView', this.$route)
      }

      return false
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag
      this.$nextTick(() => {
        if (!tags || !Array.isArray(tags)) return
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag)
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }

            break
          }
        }
      })
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({
            path: `/redirect${fullPath}`,
          })
        })
      })
    },
    closeSelectedTag(view) {
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeRightTags() {
      this.$store.dispatch('tagsView/delRightTags', this.selectedTag).then((visitedViews) => {
        if (!visitedViews.some((index) => index.fullPath === this.$route.fullPath)) {
          this.toLastView(visitedViews)
        }
      })
    },
    closeLeftTags() {
      this.$store.dispatch('tagsView/delLeftTags', this.selectedTag).then((visitedViews) => {
        if (!visitedViews.some((index) => index.fullPath === this.$route.fullPath)) {
          this.toLastView(visitedViews)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag).catch(() => {})
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === this.$route.path)) {
          return
        }

        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.at(-1)
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === 'Index') {
          this.$router.replace({ path: `/redirect${view.fullPath}` })
        } else {
          const name = this.$store.state.tagsView.enterIndex || 'Index'
          this.$router.push({ name })
        }
      }
    },
    openMenu(tag, event) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const { offsetWidth } = this.$el // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = event.clientX - offsetLeft + 15 // 15: margin right

      this.left = Math.min(left, maxLeft)

      this.top = event.clientY - 50
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    },
    onContextItemClick(action) {
      switch (action) {
        case 'refresh': {
          this.refreshSelectedTag(this.selectedTag)
          break
        }
        case 'closeCurrent': {
          this.closeSelectedTag(this.selectedTag)
          break
        }
        case 'closeOthers': {
          this.closeOthersTags()
          break
        }
        case 'closeLeft': {
          this.closeLeftTags()
          break
        }
        case 'closeRight': {
          this.closeRightTags()
          break
        }
        case 'closeAll': {
          this.closeAllTags(this.selectedTag)
          break
        }
      }
      this.closeMenu()
    },
  },
}
</script>

<style scoped>
/* 平滑过渡动画 */
.tag-item {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
