<template>
  <ElTooltip content="全局搜索" effect="dark" placement="bottom">
    <span class="inline-flex items-center" :class="{ show }">
      <i class="align-middle cursor-pointer el-icon-search text-base" @click.stop="click"> </i>
      <ElSelect
        ref="headerSearchSelect"
        v-model="search"
        class="align-middle bg-transparent duration-200 inline-block transition-[width]"
        default-first-option
        filterable
        placeholder="Search"
        remote
        :remote-method="querySearch"
        :style="{ width: show ? '210px' : '0', marginLeft: show ? '10px' : '0' }"
        @change="change"
      >
        <ElOption v-for="option in options" :key="option.item.path" :label="option.item.title.join(' > ')" :value="option.item" />
      </ElSelect>
    </span>
  </ElTooltip>
</template>

<script>
// make search results more in line with expectations
import Fuse from 'fuse.js'
// 简单的路径解析函数，替代 Node.js 的 path.resolve
const pathResolve = (basePath, routePath) => {
  if (basePath === '/') {
    return `/${routePath.replace(/^\//, '')}`
  }
  return `${basePath.replace(/\/$/, '')}/${routePath.replace(/^\//, '')}`
}

export default {
  data() {
    return {
      search: '',
      options: [],
      searchPool: [],
      show: false,
      fuse: undefined,
    }
  },
  computed: {
    routes() {
      return this.$store.getters.permissionRoutes
    },
  },
  watch: {
    routes() {
      this.searchPool = this.generateRoutes(this.routes)
    },
    searchPool(list) {
      this.initFuse(list)
    },
    show(value) {
      if (value) document.body.addEventListener('click', this.close)
      else document.body.removeEventListener('click', this.close)
    },
  },
  mounted() {
    this.searchPool = this.generateRoutes(this.routes)
  },
  beforeDestroy() {
    document.body.removeEventListener('click', this.close)
  },
  methods: {
    click() {
      this.show = !this.show
      if (this.show) this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()
    },
    close() {
      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()
      this.options = []
      this.show = false
    },
    change(value) {
      const path = value.path
      const query = value.query
      if (this.isHttp(value.path)) {
        // http(s):// 路径新窗口打开
        const pindex = path.indexOf('http')
        window.open(path.slice(pindex), '_blank')
      } else {
        if (query) {
          this.$router.push({ path, query: JSON.parse(query) })
        } else {
          this.$router.push(path)
        }
      }
      this.search = ''
      this.options = []
      this.$nextTick(() => {
        this.show = false
      })
    },
    initFuse(list) {
      this.fuse = new Fuse(list, {
        shouldSort: true,
        threshold: 0.4,
        location: 0,
        distance: 100,
        minMatchCharLength: 1,
        keys: [
          {
            name: 'title',
            weight: 0.7,
          },
          {
            name: 'path',
            weight: 0.3,
          },
        ],
      })
    },
    // Filter out the routes that can be displayed in the sidebar
    // And generate the internationalized title
    generateRoutes(routes, basePath = '/', prefixTitle = []) {
      let response = []

      for (const router of routes) {
        // skip hidden router
        if (router.hidden) continue

        const data = {
          path: this.isHttp(router.path) ? router.path : pathResolve(basePath, router.path),
          title: [...prefixTitle],
        }

        if (router.meta && router.meta.title) {
          data.title = [...data.title, router.meta.title]

          if (router.redirect !== 'noRedirect') {
            // only push the routes with title
            // special case: need to exclude parent router without redirect
            response.push(data)
          }
        }

        // recursive child routes
        if (router.children) {
          const temporaryRoutes = this.generateRoutes(router.children, data.path, data.title)
          if (temporaryRoutes.length > 0) response = [...response, ...temporaryRoutes]
        }
      }
      return response
    },
    querySearch(query) {
      this.options = query === '' ? [] : this.fuse.search(query)
    },
    isHttp(url) {
      return url.includes('http://') || url.includes('https://')
    },
  },
}
</script>
