<template>
  <ElMenu class="h-12.5 leading-12.5" :default-active="activeMenu" mode="horizontal" @select="handleSelect">
    <template v-for="(item, index) in topMenus">
      <ElMenuItem v-if="index < visibleNumber" :key="index" class="mx-2.5 px-1.25" :index="item.path" :style="{ '--theme': theme }">
        <Icon class="svg-icon" :icon="item.meta.icon" /> {{ item.meta.title }}
      </ElMenuItem>
    </template>

    <!-- 顶部菜单超出数量折叠 -->
    <ElSubmenu v-if="topMenus.length > visibleNumber" index="more" :style="{ '--theme': theme }">
      <template #title><Icon class="svg-icon" icon="mingcute:more-3-fill" /> 更多菜单</template>
      <template v-for="(item, index) in topMenus">
        <ElMenuItem v-if="index >= visibleNumber" :key="index" class="mx-2.5 px-1.25" :index="item.path">
          <Icon class="svg-icon" :icon="item.meta.icon" /> {{ item.meta.title }}
        </ElMenuItem>
      </template>
    </ElSubmenu>
  </ElMenu>
</template>

<script>
import constantRoutes from '@/router/modules/common'

// 隐藏侧边栏路由
const hideList = new Set(['/index'])

export default {
  data() {
    return {
      // 顶部栏初始数
      visibleNumber: 5,
      // 当前激活菜单的 index
      currentIndex: undefined,
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    },
    // 顶部显示菜单
    topMenus() {
      return this.buildTopMenus(this.routers)
    },
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRoutes
    },
    // 设置子路由
    childrenMenus() {
      return this.buildChildrenMenus(this.routers, constantRoutes)
    },
    // 默认激活的菜单
    activeMenu() {
      const path = this.$route.path
      let activePath = this.computeActivePath(path, hideList)
      if (path !== undefined && path.lastIndexOf('/') > 0 && !hideList.has(path)) {
        if (!this.$route.meta.link) this.$store.dispatch('app/toggleSideBarHide', false)
      } else if (!this.$route.children) {
        activePath = path
        this.$store.dispatch('app/toggleSideBarHide', true)
      }
      this.activeRoutes(activePath)
      return activePath
    },
  },
  beforeMount() {
    window.addEventListener('resize', this.setVisibleNumber)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setVisibleNumber)
  },
  mounted() {
    this.setVisibleNumber()
  },
  methods: {
    // 工具方法内聚至组件
    isHttp(url = '') {
      return typeof url === 'string' && (url.includes('http://') || url.includes('https://'))
    },
    buildTopMenus(routers = []) {
      const topMenus = []
      routers.map((menu) => {
        if (menu.hidden !== true) {
          if (menu.path === '/') topMenus.push(menu.children[0])
          else topMenus.push(menu)
        }
        return menu
      })
      return topMenus
    },
    buildChildrenMenus(routers = [], constantRoutes_ = []) {
      const childrenMenus = []
      routers.map((router) => {
        for (const index in router.children) {
          const source = router.children[index]
          const item = { ...source }

          if (item.parentPath === undefined) {
            if (router.path === '/') item.path = `/${item.path}`
            else if (!this.isHttp(item.path)) item.path = `${router.path}/${item.path}`
            item.parentPath = router.path
          }
          childrenMenus.push(item)
        }
        return router
      })
      return [...constantRoutes_, ...childrenMenus]
    },
    calcVisibleNumber(bodyWidth) {
      const width = bodyWidth / 3
      return Number.parseInt(width / 85)
    },
    computeActivePath(path = '/', hideSet = new Set()) {
      let activePath = path
      if (path !== undefined && path.lastIndexOf('/') > 0 && !hideSet.has(path)) {
        const temporaryPath = path.slice(1)
        activePath = `/${temporaryPath.slice(0, Math.max(0, temporaryPath.indexOf('/')))}
        `
        activePath = activePath.trim()
      }
      return activePath
    },
    findRouteByPath(routers = [], key) {
      return routers.find((item) => item.path === key)
    },
    findChildrenRouteByPath(childrenMenus = [], key) {
      return childrenMenus.find((item) => item.path === key)
    },
    deriveActiveRoutes(childrenMenus = [], key) {
      const routes = []
      if (childrenMenus && childrenMenus.length > 0) {
        childrenMenus.map((item) => {
          if (key === item.parentPath || (key === 'index' && item.path === '')) routes.push(item)
          return item
        })
      }
      return routes
    },
    // 根据宽度计算设置显示栏数
    setVisibleNumber() {
      const width = document.body.getBoundingClientRect().width
      this.visibleNumber = this.calcVisibleNumber(width)
    },
    // 菜单选择事件
    handleSelect(key, keyPath) {
      this.currentIndex = key
      const route = this.findRouteByPath(this.routers, key)
      if (this.isHttp(key)) {
        // http(s):// 路径新窗口打开
        window.open(key, '_blank')
      } else if (!route || !route.children) {
        // 没有子路由路径内部打开
        const routeMenu = this.findChildrenRouteByPath(this.childrenMenus, key)
        if (routeMenu && routeMenu.query) {
          const query = JSON.parse(routeMenu.query)
          this.$router.push({ path: key, query })
        } else {
          this.$router.push({ path: key })
        }
        this.$store.dispatch('app/toggleSideBarHide', true)
      } else {
        // 显示左侧联动菜单
        this.activeRoutes(key)
        this.$store.dispatch('app/toggleSideBarHide', false)
      }
    },
    // 当前激活的路由
    activeRoutes(key) {
      const routes = this.deriveActiveRoutes(this.childrenMenus, key)
      if (routes.length > 0) this.$store.commit('SET_SIDEBAR_ROUTES', routes)
      else this.$store.dispatch('app/toggleSideBarHide', true)
    },
  },
}
</script>

<style lang="scss" scoped>
.el-menu--horizontal > :deep(.el-menu-item.is-active),
.el-menu--horizontal > .el-submenu.is-active :deep(.el-submenu__title) {
  border-bottom: 2px solid #{'var(--theme)'} !important;
  color: #303133;
}
</style>
