<template>
  <div class="inline-flex items-center pr-4 select-none space-x-3">
    <p class="cursor-pointer flex group items-center text-gray-400 text-sm" @click="goProfile">
      <ElAvatar icon="el-icon-user-solid" size="small" :src="user.avatar" title="个人中心" @error="errorHandler" />
      <span class="pl-1">{{ user.username }}</span>
    </p>
    <i class="cursor-pointer duration-200 hover:scale-120 hover:text-red-400 i-lucide:log-out text-blue-400 transition-all" title="退出登录" @click="logout" />
  </div>
</template>

<script>
export default {
  computed: {
    ...mapGetters(['user']),
  },
  methods: {
    goProfile() {
      this.$router.push({ name: 'UserProfile' })
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$store.dispatch('LogOut').then(() => {
            this.$router.replace({ name: 'Login' })
          })
        })
        .catch(() => {})
    },
    errorHandler() {
      return true
    },
  },
}
</script>
