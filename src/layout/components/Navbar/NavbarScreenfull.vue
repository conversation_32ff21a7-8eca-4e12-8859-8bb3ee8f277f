<template>
  <ElTooltip class="item" content="全屏切换" effect="dark" placement="top">
    <i class="el-icon-full-screen text-xl" @click="click"></i>
  </ElTooltip>
</template>

<script>
import screenfull from 'screenfull'

export default {
  data() {
    return {
      isFullscreen: false,
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.destroy()
  },
  methods: {
    click() {
      if (!screenfull.isEnabled) {
        this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })
        return false
      }
      screenfull.toggle()
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen
    },
    init() {
      if (screenfull.isEnabled) screenfull.on('change', this.change)
    },
    destroy() {
      if (screenfull.isEnabled) screenfull.off('change', this.change)
    },
  },
}
</script>
