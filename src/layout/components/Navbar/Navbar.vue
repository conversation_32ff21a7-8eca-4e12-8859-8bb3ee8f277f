<template>
  <div class="bg-white flex h-12.5 items-center justify-between">
    <div class="flex flex-1 items-center">
      <NavbarHamburger class="cursor-pointer h-full hover:bg-black/5 transition-colors" :is-active="sidebar.opened" />
      <NavbarTopNav v-if="topNav" class="absolute left-12.5" />
      <NavbarBreadcrumb v-else />
    </div>
    <div class="flex gap-x-4 items-center">
      <!-- <NavbarHeaderSearch v-if="device !== 'mobile'" /> -->
      <!-- <NavbarScreenfull v-if="device !== 'mobile'" /> -->
      <!-- <NavbarSizeSelect v-if="device !== 'mobile'" /> -->
      <NavbarAvatar />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device']),
    topNav() {
      return this.$store.state.settings.topNav
    },
  },
}
</script>
