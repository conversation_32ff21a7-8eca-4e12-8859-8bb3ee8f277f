<template>
  <ElBreadcrumb class="pt-1" separator="/">
    <TransitionGroup name="breadcrumb">
      <ElBreadcrumbItem v-for="(item, index) in levelList" :key="item.path">
        <span v-if="item.redirect === 'noRedirect' || index === levelList.length - 1" class="text-gray-500">{{ item.meta.title }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </ElBreadcrumbItem>
    </TransitionGroup>
  </ElBreadcrumb>
</template>

<script>
export default {
  data() {
    return {
      levelList: undefined,
    }
  },
  watch: {
    $route(route) {
      // if you go to the redirect page, do not update the breadcrumbs
      if (route.path.startsWith('/redirect/')) return

      this.getBreadcrumb()
    },
  },
  created() {
    this.getBreadcrumb()
  },
  methods: {
    isDashboard(route) {
      const name = route && route.name
      if (!name) return false
      return String(name).trim() === 'Index'
    },
    buildBreadcrumb(route) {
      if (!route) return []
      let matched = (route.matched || []).filter((item) => item.meta && item.meta.title)
      const first = matched[0]
      if (!this.isDashboard(first)) matched = [{ path: '/index', meta: { title: '首页' } }, ...matched]
      return matched.filter((item) => item.meta && item.meta.title && item.meta.breadcrumb !== false)
    },
    navigateByBreadcrumbItem(router, item) {
      const { redirect, path } = item || {}
      if (!router || !path) return
      if (redirect) router.push(redirect)
      else router.push(path)
    },
    getBreadcrumb() {
      this.levelList = this.buildBreadcrumb(this.$route)
    },
    handleLink(item) {
      this.navigateByBreadcrumbItem(this.$router, item)
    },
  },
}
</script>
