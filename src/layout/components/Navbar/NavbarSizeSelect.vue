<template>
  <ElDropdown trigger="click" @command="handleSetSize">
    <i class="el-icon-s-operation text-xl"></i>
    <template #dropdown>
      <ElDropdownMenu>
        <ElDropdownItem v-for="item of sizeOptions" :key="item.value" :command="item.value" :disabled="size === item.value">
          {{ item.label }}
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>

<script>
export default {
  data() {
    return {
      sizeOptions: [
        { label: '默认', value: 'default' },
        { label: '中号', value: 'medium' },
        { label: '小号', value: 'small' },
        { label: '迷你', value: 'mini' },
      ],
    }
  },
  computed: {
    size() {
      return this.$store.getters.size
    },
  },
  methods: {
    handleSetSize(size) {
      this.$ELEMENT.size = size
      this.$store.dispatch('app/setSize', size)
      this.refreshView()
      this.$message({
        message: 'Switch Size Success',
        type: 'success',
      })
    },
    refreshView() {
      // In order to make the cached page re-rendered
      this.$store.dispatch('tagsView/delAllCachedViews', this.$route)

      const { fullPath } = this.$route

      this.$nextTick(() => {
        this.$router.replace({
          path: `/redirect${fullPath}`,
        })
      })
    },
  },
}
</script>
