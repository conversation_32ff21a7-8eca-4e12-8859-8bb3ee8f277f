<template>
  <TransitionGroup mode="out-in" name="fade-transform">
    <InnerLink
      v-for="(item, index) in iframeViews"
      v-show="$route.path === item.path"
      :key="item.path"
      :iframe-id="`iframe${index}`"
      :src="iframeUrl(item.meta.link, item.query)"
    ></InnerLink>
  </TransitionGroup>
</template>

<script>
export default {
  computed: {
    iframeViews() {
      return this.$store.state.tagsView.iframeViews
    },
  },
  methods: {
    iframeUrl(url, query) {
      if (Object.keys(query).length > 0) {
        const parameters = Object.keys(query)
          .map((key) => `${key}=${query[key]}`)
          .join('&')
        return `${url}?${parameters}`
      }
      return url
    },
  },
}
</script>
