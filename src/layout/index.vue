<template>
  <ElContainer class="h-screen" :class="classObj">
    <ElAside v-if="!sidebar.hide" class="duration-300 ease-in-out transition-all" :width="asideWidth">
      <Sidebar />
    </ElAside>
    <ElContainer direction="vertical">
      <ElHeader :style="{ height: headerHeight }">
        <Navbar />
        <TagsView v-if="needTagsView" />
      </ElHeader>
      <ElMain class="flex flex-1 flex-col min-h-0">
        <Transition mode="out-in" name="fade-transform">
          <KeepAlive :include="cachedViews">
            <RouterView v-if="!$route.meta.link" :key="$route.path" />
          </KeepAlive>
        </Transition>
        <IframeToggle />
      </ElMain>
    </ElContainer>
  </ElContainer>
</template>

<script>
export default {
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
      user: (state) => state.user.user,
    }),
    classObj() {
      return {
        'hide-sidebar': !this.sidebar.opened,
        'without-animation': this.sidebar.withoutAnimation,
      }
    },
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    // Header 高度：Navbar 50px + 可选 TagsView 34px
    headerHeight() {
      return this.needTagsView ? 84 : 50
    },
    // 侧栏宽度（桌面）：展开=200，收起=54，隐藏=0
    asideWidth() {
      if (this.sidebar.hide) return '0px'
      return this.sidebar.opened ? '200px' : '54px'
    },
  },
  mounted() {
    useWatermark({ username: this.user.username, nickname: this.user.nickname, mobile: this.user.mobile })
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
  },
}
</script>

<style>
.el-main,
.el-header,
.el-footer,
.el-aside {
  padding: 0 !important;
}
</style>
