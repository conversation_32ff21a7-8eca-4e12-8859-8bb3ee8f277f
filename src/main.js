import Vue from 'vue'
import 'default-passive-events'

import '@unocss/reset/tailwind-compat.css'
import 'uno.css'

import '@/assets/styles/theme.css'
import '@/assets/styles/index.scss'

import App from './App.vue'
import router from './router'
import store from './store'
import './lib'
import './filter'
import './directive'
import './components'
import './mixins'

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  mounted() {
    this.addTouchStartEvent()
    this.addResizeObserver()
  },
  beforeDestroy() {
    if (this._resizeObserver) {
      try {
        this._resizeObserver.disconnect()
      } catch {}
      this._resizeObserver = undefined
    }
    document.body.removeEventListener('touchstart', this._noopTouchStart)
  },
  methods: {
    addTouchStartEvent() {
      this._noopTouchStart = () => {}
      document.body.addEventListener('touchstart', this._noopTouchStart)
    },
    addResizeObserver() {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          this.setClass()
        }
      })
      resizeObserver.observe(document.querySelector('body'))
      this._resizeObserver = resizeObserver
    },
    setClass() {
      document.documentElement.setAttribute('class', useDeviceType())
    },
  },
  render: (h) => h(App),
})
