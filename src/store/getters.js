export default {
  sidebar: (state) => state.app.sidebar,
  size: (state) => state.app.size,
  device: (state) => state.app.device,

  dict: (state) => state.dict.dict,

  visitedViews: (state) => state.tagsView.visitedViews,
  cachedViews: (state) => state.tagsView.cachedViews,

  token: (state) => state.user.token,
  user: (state) => state.user.user,
  roles: (state) => state.user.roles,
  permissions: (state) => state.user.permissions,

  permissionRoutes: (state) => state.permission.routes,
  topbarRoutes: (state) => state.permission.topbarRoutes,
  defaultRoutes: (state) => state.permission.defaultRoutes,
  sidebarRoutes: (state) => state.permission.sidebarRoutes,
}
