import defaultSettings from '@/config/settings'

const { sideTheme, showSettings, topNav, tagsView, fixedHeader, sidebarLogo, dynamicTitle } = defaultSettings

const storageSetting = useMyLocalStorage.get('layout-setting')

const state = {
  title: '',
  showSettings,
  theme: storageSetting?.theme || '#3b82f6',
  sideTheme: storageSetting?.sideTheme || sideTheme,
  topNav: storageSetting?.topNav || topNav,
  tagsView: storageSetting?.tagsView || tagsView,
  fixedHeader: storageSetting?.fixedHeader || fixedHeader,
  sidebarLogo: storageSetting?.sidebarLogo || sidebarLogo,
  dynamicTitle: storageSetting?.dynamicTitle || dynamicTitle,
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (Object.prototype.hasOwnProperty.call(state, key)) {
      state[key] = value
    }
  },
  SET_TITLE: (state, title) => {
    state.title = title
    document.title = title
  },
}

const actions = {
  // 修改布局设置
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  },
  // 设置网页标题
  setTitle({ commit }, title) {
    commit('SET_TITLE', title)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
