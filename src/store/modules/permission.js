import router from '@/router'
import constantRoutes from '@/router/modules/common'
import dynamicRoutes from '@/router/permissions/dynamic'

// 使用 Vite 的动态导入
const viewsModules = import.meta.glob('@/views/**/**.vue')

const state = {
  routes: [],
  addRoutes: [],
  defaultRoutes: [],
  topbarRoutes: [],
  sidebarRoutes: [],
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = [...constantRoutes, ...(Array.isArray(routes) ? routes : [routes])]
  },
  SET_DEFAULT_ROUTES: (state, routes) => {
    state.defaultRoutes = [...constantRoutes, ...(Array.isArray(routes) ? routes : [routes])]
  },
  SET_TOPBAR_ROUTES: (state, routes) => {
    state.topbarRoutes = routes
  },
  SET_SIDEBAR_ROUTES: (state, routes) => {
    state.sidebarRoutes = routes
  },
}

const actions = {
  // 生成路由
  GenerateRoutes({ commit }) {
    return new Promise((resolve) => {
      // 向后端请求路由数据
      useApiGetRouters().then((response) => {
        const sdata = structuredClone(response)
        const rdata = structuredClone(response)
        const sidebarRoutes = filterAsyncRouter(sdata)
        const rewriteRoutes = filterAsyncRouter(rdata, false, true)
        const asyncRoutes = filterDynamicRoutes(dynamicRoutes)
        // 将动态路由合并到重写路由中
        rewriteRoutes.push(...asyncRoutes)
        commit('SET_ROUTES', rewriteRoutes)
        commit('SET_SIDEBAR_ROUTES', [...constantRoutes, ...(Array.isArray(sidebarRoutes) ? sidebarRoutes : [sidebarRoutes])])
        commit('SET_DEFAULT_ROUTES', sidebarRoutes)
        commit('SET_TOPBAR_ROUTES', sidebarRoutes)
        resolve(rewriteRoutes)
      })
    })
  },
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter((route) => {
    if (type && route.children) {
      route.children = filterChildren(route.children)
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      switch (route.component) {
        case 'Layout': {
          route.component = () => import('@/layout/index.vue')
          break
        }
        case 'ParentView': {
          route.component = () => import('@/layout/empty.vue')
          break
        }
        case 'InnerLink': {
          route.component = () => import('@/layout/link.vue')
          break
        }
        default: {
          const viewsName = typeof route.component === 'string' ? route.component.replace('.vue', '') : route.component
          route.component = viewsModules[`/src/views/${viewsName}.vue`]
        }
      }
    }
    if (route.children && route.children && route.children.length > 0) {
      route.children = filterAsyncRouter(route.children, route, type)
    } else {
      delete route.children
      delete route.redirect
    }
    return true
  })
}

function filterChildren(childrenMap, lastRouter = false) {
  let children = []
  for (const [index, element] of childrenMap.entries()) {
    if (element.children && element.children.length > 0 && element.component === 'ParentView' && !lastRouter) {
      for (const c of element.children) {
        c.path = `${element.path}/${c.path}`
        if (c.children && c.children.length > 0) {
          children = [...children, ...filterChildren(c.children, c)]
          continue
        }
        children.push(c)
      }
      continue
    }
    if (lastRouter) {
      element.path = `${lastRouter.path}/${element.path}`
      if (element.children && element.children.length > 0) {
        children = [...children, ...filterChildren(element.children, element)]
        return
      }
    }
    children = [...children, ...(Array.isArray(element) ? element : [element])]
  }
  return children
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes) {
  const response = []
  for (const route of routes) {
    if (route.permissions) {
      if (appAuth.hasAnyPermission(route.permissions)) {
        response.push(route)
      }
    } else if (route.roles && appAuth.hasAnyRole(route.roles)) {
      response.push(route)
    }
  }
  return response
}

export const loadView = (view) => {
  // eslint-disable-next-line unicorn/prefer-module
  return (resolve) => require([`@/views/${view}`], resolve)
}

export default {
  namespaced: false,
  state,
  mutations,
  actions,
}
