const state = {
  sidebar: {
    opened: useMyCookies.get('sidebarStatus') ? useMyCookies.get('sidebarStatus') === 'true' : true,
    withoutAnimation: false,
    hide: false,
  },
  device: 'desktop',
  size: useMyCookies.get('size') || 'medium',
}

const mutations = {
  TOGGLE_SIDEBAR: (state) => {
    if (state.sidebar.hide) return false
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    useMyCookies.set('sidebarStatus', state.sidebar.opened.toString())
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    useMyCookies.set('sidebarStatus', 'false')
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    useMyCookies.set('size', size)
  },
  SET_SIDEBAR_HIDE: (state, status) => {
    state.sidebar.hide = status
  },
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  toggleSideBarHide({ commit }, status) {
    commit('SET_SIDEBAR_HIDE', status)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
