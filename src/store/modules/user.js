import defaultAvatar from '@/assets/logo/logo.png'

const state = {
  token: useMyCookies.get('token') || '',
  user: {
    avatar: defaultAvatar,
    userId: '',
    username: '',
    nickname: '',
    mobile: '',
    email: '',
    dept: '',
    createTime: '',
  },
  roles: [],
  permissions: [],
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
    token ? useMyCookies.set('token', token) : useMyCookies.remove('token')
  },
  SET_USER: (state, user) => {
    // 需要用到的用户信息
    const { avatar, userId, username, nickname, mobile, email, dept, createTime } = user
    state.user = { ...state.user, avatar: avatar || defaultAvatar, userId, username, nickname, phone: mobile, email, dept, createTime }
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles.length > 0 ? roles : []
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions.length > 0 ? permissions : []
  },
}

const actions = {
  // 登录
  async Login({ commit }, userInfo) {
    const { identifier, credential, code, uuid, authType } = userInfo
    const response = await useApiLogin(identifier, credential, code, uuid, authType)
    console.log('🚀 ‣ response:', response)
    commit('SET_TOKEN', response.accessToken || response.token)
  },

  // 获取用户信息
  async GetInfo({ commit }) {
    const response = await useApiGetInfo()
    commit('SET_USER', response)
    commit('SET_ROLES', response.roles)
    commit('SET_PERMISSIONS', response.permissions)
    return response
  },

  // 退出系统
  async LogOut({ commit, state }) {
    await useApiLogout(state.token)
    commit('SET_TOKEN', '')
    commit('SET_USER', {})
    commit('SET_ROLES', [])
    commit('SET_PERMISSIONS', [])
    useMyLocalStorage.remove('layout-setting')
  },
}

export default {
  namespaced: false,
  state,
  mutations,
  actions,
}
