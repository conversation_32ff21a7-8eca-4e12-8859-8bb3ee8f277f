// 仅保留可序列化字段，避免循环结构导致存储失败
function sanitizeView(view) {
  if (!view) return view
  const { fullPath, path, name, meta, query, params, title } = view
  return { fullPath, path, name, meta, query, params, title }
}

function persist(state) {
  try {
    useMyLocalStorage.set('tags-view', {
      visitedViews: state.visitedViews.map((element) => sanitizeView(element)),
      cachedViews: [...state.cachedViews],
      iframeViews: state.iframeViews.map((element) => sanitizeView(element)),
    })
  } catch (error) {
    // 静默失败，避免影响正常流程
    console.error('[tagsView.persist]', error)
  }
}

const persisted = useMyLocalStorage.get('tags-view') || {}

const state = {
  visitedViews: Array.isArray(persisted?.visitedViews) ? persisted.visitedViews : [],
  cachedViews: Array.isArray(persisted?.cachedViews) ? persisted.cachedViews : [],
  iframeViews: Array.isArray(persisted?.iframeViews) ? persisted.iframeViews : [],
}

const mutations = {
  ADD_IFRAME_VIEW: (state, view) => {
    if (state.iframeViews.some((v) => v.path === view.path)) return
    state.iframeViews.push({
      ...view,
      title: view.meta.title || 'no-name',
    })
    persist(state)
  },
  ADD_VISITED_VIEW: (state, view) => {
    // 跳转链接不生成新标签
    if (view.path.startsWith('/redirect/')) return

    if (state.visitedViews.some((v) => v.path === view.path)) return

    const routeView = {
      ...view,
      title: view.query?.tagName ? `${view.meta.title}-${view.query.tagName}` : view.meta.title || 'no-name',
    }

    state.visitedViews.push(routeView)
    persist(state)
  },
  ADD_CACHED_VIEW: (state, view) => {
    if (!state.cachedViews.includes(view.name) && (!view.meta || !view.meta.noCache)) {
      state.cachedViews.push(view.name)
    }
    persist(state)
  },
  DEL_VISITED_VIEW: (state, view) => {
    state.visitedViews = state.visitedViews.filter((v) => v.path !== view.path)
    state.iframeViews = state.iframeViews.filter((item) => item.path !== view.path)
    persist(state)
  },
  DEL_IFRAME_VIEW: (state, view) => {
    state.iframeViews = state.iframeViews.filter((item) => item.path !== view.path)
    persist(state)
  },
  DEL_CACHED_VIEW: (state, view) => {
    const index = state.cachedViews.indexOf(view.name)
    if (index !== -1) {
      state.cachedViews.splice(index, 1)
    }
    persist(state)
  },
  DEL_OTHERS_VISITED_VIEWS: (state, view) => {
    state.visitedViews = state.visitedViews.filter((v) => v.meta.affix || v.path === view.path)
    state.iframeViews = state.iframeViews.filter((item) => item.path === view.path)
    persist(state)
  },
  DEL_OTHERS_CACHED_VIEWS: (state, view) => {
    const index = state.cachedViews.indexOf(view.name)
    state.cachedViews = index === -1 ? [] : state.cachedViews.slice(index, index + 1)
    persist(state)
  },
  DEL_ALL_VISITED_VIEWS: (state) => {
    state.visitedViews = state.visitedViews.filter((tag) => tag.meta.affix)
    state.iframeViews = []
    persist(state)
  },
  DEL_ALL_CACHED_VIEWS: (state) => {
    state.cachedViews = []
    persist(state)
  },
  UPDATE_VISITED_VIEW: (state, view) => {
    const index = state.visitedViews.findIndex((v) => v.path === view.path)
    if (index !== -1) {
      state.visitedViews.splice(index, 1, view)
    }
    persist(state)
  },
  DEL_RIGHT_VIEWS: (state, view) => {
    const index = state.visitedViews.findIndex((v) => v.path === view.path)
    if (index === -1) return

    state.visitedViews = state.visitedViews.filter((item, index_) => {
      if (index_ <= index || (item.meta && item.meta.affix)) return true

      const index__ = state.cachedViews.indexOf(item.name)
      if (index__ !== -1) state.cachedViews.splice(index__, 1)

      if (item.meta.link) {
        const fi = state.iframeViews.findIndex((v) => v.path === item.path)
        state.iframeViews.splice(fi, 1)
      }
      return false
    })
    persist(state)
  },
  DEL_LEFT_VIEWS: (state, view) => {
    const index = state.visitedViews.findIndex((v) => v.path === view.path)
    if (index === -1) return

    state.visitedViews = state.visitedViews.filter((item, index_) => {
      if (index_ >= index || (item.meta && item.meta.affix)) return true

      const index__ = state.cachedViews.indexOf(item.name)
      if (index__ !== -1) state.cachedViews.splice(index__, 1)

      if (item.meta.link) {
        const fi = state.iframeViews.findIndex((v) => v.path === item.path)
        state.iframeViews.splice(fi, 1)
      }
      return false
    })
    persist(state)
  },
}

const actions = {
  addView({ dispatch }, view) {
    dispatch('addVisitedView', view)
    dispatch('addCachedView', view)
  },
  addIframeView({ commit }, view) {
    commit('ADD_IFRAME_VIEW', view)
  },
  addVisitedView({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
  },
  addCachedView({ commit }, view) {
    commit('ADD_CACHED_VIEW', view)
  },
  delView({ dispatch, state }, view) {
    dispatch('delVisitedView', view)
    dispatch('delCachedView', view)
    return {
      visitedViews: [...state.visitedViews],
      cachedViews: [...state.cachedViews],
    }
  },
  delVisitedView({ commit, state }, view) {
    commit('DEL_VISITED_VIEW', view)
    return [...state.visitedViews]
  },
  delIframeView({ commit, state }, view) {
    commit('DEL_IFRAME_VIEW', view)
    return [...state.iframeViews]
  },
  delCachedView({ commit, state }, view) {
    commit('DEL_CACHED_VIEW', view)
    return [...state.cachedViews]
  },
  delOthersViews({ dispatch, state }, view) {
    dispatch('delOthersVisitedViews', view)
    dispatch('delOthersCachedViews', view)
    return {
      visitedViews: [...state.visitedViews],
      cachedViews: [...state.cachedViews],
    }
  },
  delOthersVisitedViews({ commit, state }, view) {
    commit('DEL_OTHERS_VISITED_VIEWS', view)
    return [...state.visitedViews]
  },
  delOthersCachedViews({ commit, state }, view) {
    commit('DEL_OTHERS_CACHED_VIEWS', view)
    return [...state.cachedViews]
  },
  delAllViews({ dispatch, state }, view) {
    dispatch('delAllVisitedViews', view)
    dispatch('delAllCachedViews', view)
    return {
      visitedViews: [...state.visitedViews],
      cachedViews: [...state.cachedViews],
    }
  },
  delAllVisitedViews({ commit, state }) {
    commit('DEL_ALL_VISITED_VIEWS')
    return [...state.visitedViews]
  },
  delAllCachedViews({ commit, state }) {
    commit('DEL_ALL_CACHED_VIEWS')
    return [...state.cachedViews]
  },
  updateVisitedView({ commit }, view) {
    commit('UPDATE_VISITED_VIEW', view)
  },
  delRightTags({ commit }, view) {
    commit('DEL_RIGHT_VIEWS', view)
    return [...state.visitedViews]
  },
  delLeftTags({ commit }, view) {
    commit('DEL_LEFT_VIEWS', view)
    return [...state.visitedViews]
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
