const state = {
  dict: [],
}

const mutations = {
  SET_DICT: (state, { key, value }) => {
    if (key && key !== '') {
      state.dict.push({
        key,
        value,
      })
    }
  },
  REMOVE_DICT: (state, key) => {
    const index = state.dict.findIndex((item) => item.key === key)
    if (index !== -1) {
      state.dict.splice(index, 1)
    }
  },
  CLEAN_DICT: (state) => {
    state.dict = []
  },
}

const actions = {
  // 设置字典
  setDict({ commit }, data) {
    commit('SET_DICT', data)
  },
  // 删除字典
  removeDict({ commit }, key) {
    commit('REMOVE_DICT', key)
  },
  // 清空字典
  cleanDict({ commit }) {
    commit('CLEAN_DICT')
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
