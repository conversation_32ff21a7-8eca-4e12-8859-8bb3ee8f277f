/* https://github.com/ElementUI/theme-chalk/blob/master/src/common/var.scss */

$--color-primary: #3b82f6; // #3b82f6
$--border-color-lighter: #ebeef5; // #ebeef5

/* icon font path, required */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

/* eslint-disable */
@import '~element-ui/packages/theme-chalk/src/index';

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--color-primary;
}
