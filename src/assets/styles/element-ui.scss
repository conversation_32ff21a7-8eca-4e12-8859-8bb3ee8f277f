/**
 * Element UI 样式覆盖
 * 按组件类别整理的样式规则
 */

/* ==============================
   布局和容器组件
   ============================== */

/* Dialog 对话框 */
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.el-dialog:not(.is-fullscreen) {
  margin-top: 6vh !important;
}

.el-dialog__wrapper.scrollbar .el-dialog .el-dialog__body {
  overflow: auto;
  overflow-x: hidden;
  max-height: 80vh;
  padding: 10px 20px 0;
}

/* Card 卡片 */
.el-card__header {
  padding: 14px 15px 7px;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px;
}

/* ==============================
   表格组件
   ============================== */

/* Table 表格基础样式 */
.el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      word-break: break-word;
      background-color: #f8f8f9 !important;
      color: #515a6e !important;
      height: 40px;
      font-size: 13px;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*='el-icon-'] + span {
      margin-left: 1px;
    }
  }
}

/* Table 表格内按钮样式 */
.el-table .fixed-width .el-button--mini {
  padding-left: 0;
  padding-right: 0;
  width: inherit;
}

/* Table 表格更多操作下拉样式 */
.el-table .el-dropdown-link,
.el-table .el-dropdown-selfdefine {
  cursor: pointer;
  margin-left: 5px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}

/* ==============================
   表单组件
   ============================== */

/* Upload 上传组件 */
.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

/* DatePicker 日期选择器 */
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

/* ==============================
   导航组件
   ============================== */

/* Menu 菜单 */
.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

/* Breadcrumb 面包屑 */
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

/* Dropdown 下拉菜单 */
.el-dropdown-menu {
  a {
    display: block;
  }
}

/* ==============================
   侧边栏菜单覆盖样式
   ============================== */

/* 侧边栏菜单基础样式覆盖 */
.sidebar-container {
  /* 重置 Element UI 样式 */
  .horizontal-collapse-transition {
    transition:
      0s width ease-in-out,
      0s padding-left ease-in-out,
      0s padding-right ease-in-out;
  }

  /* 滚动条样式 */
  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }

  .el-scrollbar__bar.is-vertical {
    right: 0px;
  }

  .el-scrollbar {
    height: 100%;
  }

  &.has-logo {
    .el-scrollbar {
      height: calc(100% - 50px);
    }
  }

  .is-horizontal {
    display: none;
  }

  /* 菜单样式 */
  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
    background-color: transparent !important;
    color: var(--menu-dark-color) !important;

    .theme-light & {
      color: var(--menu-light-color) !important;
    }
  }

  .el-menu-item,
  .el-submenu__title {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    color: inherit !important;
    background-color: transparent !important;

    &:hover {
      background-color: var(--menu-dark-sub-hover) !important;

      .theme-light & {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    &.is-active {
      color: var(--menu-dark-color-active) !important;
    }
  }

  /* 菜单悬停效果 */
  .submenu-title-no-dropdown,
  .el-submenu__title {
    &:hover {
      background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  /* 浅色主题激活状态 */
  .submenu-title-noDropdown.is-active,
  &.theme-light .is-active > .el-submenu__title {
    background: rgba(96, 165, 250, 0.1) !important;
    color: var(--menu-dark-color-active) !important;
  }

  /* 深色主题激活状态 */
  &.theme-dark .is-active > .el-submenu__title {
    display: flex;
    align-items: center;
    color: var(--menu-dark-color-active) !important;
  }

  /* 嵌套菜单样式 */
  .nest-menu .el-submenu > .el-submenu__title,
  .el-submenu .el-menu-item {
    min-width: var(--layout-sidebar-width) !important;

    &:hover {
      background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  /* 深色主题嵌套菜单 */
  &.theme-dark .nest-menu .el-submenu > .el-submenu__title,
  &.theme-dark .el-submenu .el-menu-item {
    background-color: var(--menu-dark-sub-background) !important;
    display: flex;
    align-items: center;

    &:hover {
      background-color: var(--menu-dark-sub-hover) !important;
    }
  }

  .el-submenu .el-menu {
    background-color: var(--menu-dark-sub-background) !important;

    .theme-light & {
      background-color: rgba(0, 0, 0, 0.02) !important;
    }
  }
}

/* 垂直菜单样式覆盖 */
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 10px;
    }
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  /* 子菜单过长时显示滚动条 */
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

/* 折叠菜单样式覆盖 */
.el-menu--collapse .el-menu .el-submenu {
  min-width: var(--layout-sidebar-width) !important;
}

/* 折叠菜单隐藏状态 */
.hide-sidebar {
  .el-submenu {
    overflow: hidden;

    & > .el-submenu__title {
      padding: 0 !important;

      .svg-icon {
        margin-left: 20px;
      }
    }
  }

  .el-menu--collapse {
    .el-submenu {
      & > .el-submenu__title {
        & > span {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
      }
    }
  }
}

/* ==============================
   数据展示组件
   ============================== */

/* Tag 标签 */
.cell {
  .el-tag {
    margin-right: 0px;
  }
}

/* Tree 树形控件 */
.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}

/* ==============================
   反馈组件
   ============================== */

/* Message 消息提示 */
.el-message-box__status + .el-message-box__message {
  word-break: break-word;
}

/* ==============================
   通用工具类
   ============================== */

/* 固定宽度按钮 */
.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

/* 小内边距单元格 */
.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}