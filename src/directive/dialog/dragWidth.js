/**
 * v-dialogDragWidth 可拖动弹窗宽度（右侧边）
 * Copyright (c) 2019 ruoyi
 */

export default {
  bind(element) {
    const dragDom = element.querySelector('.el-dialog')
    const lineElement = document.createElement('div')
    lineElement.style = 'width: 5px; background: inherit; height: 80%; position: absolute; right: 0; top: 0; bottom: 0; margin: auto; z-index: 1; cursor: w-resize;'

    // 保存到元素以便在 unbind 时清理
    element.__dragWidth__ = {
      lineElement,
      mousedownHandler: undefined,
      mousemoveHandler: undefined,
      mouseupHandler: undefined,
    }

    const mousedownHandler = (event) => {
      const disX = event.clientX - element.offsetLeft
      const currentWidth = dragDom.offsetWidth

      const mousemoveHandler = (event_) => {
        event_.preventDefault()
        const l = event_.clientX - disX
        dragDom.style.width = `${currentWidth + l}px`
      }
      const mouseupHandler = () => {
        document.removeEventListener('mousemove', mousemoveHandler)
        document.removeEventListener('mouseup', mouseupHandler)
        // 置空引用，帮助 GC
        element.__dragWidth__.mousemoveHandler = undefined
        element.__dragWidth__.mouseupHandler = undefined
      }

      element.__dragWidth__.mousemoveHandler = mousemoveHandler
      element.__dragWidth__.mouseupHandler = mouseupHandler

      document.addEventListener('mousemove', mousemoveHandler)
      document.addEventListener('mouseup', mouseupHandler)
    }

    element.__dragWidth__.mousedownHandler = mousedownHandler
    lineElement.addEventListener('mousedown', mousedownHandler, false)
    dragDom.append(lineElement)
  },
  unbind(element) {
    const store = element.__dragWidth__
    if (store) {
      const { lineElement, mousedownHandler, mousemoveHandler, mouseupHandler } = store
      if (lineElement && mousedownHandler) lineElement.removeEventListener('mousedown', mousedownHandler)
      if (mousemoveHandler) document.removeEventListener('mousemove', mousemoveHandler)
      if (mouseupHandler) document.removeEventListener('mouseup', mouseupHandler)
      if (lineElement && lineElement.parentNode) lineElement.remove()
      element.__dragWidth__ = undefined
    }
  },
}
