/**
 * v-dialogDrag 弹窗拖拽
 * Copyright (c) 2019 ruoyi
 */

export default {
  bind(element, binding) {
    const value = binding.value
    if (value === false) return
    // 获取拖拽内容头部
    const dialogHeaderElement = element.querySelector('.el-dialog__header')
    const dragDom = element.querySelector('.el-dialog')
    dialogHeaderElement.style.cursor = 'move'
    // 获取原有属性 ie dom 元素.currentStyle 火狐谷歌 window.getComputedStyle(dom 元素，null);
    const sty = dragDom.currentStyle || globalThis.getComputedStyle(dragDom)
    dragDom.style.position = 'absolute'
    dragDom.style.marginTop = 0
    let width = dragDom.style.width
    width = width.includes('%') ? +document.body.clientWidth * (+width.replaceAll('%', '') / 100) : +width.replaceAll('px', '')
    dragDom.style.left = `${(document.body.clientWidth - width) / 2}px`
    // 保存到元素以便解绑时清理
    element.__dialogDrag__ = {
      mousedownHandler: undefined,
      mousemoveHandler: undefined,
      mouseupHandler: undefined,
    }

    // 鼠标按下事件
    const mousedownHandler = (event) => {
      // 鼠标按下，计算当前元素距离可视区的距离 (鼠标点击位置距离可视窗口的距离)
      const disX = event.clientX - dialogHeaderElement.offsetLeft
      const disY = event.clientY - dialogHeaderElement.offsetTop

      // 获取到的值带 px 正则匹配替换
      let styL, styT

      // 注意在 ie 中 第一次获取到的值为组件自带 50% 移动之后赋值为 px
      if (sty.left.includes('%')) {
        styL = +document.body.clientWidth * (+sty.left.replaceAll('%', '') / 100)
        styT = +document.body.clientHeight * (+sty.top.replaceAll('%', '') / 100)
      } else {
        styL = +sty.left.replaceAll('px', '')
        styT = +sty.top.replaceAll('px', '')
      }

      // 鼠标拖拽事件
      const mousemoveHandler = (event) => {
        // 通过事件委托，计算移动的距离（开始拖拽至结束拖拽的距离）
        const l = event.clientX - disX
        const t = event.clientY - disY

        const finallyL = l + styL
        const finallyT = t + styT

        // 移动当前元素
        dragDom.style.left = `${finallyL}px`
        dragDom.style.top = `${finallyT}px`
      }

      const mouseupHandler = () => {
        document.removeEventListener('mousemove', mousemoveHandler)
        document.removeEventListener('mouseup', mouseupHandler)
        element.__dialogDrag__.mousemoveHandler = undefined
        element.__dialogDrag__.mouseupHandler = undefined
      }

      element.__dialogDrag__.mousemoveHandler = mousemoveHandler
      element.__dialogDrag__.mouseupHandler = mouseupHandler

      document.addEventListener('mousemove', mousemoveHandler)
      document.addEventListener('mouseup', mouseupHandler)
    }

    element.__dialogDrag__.mousedownHandler = mousedownHandler
    dialogHeaderElement.addEventListener('mousedown', mousedownHandler)
  },
  unbind(element) {
    const store = element.__dialogDrag__
    if (store) {
      const dialogHeaderElement = element.querySelector('.el-dialog__header')
      const { mousedownHandler, mousemoveHandler, mouseupHandler } = store
      if (dialogHeaderElement && mousedownHandler) dialogHeaderElement.removeEventListener('mousedown', mousedownHandler)
      if (mousemoveHandler) document.removeEventListener('mousemove', mousemoveHandler)
      if (mouseupHandler) document.removeEventListener('mouseup', mouseupHandler)
      element.__dialogDrag__ = undefined
    }
  },
}
