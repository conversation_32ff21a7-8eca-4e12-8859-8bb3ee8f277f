/**
 * v-resize 尺寸监听指令（全局）
 *
 * 作用：监听元素尺寸变化，回调返回最新尺寸（number）。
 * 实现：优先使用 ResizeObserver，并用 requestAnimationFrame 合并同帧多次变更；
 *      不支持时降级为 setInterval 轮询。解绑时完整释放（disconnect/clearInterval/cancelAnimationFrame）。
 *
 * 用法：
 *  - 简单用法（对象回调）：
 *    <div v-resize="onResize" />
 *    methods: { onResize({ width, height }) { ... } }
 *
 *  - 高级用法（对象配置）：
 *    <div v-resize="{ handler: onResize, throttle: 50, heightOnly: true }" />
 *    参数说明：
 *      handler    Function 必填，回调函数
 *      throttle   Number   可选，节流时间（毫秒），默认 0（不节流），领先 + 末尾触发
 *      heightOnly Boolean   可选，仅回调高度（仅传递 number），默认 false
 *      widthOnly  Boolean   可选，仅回调宽度（仅传递 number），默认 false
 *      box        String    可选，ResizeObserverOptions.box，默认 'content-box'
 */

function resolveOptions(value) {
  if (typeof value === 'function') return { handler: value }
  const options = value && typeof value === 'object' ? value : {}
  return {
    handler: typeof options.handler === 'function' ? options.handler : () => {},
    throttle: Number.isFinite(options.throttle) ? Number(options.throttle) : 0,
    heightOnly: Boolean(options.heightOnly),
    widthOnly: Boolean(options.widthOnly),
    box: typeof options.box === 'string' ? options.box : 'content-box',
  }
}

function emitWithShape(element, options, size) {
  const { handler, heightOnly, widthOnly } = options
  if (heightOnly) return handler(size.height)
  if (widthOnly) return handler(size.width)
  return handler(size)
}

export default {
  bind(element, binding) {
    const options = resolveOptions(binding.value)
    const callback = (size) => emitWithShape(element, options, size)

    // rAF 合帧与节流（领先 + 末尾）
    let lastW
    let lastH
    let rafId
    let lastEmitTs = 0
    let trailingTimer

    const commit = (w, h) => {
      const now = Date.now()
      const doEmit = () => {
        lastEmitTs = Date.now()
        callback({ width: w, height: h })
      }

      if (!options.throttle || options.throttle <= 0) {
        doEmit()
        return
      }

      if (now - lastEmitTs >= options.throttle) {
        // 领先触发
        doEmit()
      } else if (!trailingTimer) {
        // 末尾触发（取最后一次值）
        const delay = Math.max(0, options.throttle - (now - lastEmitTs))
        trailingTimer = setTimeout(() => {
          trailingTimer = undefined
          doEmit()
        }, delay)
      }
    }

    const schedule = (w, h) => {
      if (w === lastW && h === lastH) return
      lastW = w
      lastH = h
      if (rafId) cancelAnimationFrame(rafId)
      rafId = requestAnimationFrame(() => commit(w, h))
    }

    if (globalThis.ResizeObserver) {
      try {
        const ro = new ResizeObserver((entries) => {
          for (const entry of entries) {
            const { width, height } = entry.contentRect
            schedule(Math.round(width), Math.round(height))
          }
        })
        // @ts-ignore 部分浏览器支持 box 选项
        ro.observe(element, { box: options.box })
        element.__vueResizeObserver__ = ro
        // 主动触发一次，避免首次为 0 的情况
        const rect = element.getBoundingClientRect()
        schedule(Math.round(rect.width), Math.round(rect.height))
      } catch {
        // 某些低版本浏览器 ResizeObserver 不可用或报错，退化
        fallbackPoll(element, schedule)
      }
    } else {
      fallbackPoll(element, schedule)
    }

    element.__vueResizeCleanup__ = () => {
      if (element.__vueResizeObserver__) {
        try {
          element.__vueResizeObserver__.disconnect()
        } catch {}
        element.__vueResizeObserver__ = undefined
      }
      if (element.__vueSetInterval__) {
        clearInterval(element.__vueSetInterval__)
        element.__vueSetInterval__ = undefined
      }
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = undefined
      }
      if (trailingTimer) {
        clearTimeout(trailingTimer)
        trailingTimer = undefined
      }
    }
  },
  unbind(element) {
    if (typeof element.__vueResizeCleanup__ === 'function') {
      element.__vueResizeCleanup__()
      element.__vueResizeCleanup__ = undefined
    }
  },
}

function fallbackPoll(element, schedule) {
  let width = 0
  let height = 0
  const tick = () => {
    const style = document.defaultView.getComputedStyle(element)
    const w = Number.parseFloat(style.width) || 0
    const h = Number.parseFloat(style.height) || 0
    if (w !== width || h !== height) {
      width = w
      height = h
      schedule(w, h)
    }
  }
  element.__vueSetInterval__ = setInterval(tick, 300)
}
