/**
 * v-hasPermission 操作权限处理
 */

import store from '@/store'

export default {
  inserted(element, binding) {
    const { value } = binding
    const allPermission = '*:*:*'
    const permissions = store.getters && store.getters.permissions

    if (value && Array.isArray(value) && value.length > 0) {
      const permissionFlags = value

      const hasPermissions = permissions.some((permission) => {
        return allPermission === permission || permissionFlags.includes(permission)
      })

      if (!hasPermissions) {
        element.remove()
      }
    } else {
      console.error('请设置操作权限标签值')
    }
  },
}
