/**
 * v-hasRole 角色权限处理
 * Copyright (c) 2019 ruoyi
 */

import store from '@/store'

export default {
  inserted(element, binding) {
    const { value } = binding
    const superAdminRole = 'ADMIN'
    const roles = store.getters && store.getters.roles

    if (value && Array.isArray(value) && value.length > 0) {
      const roleFlags = value

      const hasRole = roles.some((role) => {
        return superAdminRole === role || roleFlags.includes(role)
      })

      if (!hasRole) {
        element.remove()
      }
    } else {
      console.error('请设置角色权限标签值')
    }
  },
}
