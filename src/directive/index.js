import Vue from 'vue'

import hasRole from './permission/hasRole'
import hasPermission from './permission/hasPermission'
import dialogDrag from './dialog/drag'
import dialogDragWidth from './dialog/dragWidth'
import dialogDragHeight from './dialog/dragHeight'
import resize from './resize'

Vue.directive('hasRole', hasRole)
Vue.directive('hasPermission', hasPermission)
Vue.directive('dialogDrag', dialogDrag)
Vue.directive('dialogDragWidth', dialogDragWidth)
Vue.directive('dialogDragHeight', dialogDragHeight)
Vue.directive('resize', resize)

if (globalThis.Vue) {
  globalThis.hasRole = hasRole
  globalThis.hasPermission = hasPermission
}
