<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<script>
export default {
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title ? `${title} - ${useEnvironment.VITE_TITLE}` : useEnvironment.VITE_TITLE
      },
    }
  },
}
</script>

<style lang="scss">
/* HTML 和 Body 基础样式 */
html {
  height: 100%;
  box-sizing: border-box;
}

body {
  height: 100%;
  font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
}

/* 应用根容器 */
#app {
  height: 100%;
}

/* 链接样式 */
a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

/* 链接类型样式 */
.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

/* SVG 和图片样式 */
svg,
img {
  display: inline-block;
}
</style>
