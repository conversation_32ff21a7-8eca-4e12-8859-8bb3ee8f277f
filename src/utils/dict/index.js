import Dict from './Dict'
import { mergeOptions } from './DictOptions'

export default function DictPlugin(Vue, options) {
  mergeOptions(options)
  Vue.mixin({
    data() {
      if (!this.$options || !this.$options.dicts) return {}

      const dict = new Dict()
      dict.owner = this
      return {
        dict,
      }
    },
    created() {
      if (!this.dict || !(this.dict instanceof Dict)) return

      options.onCreated && options.onCreated(this.dict)
      this.dict.init(this.$options.dicts).then(() => {
        options.onReady && options.onReady(this.dict)
        this.$nextTick(() => {
          this.$emit('dict-ready', this.dict)
          if (this.$options.methods && this.$options.methods.onDictReady instanceof Function) this.$options.methods.onDictReady.call(this, this.dict)
        })
      })
    },
  })
}
