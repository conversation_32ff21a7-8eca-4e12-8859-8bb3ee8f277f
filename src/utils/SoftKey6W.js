export default class SoftKey6W {
  static Socket_UK
  static url
  static LastError
  constructor() {
    const isIE11 = navigator.userAgent.includes('Trident') && navigator.userAgent.includes('rv:11.0')
    const isEDGE = navigator.userAgent.includes('Edge')
    const u = document.URL
    if (u.slice(0, 5) === 'https') {
      if (isIE11 || isEDGE) {
        this.url = isIE11 ? 'wss://127.0.0.1:4007/xxx' : 'ws://127.0.0.1:4007/xxx'
      } else {
        this.url = 'ws://localhost:4007/xxx'
      }
    } else {
      this.url = 'ws://127.0.0.1:4007/xxx'
    }

    // eslint-disable-next-line no-undef
    // this.Socket_UK = typeof MozWebSocket == 'undefined' ? new WebSocket(this.url, 'usbkey-protocol') : new MozWebSocket(this.url, 'usbkey-protocol')
    this.Socket_UK = new WebSocket(this.url, 'usbkey-protocol')
    this.Socket_UK.addEventListener('error', (event) => {
      this.$message.error(`未能连接服务程序，请确定服务程序是否安装。`)
      // alert('未能连接服务程序，请确定服务程序是否安装。')
    })
    SoftKey6W.LastError = 0
  }

  _FindPort = function (UK, start) {
    const message = {
      FunName: 'FindPort',
      start,
    }
    UK.send(JSON.stringify(message))
  }

  _FindPort_2 = function (UK, start, in_data, verf_data) {
    const message = {
      FunName: 'FindPort_2',
      start,
      in_data,
      verf_data,
    }
    UK.send(JSON.stringify(message))
  }

  _FindPort_3 = function (UK, start, in_data, verf_data) {
    const message = {
      FunName: 'FindPort_3',
      start,
      in_data,
      verf_data,
    }
    UK.send(JSON.stringify(message))
  }

  _GetVersion = function (UK, Path) {
    const message = {
      FunName: 'GetVersion',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _GetVersionEx = function (UK, Path) {
    const message = {
      FunName: 'GetVersionEx',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _GetID_1 = function (UK, Path) {
    const message = {
      FunName: 'GetID_1',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _GetID_2 = function (UK, Path) {
    const message = {
      FunName: 'GetID_2',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _sRead = function (UK, Path) {
    const message = {
      FunName: 'sRead',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _sWrite = function (UK, InData, Path) {
    const message = {
      FunName: 'sWrite',
      InData,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _sWrite_2 = function (UK, InData, Path) {
    const message = {
      FunName: 'sWrite_2',
      InData,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _sWrite_2Ex = function (UK, InData, Path) {
    const message = {
      FunName: 'sWrite_2Ex',
      InData,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _sWriteEx = function (UK, InData, Path) {
    const message = {
      FunName: 'sWriteEx',
      InData,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _sWriteEx_New = function (UK, InData, Path) {
    const message = {
      FunName: 'sWriteEx_New',
      InData,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _sWrite_2Ex_New = function (UK, InData, Path) {
    const message = {
      FunName: 'sWrite_2Ex_New',
      InData,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  static _SetBuf = function (UK, InData, pos) {
    const message = {
      FunName: 'SetBuf',
      InData,
      pos,
    }
    UK.send(JSON.stringify(message))
  }

  static _GetBuf = function (UK, pos) {
    const message = {
      FunName: 'GetBuf',
      pos,
    }
    UK.send(JSON.stringify(message))
  }

  static _YRead = function (UK, Address, length_, HKey, LKey, Path) {
    const message = {
      FunName: 'YRead',
      Address,
      len: length_,
      HKey,
      LKey,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  static _YWrite = function (UK, Address, length_, HKey, LKey, Path) {
    const message = {
      FunName: 'YWrite',
      Address,
      len: length_,
      HKey,
      LKey,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _YReadString = function (UK, Address, length_, HKey, LKey, Path) {
    const message = {
      FunName: 'YReadString',
      Address,
      len: length_,
      HKey,
      LKey,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _YWriteString = function (UK, InString, Address, HKey, LKey, Path) {
    const message = {
      FunName: 'YWriteString',
      InString,
      Address,
      HKey,
      LKey,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _SetWritePassword = function (UK, W_Hkey, W_Lkey, new_Hkey, new_Lkey, Path) {
    const message = {
      FunName: 'SetWritePassword',
      W_Hkey,
      W_Lkey,
      new_Hkey,
      new_Lkey,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _SetReadPassword = function (UK, W_Hkey, W_Lkey, new_Hkey, new_Lkey, Path) {
    const message = {
      FunName: 'SetReadPassword',
      W_Hkey,
      W_Lkey,
      new_Hkey,
      new_Lkey,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _DecString = function (UK, InString, Key) {
    const message = {
      FunName: 'DecString',
      InString,
      Key,
    }
    UK.send(JSON.stringify(message))
  }

  _EncString = function (UK, InString, Path) {
    const message = {
      FunName: 'EncString',
      InString,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _EncString_New = function (UK, InString, Path) {
    const message = {
      FunName: 'EncString_New',
      InString,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _Cal = function (UK, Path) {
    const message = {
      FunName: 'Cal',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _Cal_New = function (UK, Path) {
    const message = {
      FunName: 'Cal_New',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _SetCal_2 = function (UK, Key, Path) {
    const message = {
      FunName: 'SetCal_2',
      Key,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _SetCal_New = function (UK, Key, Path) {
    const message = {
      FunName: 'SetCal_New',
      Key,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  static _SetEncBuf = function (UK, InData, pos) {
    const message = {
      FunName: 'SetEncBuf',
      InData,
      pos,
    }
    UK.send(JSON.stringify(message))
  }

  static _GetEncBuf = function (UK, pos) {
    const message = {
      FunName: 'GetEncBuf',
      pos,
    }
    UK.send(JSON.stringify(message))
  }

  _ReSet = function (UK, Path) {
    const message = {
      FunName: 'ReSet',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _MacAddr = function (UK) {
    const message = {
      FunName: 'MacAddr',
    }
    UK.send(JSON.stringify(message))
  }

  _GetChipID = function (UK, Path) {
    const message = {
      FunName: 'GetChipID',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  static _StarGenKeyPair = function (UK, Path) {
    const message = {
      FunName: 'StarGenKeyPair',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  static _GenPubKeyY = function (UK) {
    const message = {
      FunName: 'GenPubKeyY',
    }
    UK.send(JSON.stringify(message))
  }

  static _GenPubKeyX = function (UK) {
    const message = {
      FunName: 'GenPubKeyX',
    }
    UK.send(JSON.stringify(message))
  }

  static _GenPriKey = function (UK) {
    const message = {
      FunName: 'GenPriKey',
    }
    UK.send(JSON.stringify(message))
  }

  _GetPubKeyY = function (UK, Path) {
    const message = {
      FunName: 'GetPubKeyY',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _GetPubKeyX = function (UK, Path) {
    const message = {
      FunName: 'GetPubKeyX',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _GetSm2UserName = function (UK, Path) {
    const message = {
      FunName: 'GetSm2UserName',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _Set_SM2_KeyPair = function (UK, PriKey, PubKeyX, PubKeyY, sm2UserName, Path) {
    const message = {
      FunName: 'Set_SM2_KeyPair',
      PriKey,
      PubKeyX,
      PubKeyY,
      sm2UserName,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _YtSign = function (UK, SignMessage, Pin, Path) {
    const message = {
      FunName: 'YtSign',
      SignMsg: SignMessage,
      Pin,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _YtSign_2 = function (UK, SignMessage, Pin, Path) {
    const message = {
      FunName: 'YtSign_2',
      SignMsg: SignMessage,
      Pin,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _YtVerfiy = function (UK, id, SignMessage, PubKeyX, PubKeyY, VerfiySign, Path) {
    const message = {
      FunName: 'YtVerfiy',
      id,
      SignMsg: SignMessage,
      PubKeyX,
      PubKeyY,
      VerfiySign,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _SM2_DecString = function (UK, InString, Pin, Path) {
    const message = {
      FunName: 'SM2_DecString',
      InString,
      Pin,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _SM2_EncString = function (UK, InString, Path) {
    const message = {
      FunName: 'SM2_EncString',
      InString,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _YtSetPin = function (UK, OldPin, NewPin, Path) {
    const message = {
      FunName: 'YtSetPin',
      OldPin,
      NewPin,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _FindU = function (UK, start) {
    const message = {
      FunName: 'FindU',
      start,
    }
    UK.send(JSON.stringify(message))
  }

  _FindU_2 = function (UK, start, in_data, verf_data) {
    const message = {
      FunName: 'FindU_2',
      start,
      in_data,
      verf_data,
    }
    UK.send(JSON.stringify(message))
  }

  _FindU_3 = function (UK, start, in_data, verf_data) {
    const message = {
      FunName: 'FindU_3',
      start,
      in_data,
      verf_data,
    }
    UK.send(JSON.stringify(message))
  }

  _IsUReadOnly = function (UK, Path) {
    const message = {
      FunName: 'IsUReadOnly',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _SetUReadOnly = function (UK, Path) {
    const message = {
      FunName: 'SetUReadOnly',
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  _SetHidOnly = function (UK, IsHidOnly, Path) {
    const message = {
      FunName: 'SetHidOnly',
      IsHidOnly,
      Path,
    }
    UK.send(JSON.stringify(message))
  }

  static ResetOrder = function (UK) {
    const message = {
      FunName: 'ResetOrder',
    }
    UK.send(JSON.stringify(message))
  }

  ContinueOrder = function (UK) {
    const message = {
      FunName: 'ContinueOrder',
    }
    UK.send(JSON.stringify(message))
  }

  _ComputerName = function (UK) {
    const message = {
      FunName: 'ComputerName',
    }
    UK.send(JSON.stringify(message))
  }

  err_Connect = function () {
    if (SoftKey6W.bConnect === 1) return
    this.$message.error(`未能连接服务程序，请确定服务程序是否安装。`)
    // alert('未能连接服务程序，请确定服务程序是否安装。')
  }

  SendCmdAndWait = function (IsReturnError, fun, parameter1, parameter2, parameter3, parameter4, parameter5, parameter6, parameter7, parameter8, parameter9, parameter10) {
    let UK

    return new Promise((resolve, reject) => {
      try {
        UK = new WebSocket(this.url, 'usbkey-protocol')
        UK.addEventListener('open', () => {
          SoftKey6W.ResetOrder(UK) //
        })

        UK.addEventListener('message', (Message) => {
          const UK_Data = JSON.parse(Message.data)
          let return_value
          if (UK_Data.type !== 'Process') return //
          switch (UK_Data.order) {
            case 0: {
              fun(UK, parameter1, parameter2, parameter3, parameter4, parameter5, parameter6, parameter7, parameter8, parameter9, parameter10)
              break
            } //!!!!!
            case 1: {
              SoftKey6W.LastError = UK_Data.LastError
              return_value = UK_Data.return_value
              if (SoftKey6W.LastError !== 0 && IsReturnError) return_value = SoftKey6W.LastError
              //
              UK.close()
              resolve(return_value)
              break
            }
          }
        })
        UK.addEventListener('close', () => {})
        UK.addEventListener('error', (event) => {
          this.$message.error('未能连接服务程序，请确定服务程序是否安装。')
        })
      } catch (error) {
        this.$message.error(`${error.name}: ${error.message}`)
        resolve(false)
      }
    })
  }

  GetLastError = function () {
    return SoftKey6W.LastError
  }

  FindPort = function (start) {
    return this.SendCmdAndWait(false, this._FindPort, start)
  }

  FindPort_2 = function (start, in_data, verf_data) {
    return this.SendCmdAndWait(false, this._FindPort_2, start, in_data, verf_data)
  }

  GetVersionEx = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetVersionEx, KeyPath)
  }

  GetVersion = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetVersion, KeyPath)
  }

  GetID_1 = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetID_1, KeyPath)
  }

  GetID_2 = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetID_2, KeyPath)
  }

  GetChipID = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetChipID, KeyPath)
  }

  SetWritePassword = function (W_HKey, W_LKey, new_HKey, new_LKey, KeyPath) {
    return this.SendCmdAndWait(true, this._SetWritePassword, W_HKey, W_LKey, new_HKey, new_LKey, KeyPath)
  }

  SetReadPassword = function (W_HKey, W_LKey, new_HKey, new_LKey, KeyPath) {
    return this.SendCmdAndWait(true, this._SetReadPassword, W_HKey, W_LKey, new_HKey, new_LKey, KeyPath)
  }

  SetCal_2 = function (Key, KeyPath) {
    return this.SendCmdAndWait(true, this._SetCal_2, Key, KeyPath)
  }

  SetCal_New = function (Key, KeyPath) {
    return this.SendCmdAndWait(true, this._SetCal_New, Key, KeyPath)
  }

  EncString = function (InString, KeyPath) {
    return this.SendCmdAndWait(false, this._EncString, InString, KeyPath)
  }

  EncString_New = function (InString, KeyPath) {
    return this.SendCmdAndWait(false, this._EncString_New, InString, KeyPath)
  }

  Cal = function (Inbuf, KeyPath) {
    return this.SubCal(this._Cal, Inbuf, KeyPath)
  }

  Cal_New = function (Inbuf, KeyPath) {
    return this.SubCal(this._Cal_New, Inbuf, KeyPath)
  }

  sWriteEx = function (in_data, KeyPath) {
    return this.SendCmdAndWait(false, this._sWriteEx, in_data, KeyPath)
  }

  sWrite_2Ex = function (in_data, KeyPath) {
    return this.SendCmdAndWait(false, this._sWrite_2Ex, in_data, KeyPath)
  }

  sWriteEx_New = function (in_data, KeyPath) {
    return this.SendCmdAndWait(false, this._sWriteEx_New, in_data, KeyPath)
  }

  sWrite_2Ex_New = function (in_data, KeyPath) {
    return this.SendCmdAndWait(false, this._sWrite_2Ex_New, in_data, KeyPath)
  }

  sWrite = function (in_data, KeyPath) {
    return this.SendCmdAndWait(true, this._sWrite, in_data, KeyPath)
  }

  sWrite_2 = function (in_data, KeyPath) {
    return this.SendCmdAndWait(true, this._sWrite_2, in_data, KeyPath)
  }

  sRead = function (KeyPath) {
    return this.SendCmdAndWait(false, this._sRead, KeyPath)
  }

  YWrite = function (indata, address, nlen, HKey, LKey, KeyPath) {
    let UK

    return new Promise((resolve, reject) => {
      // UK = typeof MozWebSocket == 'undefined' ? new WebSocket(this.url, 'usbkey-protocol') : new MozWebSocket(this.url, 'usbkey-protocol')
      UK = new WebSocket(this.url, 'usbkey-protocol')

      try {
        const index = 0
        let order
        UK.addEventListener('open', () => {
          SoftKey6W.ResetOrder(UK) //
        })
        UK.addEventListener('message', (Message) => {
          // UK.onmessage = function got_packet(Message) {
          const UK_Data = JSON.parse(Message.data)
          let return_value
          if (UK_Data.type !== 'Process') return //
          if (UK_Data.order < nlen) {
            SoftKey6W._SetBuf(UK, indata[UK_Data.order], UK_Data.order)
            return
          } else {
            order = UK_Data.order - nlen
          }

          switch (order) {
            case 0: {
              SoftKey6W.LastError = UK_Data.LastError
              if (SoftKey6W.LastError !== 0) {
                UK.close()
                resolve(UK_Data.return_value)
                return
              }
              SoftKey6W._YWrite(UK, address, nlen, HKey, LKey, KeyPath)

              break
            } //!!!!!
            case 1: {
              SoftKey6W.LastError = UK_Data.LastError
              return_value = UK_Data.return_value
              if (SoftKey6W.LastError !== 0) {
                return_value = SoftKey6W.LastError
              }
              UK.close()
              resolve(return_value)

              break
            }
          }
        })
        UK.addEventListener('close', () => {})
        UK.addEventListener('error', (event) => {
          this.$message.error('未能连接服务程序，请确定服务程序是否安装。')
        })
      } catch (error) {
        this.$message.error(`${error.name}: ${error.message}`)
        resolve(false)
      }
    })
  }

  YWriteString = function (InString, Address, HKey, LKey, KeyPath) {
    return this.SendCmdAndWait(true, this._YWriteString, InString, Address, HKey, LKey, KeyPath)
  }

  YRead = function (address, nlen, HKey, LKey, KeyPath) {
    const outb = new Uint8Array(nlen)
    let UK
    return new Promise((resolve, reject) => {
      // UK = typeof MozWebSocket == 'undefined' ? new WebSocket(this.url, 'usbkey-protocol') : new MozWebSocket(this.url, 'usbkey-protocol')
      UK = new WebSocket(this.url, 'usbkey-protocol')

      try {
        let index, order
        UK.addEventListener('open', () => {
          SoftKey6W.ResetOrder(UK) //
        })
        UK.addEventListener('message', (Message) => {
          // UK.onmessage = function got_packet(Message) {
          const UK_Data = JSON.parse(Message.data)
          let return_value
          if (UK_Data.type !== 'Process') return //
          if (UK_Data.order < 2) {
            switch (UK_Data.order) {
              case 0: {
                SoftKey6W._YRead(UK, address, nlen, HKey, LKey, KeyPath)

                break
              } //!!!!!
              case 1: {
                SoftKey6W.LastError = UK_Data.LastError
                if (SoftKey6W.LastError !== 0) {
                  UK.close()
                  resolve(outb)
                  return
                }

                index = 0
                SoftKey6W._GetBuf(UK, index) //

                break
              }
            }
          } else {
            SoftKey6W.LastError = UK_Data.LastError
            if (SoftKey6W.LastError !== 0) {
              UK.close()
              resolve(outb)
              return
            }
            outb[index] = UK_Data.return_value
            index++
            if (UK_Data.LastError !== 0 || index >= nlen) {
              UK.close()
              resolve(outb)
              return
            }
            SoftKey6W._GetBuf(UK, index) //
          }
        })
        UK.addEventListener('close', () => {})
        UK.addEventListener('error', (event) => {
          this.$message.error('未能连接服务程序，请确定服务程序是否安装。')
        })
      } catch (error) {
        this.$message.error(`${error.name}: ${error.message}`)
        resolve(false)
        // alert(`${error.name}: ${error.message}`)
      }
    })
    //   return outb
  }

  YReadString = function (Address, nlen, HKey, LKey, KeyPath) {
    return this.SendCmdAndWait(false, this._YReadString, Address, nlen, HKey, LKey, KeyPath)
  }

  ReSet = function (KeyPath) {
    return this.SendCmdAndWait(true, this._ReSet, KeyPath)
  }

  SetCal = function (HKey, LKey, new_HKey, new_LKey, KeyPath) {
    return this.SendCmdAndWait(true, this._SetCal, HKey, LKey, new_HKey, new_LKey, KeyPath)
  }

  SetID = function (Seed, KeyPath) {
    return this.SendCmdAndWait(true, this._SetID, Seed, KeyPath)
  }

  GetProduceDate = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetProduceDate, KeyPath)
  }

  SetHidOnly = function (IsHidOnly, KeyPath) {
    return this.SendCmdAndWait(true, this._SetHidOnly, IsHidOnly, KeyPath)
  }

  SetUReadOnly = function (KeyPath) {
    return this.SendCmdAndWait(true, this._SetUReadOnly, KeyPath)
  }

  StarGenKeyPair = function (KeyPath) {
    const KeyPairInfo = {
      GenPriKey: '',
      GenPubKeyX: '',
      GenPubKeyY: '',
    }
    let UK

    return new Promise((resolve, reject) => {
      // UK = typeof MozWebSocket == 'undefined' ? new WebSocket(this.url, 'usbkey-protocol') : new MozWebSocket(this.url, 'usbkey-protocol')
      UK = new WebSocket(this.url, 'usbkey-protocol')

      try {
        UK.addEventListener('open', () => {
          SoftKey6W.ResetOrder(UK) //
        })
        UK.addEventListener('message', (Message) => {
          // UK.onmessage = function got_packet(Message) {
          const UK_Data = JSON.parse(Message.data)
          let return_value
          if (UK_Data.type !== 'Process') return //
          switch (UK_Data.order) {
            case 0: {
              SoftKey6W._StarGenKeyPair(UK, KeyPath)

              break
            } //
            case 1: {
              SoftKey6W.LastError = UK_Data.LastError
              if (SoftKey6W.LastError !== 0) {
                UK.close()
                resolve(KeyPairInfo)
                return
              }
              SoftKey6W._GenPriKey(UK)

              break
            }
            case 2: {
              SoftKey6W.LastError = UK_Data.LastError
              if (SoftKey6W.LastError !== 0) {
                UK.close()
                resolve(KeyPairInfo)
                return
              }
              KeyPairInfo.GenPriKey = UK_Data.return_value
              SoftKey6W._GenPubKeyX(UK)

              break
            }
            case 3: {
              SoftKey6W.LastError = UK_Data.LastError
              if (SoftKey6W.LastError !== 0) {
                UK.close()
                resolve(KeyPairInfo)
                return
              }
              KeyPairInfo.GenPubKeyX = UK_Data.return_value
              SoftKey6W._GenPubKeyY(UK)

              break
            }
            case 4: {
              SoftKey6W.LastError = UK_Data.LastError
              if (SoftKey6W.LastError !== 0) {
                UK.close()
                resolve(KeyPairInfo)
                return
              }
              KeyPairInfo.GenPubKeyY = UK_Data.return_value

              UK.close()
              resolve(KeyPairInfo)

              break
            }
          }
        })
        UK.addEventListener('close', () => {})
        UK.addEventListener('error', (event) => {
          this.$message.error('未能连接服务程序，请确定服务程序是否安装。')
        })
      } catch (error) {
        this.$message.error(`${error.name}: ${error.message}`)
        resolve(false)
      }
    })
  }

  Set_SM2_KeyPair = function (PriKey, PubKeyX, PubKeyY, SM2_UserName, KeyPath) {
    return this.SendCmdAndWait(true, this._Set_SM2_KeyPair, PriKey, PubKeyX, PubKeyY, SM2_UserName, KeyPath)
  }

  Get_SM2_PubKey = function (KeyPath) {
    return this.SendCmdAndWait(false, this._Get_SM2_PubKey, KeyPath)
  }

  GetPubKeyX = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetPubKeyX, KeyPath)
  }

  GetPubKeyY = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetPubKeyY, KeyPath)
  }

  GetSm2UserName = function (KeyPath) {
    return this.SendCmdAndWait(false, this._GetSm2UserName, KeyPath)
  }

  SM2_EncBuf = function (InBuf, inlen, KeyPath) {
    return this.SendCmdAndWait(true, this._SM2_EncBuf, InBuf, inlen, KeyPath)
  }

  SM2_DecBuf = function (InBuf, inlen, pin, KeyPath) {
    return this.SendCmdAndWait(true, this._SM2_DecBuf, InBuf, inlen, pin, KeyPath)
  }

  SM2_EncString = function (InString, KeyPath) {
    return this.SendCmdAndWait(false, this._SM2_EncString, InString, KeyPath)
  }

  SM2_DecString = function (InString, pin, KeyPath) {
    return this.SendCmdAndWait(false, this._SM2_DecString, InString, pin, KeyPath)
  }

  YtSetPin = function (old_pin, new_pin, KeyPath) {
    return this.SendCmdAndWait(true, this._YtSetPin, old_pin, new_pin, KeyPath)
  }

  YtSign = function (message, pin, KeyPath) {
    return this.SendCmdAndWait(false, this._YtSign, message, pin, KeyPath)
  }

  YtSign_2 = function (message, pin, KeyPath) {
    return this.SendCmdAndWait(false, this._YtSign_2, message, pin, KeyPath)
  }

  MacAddr = function () {
    return this.SendCmdAndWait(false, this._MacAddr)
  }

  ComputerName = function () {
    return this.SendCmdAndWait(false, this._ComputerName)
  }

  SubCal = function (Fun, Inbuf, KeyPath) {
    let UK
    const outb = new Uint8Array(8)
    return new Promise((resolve, reject) => {
      // UK = typeof MozWebSocket == 'undefined' ? new WebSocket(this.url, 'usbkey-protocol') : new MozWebSocket(this.url, 'usbkey-protocol')
      UK = new WebSocket(this.url, 'usbkey-protocol')

      try {
        let index = 0
        let order
        let bIsEnc = true
        UK.addEventListener('open', () => {
          SoftKey6W.ResetOrder(UK) //
        })
        UK.addEventListener('message', (Message) => {
          // UK.onmessage = function got_packet(Message) {
          const UK_Data = JSON.parse(Message.data)
          let return_value
          if (UK_Data.type !== 'Process') return //
          if (bIsEnc) {
            if (UK_Data.order < 8) {
              SoftKey6W._SetEncBuf(UK, Inbuf[UK_Data.order], UK_Data.order)
              return
            } else {
              order = UK_Data.order - 8
            }

            switch (order) {
              case 0: {
                SoftKey6W.LastError = UK_Data.LastError
                if (SoftKey6W.LastError !== 0) {
                  UK.close()
                  resolve(outb)
                  return
                }
                Fun(UK, KeyPath)

                break
              } //
              case 1: {
                SoftKey6W.LastError = UK_Data.LastError
                return_value = UK_Data.return_value
                if (SoftKey6W.LastError !== 0) {
                  UK.close()
                  resolve(outb)
                  return
                }
                bIsEnc = false
                index = 0
                SoftKey6W.ResetOrder(UK)

                break
              }
            }
          } else {
            SoftKey6W.LastError = UK_Data.LastError
            if (SoftKey6W.LastError !== 0) {
              UK.close()
              resolve(outb)
              return
            }
            if (UK_Data.order < 8) {
              if (UK_Data.order > 0) {
                outb[index - 1] = UK_Data.return_value
              }
              SoftKey6W._GetEncBuf(UK, index) //
              index++
            } else {
              outb[index - 1] = UK_Data.return_value
              UK.close()
              resolve(outb)
            }
          }
        })
        UK.addEventListener('close', () => {})
        UK.addEventListener('error', (event) => {
          this.$message.error('未能连接服务程序，请确定服务程序是否安装。')
        })
      } catch (error) {
        this.$message.error(`${error.name}: ${error.message}`)
        resolve(false)
      }
    })
  }
}
