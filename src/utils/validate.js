/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(string_) {
  const valid_map = ['admin', 'editor']
  return valid_map.includes(string_.trim())
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg =
    /^(https?|ftp):\/\/([\d.A-Za-z-]+(:[\d$%&.A-Za-z-]+)*@)*((25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d?)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}|([\dA-Za-z-]+\.)*[\dA-Za-z-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[A-Za-z]{2}))(:\d+)*(\/($|[\w#$%&'+,.=?\\~-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(string_) {
  const reg = /^[a-z]+$/
  return reg.test(string_)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(string_) {
  const reg = /^[A-Z]+$/
  return reg.test(string_)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(string_) {
  const reg = /^[A-Za-z]+$/
  return reg.test(string_)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg = /^(([^\s"(),.:;<>@[\\\]]+(\.[^\s"(),.:;<>@[\\\]]+)*)|(".+"))@((\[(?:\d{1,3}\.){3}\d{1,3}])|(([\dA-Za-z-]+\.)+[A-Za-z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(string_) {
  if (typeof string_ === 'string' || string_ instanceof String) return true

  return false
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(argument) {
  if (Array.isArray === undefined) return Object.prototype.toString.call(argument) === '[object Array]'

  return Array.isArray(argument)
}
