import store from '@/store'

// 缓存对象，避免重复计算
const permissionCache = new Map()
const roleCache = new Map()

// 缓存时间（5 分钟）
const CACHE_TTL = 5 * 60 * 1000

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now()
  for (const [key, value] of permissionCache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      permissionCache.delete(key)
    }
  }
  for (const [key, value] of roleCache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      roleCache.delete(key)
    }
  }
}

// 获取用户权限（带缓存）
function getUserPermissions() {
  const cacheKey = 'user_permissions'
  const now = Date.now()

  if (permissionCache.has(cacheKey)) {
    const cached = permissionCache.get(cacheKey)
    if (now - cached.timestamp < CACHE_TTL) {
      return cached.data
    }
  }

  const permissions = store.getters.permissions || []
  permissionCache.set(cacheKey, { data: permissions, timestamp: now })

  // 定期清理缓存
  if (Math.random() < 0.1) {
    cleanExpiredCache()
  }

  return permissions
}

// 获取用户角色（带缓存）
function getUserRoles() {
  const cacheKey = 'user_roles'
  const now = Date.now()

  if (roleCache.has(cacheKey)) {
    const cached = roleCache.get(cacheKey)
    if (now - cached.timestamp < CACHE_TTL) {
      return cached.data
    }
  }

  const roles = store.getters.roles || []
  roleCache.set(cacheKey, { data: roles, timestamp: now })

  // 定期清理缓存
  if (Math.random() < 0.1) {
    cleanExpiredCache()
  }

  return roles
}

// 清除权限缓存
function clearPermissionCache() {
  permissionCache.clear()
}

// 清除角色缓存
function clearRoleCache() {
  roleCache.clear()
}

// 清除所有缓存
function clearAllCache() {
  permissionCache.clear()
  roleCache.clear()
}

function checkPermission(permission) {
  if (!permission || permission.length === 0) {
    return false
  }

  const allPermission = '*:*:*'
  const userPermissions = getUserPermissions()

  return userPermissions.includes(allPermission) || userPermissions.includes(permission)
}

function checkRole(role) {
  if (!role || role.length === 0) {
    return false
  }

  const superAdminRole = 'ADMIN'
  const userRoles = getUserRoles()

  return userRoles.includes(superAdminRole) || userRoles.includes(role)
}

export const appAuth = {
  // 验证用户是否具备某权限
  hasPermission(permission) {
    try {
      return checkPermission(permission)
    } catch (error) {
      console.error('Permission check failed:', error)
      return false
    }
  },

  // 验证用户是否含有指定权限，只需包含其中一个
  hasAnyPermission(permissions) {
    try {
      if (!Array.isArray(permissions) || permissions.length === 0) {
        return false
      }
      return permissions.some((item) => checkPermission(item))
    } catch (error) {
      console.error('Any permission check failed:', error)
      return false
    }
  },

  // 验证用户是否含有指定权限，必须全部拥有
  hasAllPermissions(permissions) {
    try {
      if (!Array.isArray(permissions) || permissions.length === 0) {
        return false
      }
      return permissions.every((item) => checkPermission(item))
    } catch (error) {
      console.error('All permissions check failed:', error)
      return false
    }
  },

  // 验证用户是否具备某角色
  hasRole(role) {
    try {
      return checkRole(role)
    } catch (error) {
      console.error('Role check failed:', error)
      return false
    }
  },

  // 验证用户是否含有指定角色，只需包含其中一个
  hasAnyRole(roles) {
    try {
      if (!Array.isArray(roles) || roles.length === 0) {
        return false
      }
      return roles.some((item) => checkRole(item))
    } catch (error) {
      console.error('Any role check failed:', error)
      return false
    }
  },

  // 验证用户是否含有指定角色，必须全部拥有
  hasAllRoles(roles) {
    try {
      if (!Array.isArray(roles) || roles.length === 0) {
        return false
      }
      return roles.every((item) => checkRole(item))
    } catch (error) {
      console.error('All roles check failed:', error)
      return false
    }
  },

  // 获取用户所有权限
  getPermissions() {
    try {
      return getUserPermissions()
    } catch (error) {
      console.error('Failed to get permissions:', error)
      return []
    }
  },

  // 获取用户所有角色
  getRoles() {
    try {
      return getUserRoles()
    } catch (error) {
      console.error('Failed to get roles:', error)
      return []
    }
  },

  // 清除权限缓存
  clearPermissionCache,

  // 清除角色缓存
  clearRoleCache,

  // 清除所有缓存
  clearAllCache,

  // 检查是否为超级管理员
  isSuperAdmin() {
    try {
      const userRoles = getUserRoles()
      return userRoles.includes('ADMIN')
    } catch (error) {
      console.error('Super admin check failed:', error)
      return false
    }
  },
}
