/**
 * 将数字转换为十六进制字符串
 * @param {number} n - 要转换的数字
 * @returns {string} 十六进制字符串
 */
export function toHex(n) {
  const digitArray = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f']
  let result = ''
  let start = true

  for (let index = 32; index > 0; ) {
    index -= 4
    const digit = (n >> index) & 0xf

    if (!start || digit !== 0) {
      start = false
      result += digitArray[digit]
    }
  }

  return result === '' ? '0' : result
}
