import store from '@/store'

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkPermission(value) {
  if (value && Array.isArray(value) && value.length > 0) {
    const permissions = store.getters && store.getters.permissions
    const permissionData = value
    const all_permission = '*:*:*'

    const hasPermission = permissions.some((permission) => {
      return all_permission === permission || permissionData.includes(permission)
    })

    if (!hasPermission) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkPermission="['system:user:add','system:user:edit']"`)
    return false
  }
}

/**
 * 角色权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkRole(value) {
  if (value && Array.isArray(value) && value.length > 0) {
    const roles = store.getters && store.getters.roles
    const permissionRoles = value
    const super_admin = 'ADMIN'

    const hasRole = roles.some((role) => {
      return super_admin === role || permissionRoles.includes(role)
    })

    if (!hasRole) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkRole="['ADMIN','COMMON']"`)
    return false
  }
}
