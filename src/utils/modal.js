import { Loading, Message, MessageBox, Notification } from 'element-ui'

// 使用 Map 来管理多个 loading 实例，避免内存泄漏
const loadingInstances = new Map()

// 生成唯一的 loading 实例 key
function generateLoadingKey() {
  return Date.now().toString(36) + Math.random().toString(36).slice(2, 9)
}

export const appModal = {
  // 消息提示
  msg(content) {
    Message.info(content)
  },
  // 错误消息
  msgError(content) {
    Message.error(content)
  },
  // 成功消息
  msgSuccess(content) {
    Message.success(content)
  },
  // 警告消息
  msgWarning(content) {
    Message.warning(content)
  },
  // 弹出提示
  alert(content) {
    MessageBox.alert(content, '系统提示')
  },
  // 错误提示
  alertError(content) {
    MessageBox.alert(content, '系统提示', { type: 'error' })
  },
  // 成功提示
  alertSuccess(content) {
    MessageBox.alert(content, '系统提示', { type: 'success' })
  },
  // 警告提示
  alertWarning(content) {
    MessageBox.alert(content, '系统提示', { type: 'warning' })
  },
  // 通知提示
  notify(content) {
    Notification.info(content)
  },
  // 错误通知
  notifyError(content) {
    Notification.error(content)
  },
  // 成功通知
  notifySuccess(content) {
    Notification.success(content)
  },
  // 警告通知
  notifyWarning(content) {
    Notification.warning(content)
  },
  // 确认窗体
  confirm(content, title = '系统提示', options = {}) {
    return MessageBox.confirm(content, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      ...options,
    })
  },
  // 提交内容
  prompt(content, title = '系统提示', options = {}) {
    return MessageBox.prompt(content, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      ...options,
    })
  },
  // 打开遮罩层
  loading(content = '加载中...', key) {
    const loadingKey = key || generateLoadingKey()

    // 如果已经存在相同 key 的 loading，先关闭
    if (loadingInstances.has(loadingKey)) {
      loadingInstances.get(loadingKey).close()
    }

    const instance = Loading.service({
      lock: true,
      text: content,
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })

    loadingInstances.set(loadingKey, instance)
    return loadingKey
  },
  // 关闭遮罩层
  closeLoading(key) {
    if (key) {
      // 关闭指定 key 的 loading
      if (loadingInstances.has(key)) {
        loadingInstances.get(key).close()
        loadingInstances.delete(key)
      }
    } else {
      // 关闭所有 loading
      for (const [key, instance] of loadingInstances) instance.close()
      loadingInstances.clear()
    }
  },
  // 获取当前活跃的 loading 数量
  getLoadingCount() {
    return loadingInstances.size
  },
}
