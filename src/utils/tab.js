import store from '@/store'
import router from '@/router'

// 错误处理函数
function handleError(error, operation) {
  console.error(`Tab operation failed: ${operation}`, error)
  // 可以在这里添加错误通知
  return Promise.reject(error)
}

// 安全的 store 操作
function safeStoreDispatch(action, payload) {
  try {
    return store.dispatch(action, payload)
  } catch (error) {
    return handleError(error, action)
  }
}

// 安全的路由操作
function safeRouterPush(route) {
  try {
    return router.push(route)
  } catch (error) {
    return handleError(error, 'router.push')
  }
}

function safeRouterReplace(route) {
  try {
    return router.replace(route)
  } catch (error) {
    return handleError(error, 'router.replace')
  }
}

// 刷新当前 tab 页签
function refreshPage(object) {
  try {
    const { path, query, matched } = router.currentRoute
    if (!object) {
      const matchedComponent = matched?.find((m) => m.components?.default?.name && !['Layout', 'ParentView'].includes(m.components.default.name))
      object = matchedComponent ? { name: matchedComponent.components.default.name, path, query } : undefined
    }

    if (!object) {
      console.warn('No valid component found for refreshPage')
      return
    }

    return safeStoreDispatch('tagsView/delCachedView', object).then(() => {
      return safeRouterReplace({
        path: `/redirect${path}`,
        query,
      })
    })
  } catch (error) {
    return handleError(error, 'refreshPage')
  }
}

// 关闭当前 tab 页签，打开新页签
function closeOpenPage(object) {
  try {
    return safeStoreDispatch('tagsView/delView', router.currentRoute).then(() => {
      if (object !== undefined) {
        return safeRouterPush(object)
      }
    })
  } catch (error) {
    return handleError(error, 'closeOpenPage')
  }
}

// 关闭指定 tab 页签
function closePage(object) {
  try {
    if (!object) {
      return safeStoreDispatch('tagsView/delView', router.currentRoute).then(({ visitedViews }) => {
        const nextPath = visitedViews.at(-1)?.fullPath || '/'
        return safeRouterPush(nextPath)
      })
    }
    return safeStoreDispatch('tagsView/delView', object)
  } catch (error) {
    return handleError(error, 'closePage')
  }
}

// 关闭所有 tab 页签
function closeAllPage() {
  try {
    return safeStoreDispatch('tagsView/delAllViews')
  } catch (error) {
    return handleError(error, 'closeAllPage')
  }
}

// 关闭左侧 tab 页签
function closeLeftPage(object) {
  try {
    const target = object || router.currentRoute
    return safeStoreDispatch('tagsView/delLeftTags', target)
  } catch (error) {
    return handleError(error, 'closeLeftPage')
  }
}

// 关闭右侧 tab 页签
function closeRightPage(object) {
  try {
    const target = object || router.currentRoute
    return safeStoreDispatch('tagsView/delRightTags', target)
  } catch (error) {
    return handleError(error, 'closeRightPage')
  }
}

// 关闭其他 tab 页签
function closeOtherPage(object) {
  try {
    const target = object || router.currentRoute
    return safeStoreDispatch('tagsView/delOthersViews', target)
  } catch (error) {
    return handleError(error, 'closeOtherPage')
  }
}

// 添加 tab 页签
function openPage(title, url, parameters = {}) {
  try {
    if (!title || !url) {
      console.warn('openPage requires title and url parameters')
      return Promise.reject(new Error('openPage requires title and url parameters'))
    }

    const routeObject = { path: url, meta: { title } }
    return safeStoreDispatch('tagsView/addView', routeObject).then(() => {
      return safeRouterPush({ path: url, query: parameters })
    })
  } catch (error) {
    return handleError(error, 'openPage')
  }
}

// 修改 tab 页签
function updatePage(object) {
  try {
    if (!object) {
      console.warn('updatePage requires an object parameter')
      return Promise.reject(new Error('updatePage requires an object parameter'))
    }

    return safeStoreDispatch('tagsView/updateVisitedView', object)
  } catch (error) {
    return handleError(error, 'updatePage')
  }
}

// 获取当前路由信息
function getCurrentRoute() {
  return router.currentRoute
}

// 获取所有访问过的视图
function getVisitedViews() {
  try {
    return store.getters.visitedViews || []
  } catch (error) {
    console.error('Failed to get visited views:', error)
    return []
  }
}

export const appTab = {
  refreshPage,
  closeOpenPage,
  closePage,
  closeAllPage,
  closeLeftPage,
  closeRightPage,
  closeOtherPage,
  openPage,
  updatePage,
  getCurrentRoute,
  getVisitedViews,
}
