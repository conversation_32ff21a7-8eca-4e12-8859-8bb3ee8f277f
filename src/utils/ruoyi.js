/**
 * 通用 js 方法封装处理
 * Copyright (c) 2019 ruoyi
 */

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return
  }
  // eslint-disable-next-line unicorn/prefer-default-parameters
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^\d+$/.test(time)) {
      time = Number.parseInt(time)
    } else if (typeof time === 'string') {
      time = time
        .replaceAll(/-/gm, '/')
        .replace('T', ' ')
        .replaceAll(/\.\d{3}/gm, '')
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObject = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_string = format.replaceAll(/{([adhimsy])+}/g, (result, key) => {
    let value = formatObject[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = `0${value}`
    }
    return value || 0
  })
  return time_string
}

// 回显数据字典
export function selectDictLabel(data, value) {
  if (value === undefined) {
    return ''
  }
  const actions = []
  // eslint-disable-next-line array-callback-return
  Object.keys(data).some((key) => {
    if (data[key].value === `${value}`) {
      actions.push(data[key].label)
      return true
    }
  })
  if (actions.length === 0) {
    actions.push(value)
  }
  return actions.join('')
}

// 回显数据字典（字符串数组）
export function selectDictLabels(data, value, separator) {
  if (value === undefined) {
    return ''
  }
  const actions = []
  const currentSeparator = undefined === separator ? ',' : separator
  const temporary = value.split(currentSeparator)
  Object.keys(value.split(currentSeparator)).some((value_) => {
    return Object.keys(data).some((key) => {
      if (data[key].value === `${temporary[value_]}`) {
        actions.push(data[key].label + currentSeparator)
      }

      return key
    })
  })
  return actions.join('').slice(0, Math.max(0, actions.join('').length - 1))
}

// 数据合并
export function mergeRecursive(source, target) {
  for (const p in target) {
    try {
      source[p] = target[p].constructor === Object ? mergeRecursive(source[p], target[p]) : target[p]
    } catch {
      source[p] = target[p]
    }
  }
  return source
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id 字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children',
  }

  const childrenListMap = {}
  const nodeIds = {}
  const tree = []

  for (const d of data) {
    const parentId = d[config.parentId]
    if (childrenListMap[parentId] === undefined) {
      childrenListMap[parentId] = []
    }
    nodeIds[d[config.id]] = d
    childrenListMap[parentId].push(d)
  }

  for (const d of data) {
    const parentId = d[config.parentId]
    if (nodeIds[parentId] === undefined) {
      tree.push(d)
    }
  }

  for (const t of tree) {
    adaptToChildrenList(t)
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== undefined) {
      o[config.childrenList] = childrenListMap[o[config.id]]
    }
    if (o[config.childrenList]) {
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c)
      }
    }
  }
  return tree
}
