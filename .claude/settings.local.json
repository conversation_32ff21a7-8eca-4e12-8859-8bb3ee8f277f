{"permissions": {"allow": ["Bash(pnpm run lint)", "Bash(pnpm run lint:fix:*)", "Bash(git checkout:*)", "Bash(pnpm lint)", "Bash(git restore:*)", "Bash(git add:*)", "Bash(pnpm lint:fix:*)", "Bash(node:*)", "Bash(pnpm run serve:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "Bash(pnpm:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(npm run)", "Read(//Users/<USER>/Documents/GitLab/project-templates/xinjian-template/xinjian-service-template/**)", "Read(//Users/<USER>/Documents/GitLab/project-templates/xinjian-template/**)", "WebFetch(domain:element.eleme.cn)"], "deny": [], "ask": []}}